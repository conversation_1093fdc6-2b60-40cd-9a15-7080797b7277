export const APP = {
  NAME: "template-react-client",
};

// @ts-expect-error: gotta figure out how to configure it to allow optional property searches
export const IS_CLIENT_HUB = window?.FQ?.isClientHub ?? false;

const buildNumber = document.querySelector("meta[name=floqast-build-number]") ?? "content";

export const CURRENT_BUILD_VERSION =
  process.env.NODE_ENV === "test" ? "test" : buildNumber || undefined;

export const ALLOWED_FILE_TYPES = [".csv", ".xlsx"];

/**
 * Route Components
 *
 * This approach to versioning uses constants (e.g., `v2`, `v3`) to manage system-wide updates and enable the addition of new versions easily.
 *
 * - To **add a new version**:
 *   1. Define the new version as an additional constant (e.g., `const v4 = 'v4';`).
 *   2. Use this constant wherever it is needed in the codebase.
 *
 * - To **perform a system-wide update**:
 *   1. Update the desired constant (e.g., update `v3` to point to `'v4'`).
 *   2. Rename the constant to reflect the new version (e.g., rename `V3` to `V4`).
 *  	The change will automatically propagate throughout the system wherever the constant is used.
 *
 */
export const V2 = "v2";
export const V3 = "v3";
export const BUILDER = "builder";
export const RUNNER = "runner";
export const AGENTS = "agents";
export const STEPS = "steps";
export const EXAMPLES = "examples";
export const RUNS = "runs";
export const INPUTS = "inputs";

// Product Name
export const AGENT = "Agent";
export const CONNECTIONS = "Connections";

// Route Paths
export const INPUTS_PATH = `${AGENTS}/:workflowId/${INPUTS}`;
