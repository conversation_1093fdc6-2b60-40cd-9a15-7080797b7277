import { fetchStuff, getLambdaEndpoint } from "@/utils/request";
import * as apiSdk from "@floqastinc/transform-v0";

// @ts-ignore
const params: [apiSdk.FetchLike, apiSdk.FloBuilderOptions | undefined] = [
  typeof window !== "undefined" && window.fetch
    ? fetchStuff
    : () => Promise.reject(new Error("Fetch not supported")),
  {
    root: `${getLambdaEndpoint("transform_api", true)}/v0`,
  },
];

const v0 = {
  apiKeys: new apiSdk.HttpApiKeyService(...params),
  principals: new apiSdk.HttpPrincipalService(...params),
  presignedUrls: new apiSdk.HttpPresignedUrlService(...params),
  teams: new apiSdk.HttpTeamService(...params),
  users: new apiSdk.HttpUserService(...params),
};

export class ApiError extends Error {
  constructor(readonly errors: apiSdk.Error[]) {
    super(errors.map((e) => e.detail).join(", "));

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ApiError);
    }
  }
}

export default v0;
