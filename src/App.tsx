import { useEffect } from "react";
import { Theme } from "@floqastinc/flow-ui_core";
import { useUpdateOnIntlSettingsChange } from "@floqastinc/fq-intl-react";
import { createBrowserRouter, RouterProvider, Navigate } from "react-router-dom";
import { QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import "rowsncolumns/packages/spreadsheet/dist/spreadsheet.min.css";
import v3BuilderRoutes from "./apps/BuilderV3/BuilderApp";
import { routes as transformRoutes } from "./apps/Transform/app/routes";
import { routes as adminRoutes } from "./apps/Admin/AdminApp";
import { BUILDER, RUNNER, V2, V3 } from "./constants";
import { PrincipalProvider } from "./components/PrincipalProvider";
import { AuthorizationWrapper } from "./components/AuthorizationWrapper";
import { fetchStuff, getLambdaEndpoint } from "./utils/request";
import { queryClient } from "@/components";
import "./App.css";
import { TransformProvider as V3TransformProvider } from "@v3/context";
import { TransformProvider as V0TransformProvider } from "@v0/context";

const enableReactQueryDevTools = localStorage.getItem("enable-react-query-devtools") === "true";

const router = createBrowserRouter(
  [
    {
      path: "/",
      element: <Navigate to={`/${V3}`} replace />,
    },
    {
      path: `/${BUILDER}`,
      element: <Navigate to={`/${V3}`} replace />,
    },
    {
      path: `/${BUILDER}/${V2}`,
      element: <Navigate to={`/${V2}`} replace />,
    },
    {
      path: `/${BUILDER}/${V3}`,
      element: <Navigate to={`/${V3}`} replace />,
    },
    {
      // TODO: Consider removing the V3 here, we won't
      // be doing major rewrites like we've done in the past, or
      // at least should consider better versioning at the point in time
      path: `/${RUNNER}`,
      element: <Navigate to={`/${RUNNER}/${V3}`} replace />,
    },
    ...v3BuilderRoutes,
    ...adminRoutes,
    ...transformRoutes,
  ],
  {
    basename: "/transform",
  },
);

type AppPropTypes = {
  initialError?: number;
  hasInitted?: boolean;
  tlcModules?: Record<string, boolean>;
};
export function App(props: AppPropTypes) {
  const isStandalone = process.env.REACT_APP_RUNTIME_MODE === "standalone";

  useUpdateOnIntlSettingsChange();

  useEffect(() => {
    // When injected, the client-hub applies the theme, otherwise apply it here.
    if (isStandalone) {
      Theme.apply();
      console.log("Running on standalone mode");
    } else {
      Theme.apply();
      console.log("Running on injected mode");
    }
  }, [isStandalone]);

  // TODO: TEMP while flo-ui and other packages get their defaultProps
  //  errors sorted out
  useEffect(() => {
    const originalConsoleError = console.error;

    console.error = (...args: any[]) => {
      if (typeof args[0] === "string" && /defaultProps/.test(args[0])) {
        return;
      }

      originalConsoleError(...args);
    };

    return () => {
      console.error = originalConsoleError;
    };
  }, []);

  return (
    <div
      className="main"
      id="template-app-container"
      style={
        isStandalone
          ? {
              // 16px of total padding top and bottom
              height: "calc(100vh - 16px)",
              width: "100vw",
            }
          : {
              position: "relative",
              display: "flex",
              flexGrow: 1,
              // 60px for the header
              height: "calc(100vh - 60px)",
              // 56px for the sidebar
              width: "calc(100vw - 56px)",
            }
      }
    >
      <QueryClientProvider client={queryClient}>
        <V3TransformProvider
          root={`${getLambdaEndpoint("transform_api", true)}/v3`}
          fetch={fetchStuff}
        >
          <V0TransformProvider
            root={`${getLambdaEndpoint("transform_api", true)}/v0`}
            fetch={fetchStuff}
          >
            <PrincipalProvider>
              <AuthorizationWrapper tlcModules={props.tlcModules}>
                <RouterProvider router={router} />
              </AuthorizationWrapper>
            </PrincipalProvider>
          </V0TransformProvider>
        </V3TransformProvider>
        {enableReactQueryDevTools ? <ReactQueryDevtools initialIsOpen={false} /> : null}
      </QueryClientProvider>
    </div>
  );
}
