import React, { Suspense } from "react";
import { Outlet, RouteObject } from "react-router-dom";
// eslint-disable-next-line
import styled from "styled-components";
import { initializeForFrontend } from "@floqastinc/fq-intl";
import { ProtectedComponent } from "@floqastinc/auth-module-client";
import { TeamsPage } from "./routes/teams/TeamsPage";
import { TeamPage } from "./routes/teams/TeamPage";
import { UserPage } from "./routes/users/UserPage";
import { ScriptsPage } from "./routes/scripts/ScriptsPage";
// import { NavBar } from './app-components/NavBar/NavBar';
import { DashboardPage } from "./routes/dashboard/DashboardPage";
import { ErrorBoundary, PageLayout } from "@/components";
import { Loading } from "@/components/Loading";
import { USER_ACTION_KEYS } from "@/authorization";

if (initializeForFrontend && typeof initializeForFrontend === "function") {
  initializeForFrontend();
}

const RootLayout = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100vw;
`;

const PageContents = styled.div`
  margin: 0 auto;
  padding: 3rem 1rem;
  max-width: 960px;
  width: 100%;
`;

const PageGuts = styled.div`
  width: 100%;
`;

// eslint-disable-next-line
const AdminApp: React.FC = () => {
  return (
    <PageLayout>
      <ProtectedComponent
        actionKey={USER_ACTION_KEYS.TRANSFORM_PRINCIPAL_FULL}
        fallback={<h1>Forbidden</h1>}
      >
        <RootLayout>
          {/* <NavBar /> */}
          <PageContents>
            <PageGuts>
              <Outlet />
            </PageGuts>
          </PageContents>
        </RootLayout>
      </ProtectedComponent>
    </PageLayout>
  );
};

export const routes: RouteObject[] = [
  {
    path: "/admin",
    element: <AdminApp />,
    errorElement: <ErrorBoundary />,
    children: [
      {
        index: true,
        element: (
          <Suspense fallback={<Loading />}>
            <TeamsPage />
          </Suspense>
        ),
        errorElement: <ErrorBoundary />,
      },
      {
        path: "teams",
        element: (
          <Suspense fallback={<Loading />}>
            <TeamsPage />
          </Suspense>
        ),
        errorElement: <ErrorBoundary />,
      },
      {
        path: "teams/:teamId",
        element: (
          <Suspense fallback={<Loading />}>
            <TeamPage />
          </Suspense>
        ),
        errorElement: <ErrorBoundary />,
      },
      {
        path: "users/:userId",
        element: (
          <Suspense fallback={<Loading />}>
            <UserPage />
          </Suspense>
        ),
        errorElement: <ErrorBoundary />,
      },
      {
        path: "dashboard",
        element: (
          <Suspense fallback={<Loading />}>
            <DashboardPage />
          </Suspense>
        ),
        errorElement: <ErrorBoundary />,
      },
      {
        path: "workflows/:workflowId/scripts",
        element: (
          <Suspense fallback={<Loading />}>
            <ScriptsPage />
          </Suspense>
        ),
        errorElement: <ErrorBoundary />,
      },
    ],
  },
];
