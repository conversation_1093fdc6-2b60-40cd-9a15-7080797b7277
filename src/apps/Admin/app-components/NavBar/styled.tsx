import styled from "styled-components";

export const Nav = styled.nav`
  height: 68px;
  background-color: var(--flo-base-color-neutral);
  display: grid;
  gap: 32px;
  grid-template-areas: "left right";
  justify-content: space-between;
  align-items: center;
  padding: 8px 24px 8px 32px;
  box-sizing: border-box;
`;

export const LeftNav = styled.div`
  display: grid;
  grid-auto-flow: column;
  gap: 48px;
  place-items: center;
  grid-area: left;
`;

export const IconButton = styled.button`
  cursor: pointer;
  min-width: 48px;
  min-height: 48px;
  border-radius: 6px;
  border: 0;
  background-color: transparent;
`;

export const ExternalLinksAndSettings = styled.div`
  display: grid;
  grid-auto-flow: column;
  gap: 8px;
  align-items: center;
  grid-area: right;
`;
