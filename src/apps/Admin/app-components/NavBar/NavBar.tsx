import { useState } from "react";
import { <PERSON>b<PERSON><PERSON>, Tab, FQLogo } from "@floqastinc/flow-ui_core";
import MagicAiStar from "@floqastinc/flow-ui_icons/fq/MagicAIStar";
import GroupsOutlined from "@floqastinc/flow-ui_icons/material/GroupsOutlined";
import ChartDataOutlined from "@floqastinc/flow-ui_icons/material/ChartDataOutlined";
import * as Styled from "./styled";
import { t } from "@/utils/i18n";
import { Link } from "@/components/Link";

// const UserAvatarDropdown = () => {
//   const [isOpen, setIsOpen] = useState(false);
//   return (
//     <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
//       <DropdownMenu.Trigger>
//         <Avatar size="lg" />
//       </DropdownMenu.Trigger>
//       <DropdownMenu.Content>
//         <DropdownMenu.ItemHeader>
//           <DropdownMenu.ItemTitle>Flo Qast</DropdownMenu.ItemTitle>
//           <DropdownMenu.ItemSubtitle>@flo.qast</DropdownMenu.ItemSubtitle>
//         </DropdownMenu.ItemHeader>
//         <DropdownMenu.Item>
//           <DropdownMenu.ItemText>
//             Organization Settings
//           </DropdownMenu.ItemText>
//         </DropdownMenu.Item>
//       </DropdownMenu.Content>
//     </DropdownMenu>
//   );
// };

export const NavBar = () => {
  const [activeTab, setActiveTab] = useState(window.location.pathname.split("/")[2] || "teams");
  return (
    <Styled.Nav>
      <Styled.LeftNav>
        <Link to="/admin">
          <FQLogo />
        </Link>
        <TabGroup
          defaultValue="teams"
          styleOverrides={{ list: { flexWrap: "nowrap" } }}
          onValueChange={setActiveTab}
          value={activeTab}
        >
          <Tab
            tabId="teams"
            title={
              <Link to="/admin/teams">
                {t("components.NavBar.teams")}
                <GroupsOutlined size={18} />
              </Link>
            }
          />
          {/* <Tab
            tabId="users"
            title={
              <Link to="/admin/users">
                Users
                <PersonOutlined size={18} />
              </Link>
            }
          /> */}
          <Tab
            tabId="dashboard"
            title={
              <Link to="/admin/dashboard">
                {t("components.NavBar.dashboard")}
                <ChartDataOutlined size={18} />
              </Link>
            }
          />
          <Tab
            tabId="builder"
            title={
              <Link to="/">
                {t("components.NavBar.backToApp")}
                <MagicAiStar size={18} />
              </Link>
            }
          />
        </TabGroup>
      </Styled.LeftNav>
      <Styled.ExternalLinksAndSettings>
        {/* <Styled.IconButton type="icon-button">
          <NavHelpOutline size="25" color="rgba(29, 36, 51, 0.8)" />
        </Styled.IconButton>
        <UserAvatarDropdown /> */}
      </Styled.ExternalLinksAndSettings>
    </Styled.Nav>
  );
};
