import React, { useCallback } from "react";
import {
  Button,
  Dialog,
  EmptyState,
  Flex,
  Heading,
  IconButton,
  Table,
  THead,
  TR,
  TH,
  TD,
  TBody,
  useToast,
} from "@floqastinc/flow-ui_core";
import KeyOutlined from "@floqastinc/flow-ui_icons/material/KeyOutlined";
import DeleteForever from "@floqastinc/flow-ui_icons/material/DeleteForeverOutlined";
import { ApiKey, UserRole } from "@floqastinc/transform-v0";
import { t } from "@/utils/i18n";
import v0 from "@/services/v0";
import { Time } from "@/components/Time";

export const ApiKeysTabContents: React.FC<{
  teamId: string;
  apiKeys: ApiKey[];
  onInvalidateQuery: () => Promise<void>;
}> = ({ teamId, apiKeys, onInvalidateQuery }) => {
  const { showToast, Toast } = useToast();
  const [disableCreate, setDisableCreate] = React.useState(false);
  const handleCreate = useCallback(
    async ({ role }: { role: UserRole }) => {
      setDisableCreate(true);
      const res = await v0.apiKeys.createApiKey({
        teamId,
        apiKey: { role },
      });
      setDisableCreate(false);

      if (res.errors.length) {
        console.error(res.errors);
        showToast(
          <Toast type="error">
            <Toast.Title>{t("components.ApiKeysTab.somethingWentWrong")}</Toast.Title>
            <Toast.Message>
              {t("components.ApiKeysTab.unableCreateAPI", {
                errorDetails: res.errors.map((e) => e.detail).join("; "),
              })}
            </Toast.Message>
          </Toast>,
          { position: "top-right" },
        );
      } else {
        setNewApiKey(res.data);
      }

      await onInvalidateQuery();

      console.log(res);
    },
    [onInvalidateQuery, showToast, teamId],
  );

  const [confirmingDeletion, setConfirmingDeletion] = React.useState(false);
  const [disableDelete, setDisableDelete] = React.useState(false);
  const handleDelete = useCallback(
    async ({ apiKeyId }: { apiKeyId: string }) => {
      setDisableDelete(true);
      const res = await v0.apiKeys.deleteApiKey({ teamId, apiKeyId });
      setDisableDelete(false);
      setConfirmingDeletion(false);

      if (res.errors.length) {
        console.error(res.errors);
        showToast(
          <Toast type="error">
            <Toast.Title>{t("components.ApiKeysTab.somethingWentWrong")}</Toast.Title>
            <Toast.Message>
              {t("components.ApiKeysTab.unableDeleteAPI", {
                errorDetails: res.errors.map((e) => e.detail).join("; "),
              })}
            </Toast.Message>
          </Toast>,
          { position: "top-right" },
        );
      } else {
        showToast(
          <Toast type="success">
            <Toast.Title>{t("components.ApiKeysTab.apiKeyDeleted")}</Toast.Title>
            <Toast.Message>
              {t("components.ApiKeysTab.secretAPIKey", { secretKey: res.data?.secretKey })}
            </Toast.Message>
          </Toast>,
          { position: "top-right" },
        );
      }

      await onInvalidateQuery();
    },
    [onInvalidateQuery, showToast, teamId],
  );

  const [newApiKey, setNewApiKey] = React.useState<ApiKey>();

  return (
    <>
      {apiKeys.length > 0 && (
        <Flex direction="column" gap={24} style={{ marginTop: "3rem" }}>
          <Heading variant="h2">{t("components.ApiKeysTab.apiKeys")}</Heading>
          <CreateForm disabled={disableCreate} handleCreate={handleCreate} />
          <Table fixed middleBorders>
            <THead>
              <TR>
                <TH width="10%">{t("components.ApiKeysTab.role")}</TH>
                <TH width="20%">{t("components.ApiKeysTab.secretKey")}</TH>
                <TH width="25%">{t("components.ApiKeysTab.created")}</TH>
                <TH width="25%">{t("components.ApiKeysTab.lastUsed")}</TH>
                <TH width="20%"></TH>
              </TR>
            </THead>
            <TBody>
              {apiKeys.map((apiKey) => (
                <TR key={apiKey.id} style={{ cursor: "pointer" }}>
                  <TD>{apiKey.role}</TD>
                  <TD>{apiKey.secretKey}</TD>
                  <TD>
                    <Time value={apiKey.createdAt} />
                  </TD>
                  <TD>
                    <Time value={apiKey.lastUsedAt} />
                  </TD>
                  <TD>
                    <Dialog
                      open={confirmingDeletion}
                      onOpenChange={setConfirmingDeletion}
                      type="danger"
                    >
                      <Dialog.Trigger color="danger">
                        <IconButton disabled={disableDelete}>
                          <DeleteForever />
                        </IconButton>
                      </Dialog.Trigger>
                      <Dialog.Header>{t("components.ApiKeysTab.deleteAPIKey")}</Dialog.Header>
                      <Dialog.Body>
                        <p>{t("components.ApiKeysTab.permanentlyDeleteAPIKey")}</p>
                      </Dialog.Body>
                      <Dialog.Footer>
                        <Dialog.FooterCancelBtn onClick={() => setConfirmingDeletion(false)}>
                          {t("components.ApiKeysTab.cancel")}
                        </Dialog.FooterCancelBtn>
                        <Dialog.FooterActionBtn
                          color="danger"
                          onClick={() => handleDelete({ apiKeyId: apiKey.id })}
                        >
                          {t("components.ApiKeysTab.delete")}
                        </Dialog.FooterActionBtn>
                      </Dialog.Footer>
                    </Dialog>
                  </TD>
                </TR>
              ))}
            </TBody>
          </Table>
        </Flex>
      )}

      {apiKeys.length === 0 && (
        <Flex align="center" direction="column" gap={24} style={{ marginTop: "4rem" }}>
          <Heading variant="h2">{t("components.ApiKeysTab.noAPIKey")}</Heading>
          <EmptyState />
          <Flex align="center" direction="column">
            <p>{t("components.ApiKeysTab.startAPIKey")}</p>
            <p>{t("components.ApiKeysTab.APImakeRequests")}</p>
          </Flex>
          <CreateForm disabled={disableCreate} handleCreate={handleCreate} />
        </Flex>
      )}
      <Dialog
        open={!!newApiKey}
        onOpenChange={(x: boolean) => {
          if (!x) {
            setNewApiKey(undefined);
          }
        }}
      >
        <Dialog.Header>{t("components.ApiKeysTab.APIKeyCreated")}</Dialog.Header>
        <Dialog.Body>
          <Flex direction="column" gap={16}>
            <p>
              {t("components.ApiKeysTab.createdNewAPIKey", { secretKey: newApiKey?.secretKey })}
            </p>
            <p>{t("components.ApiKeysTab.pleaseCopySecret")}</p>
          </Flex>
        </Dialog.Body>
        <Dialog.Footer>
          <Dialog.FooterActionBtn onClick={() => setNewApiKey(undefined)}>
            {t("components.ApiKeysTab.close")}
          </Dialog.FooterActionBtn>
        </Dialog.Footer>
      </Dialog>
    </>
  );
};

const CreateForm: React.FC<{
  disabled?: boolean;
  handleCreate(apiKey: { role: UserRole }): void;
}> = ({ disabled, handleCreate: handleInvite }) => {
  const [role, setRole] = React.useState<UserRole>("USER");

  const handleChangeRole = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setRole(e.target.value as UserRole);
  }, []);

  const handleClick = useCallback(() => {
    handleInvite({ role });
    setRole("USER");
  }, [role, handleInvite]);

  const handleSubmit = useCallback(
    (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      handleInvite({ role });
      setRole("USER");
    },
    [role, handleInvite],
  );

  return (
    <form onSubmit={handleSubmit} style={{ maxWidth: "600px", width: "100%" }}>
      <Flex align="center" gap={16} style={{ width: "100%" }}>
        <select disabled={disabled} name="userRole" id="userRole" onChange={handleChangeRole}>
          {Object.entries(options).map(([key, value]) => (
            <option key={key} value={key} selected={role === key}>
              {value}
            </option>
          ))}
        </select>
        <Button
          disabled={disabled}
          onClick={handleClick}
          type="submit"
          style={{ paddingLeft: "1.5rem", paddingRight: "1.5rem" }}
        >
          <KeyOutlined style={{ flexShrink: 0 }} color="currentColor" />
          <span style={{ flexShrink: 0 }}>{t("components.ApiKeysTab.createKey")}</span>
        </Button>
      </Flex>
    </form>
  );
};

const options: Record<UserRole, string> = {
  ADMIN: "ADMIN",
  OMNI: "OMNI",
  SUPER: "SUPER",
  USER: "USER",
};
