import React, { useCallback, useState } from "react";
import { useQueryClient, useSuspenseInfiniteQuery, useSuspenseQuery } from "@tanstack/react-query";
import { Flex, Heading, Link as FqLink, TabGroup, Tab } from "@floqastinc/flow-ui_core";
import { useParams } from "react-router-dom";
import { WorkflowsTabContents } from "./WorkflowsTab";
import { t } from "@/utils/i18n";
import { queryKeys } from "@BuilderV3/api/query-keys";
import { useDebounce } from "@/hooks/useDebounce";
import { getWorkflowsQuery } from "@BuilderV3/api/workflows";
import v0, { ApiError } from "@/services/v0";

export const TeamPage: React.FC = () => {
  const { teamId } = useParams();

  if (!teamId) return null;

  return <WrappedTeamPage teamId={teamId} />;
};

const WrappedTeamPage: React.FC<{ teamId: string }> = ({ teamId }) => {
  const queryClient = useQueryClient();
  const { data: teams } = useSuspenseQuery({
    queryKey: ["teams", teamId],
    queryFn: async () => {
      const res = await v0.teams.getTeams({ first: 100 }); // TODO: pagination
      if (res.errors.length) throw new ApiError(res.errors);
      return res.data || [];
    },
  });
  const team = teams?.find((t) => t.id === teamId);

  // Workflows
  const [search, setSearch] = useState<string>();
  const debounce = useDebounce();
  const doSearch = useCallback((value: string) => {
    setSearch(value);
  }, []);

  const handleSearch = useCallback(
    debounce((search: string) => {
      doSearch(search);
    }, 1200),
    [doSearch],
  );

  const { data, fetchNextPage } = useSuspenseInfiniteQuery(
    getWorkflowsQuery({ search, first: 10 }),
  );

  const { workflows, hasNextPage } = data;

  const invalidateWorkflowsQuery = () =>
    queryClient.invalidateQueries({
      queryKey: queryKeys.workflows.all({ search, first: 10 }),
      refetchType: "all",
    });

  if (!team)
    return (
      <div>
        <Heading>{t("components.TeamPage.team")}</Heading>
        <Heading variant="h2">{t("components.TeamPage.notFound")}</Heading>
      </div>
    );

  return (
    <Flex direction="column" gap={16}>
      <Heading>{team.name}</Heading>
      <p>
        {t("components.TeamPage.additionalFunctionality", {
          apiLink: (
            <FqLink href="/api/v0" target="_blank" style={{ display: "inline" }}>
              {t("components.TeamPage.APISwaggerUI")}
            </FqLink>
          ),
        })}
      </p>
      <TabGroup defaultValue="1">
        <Tab tabId="1" title="Workflows">
          <WorkflowsTabContents
            teams={teams}
            workflows={workflows}
            hasNextPage={hasNextPage}
            fetchNextPage={fetchNextPage}
            onInvalidateQuery={invalidateWorkflowsQuery}
            handleSearch={handleSearch}
          />
        </Tab>
      </TabGroup>
    </Flex>
  );
};
