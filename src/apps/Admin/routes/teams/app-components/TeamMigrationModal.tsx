import React, { useState } from "react";
import { Modal, Input } from "@floqastinc/flow-ui_core";
import { Workflow } from "@floqastinc/transform-v3";
import { Team } from "@floqastinc/transform-v0";
import TeamDropdownSelect from "./TeamDropdownSelect";
import { t } from "@/utils/i18n";

interface TeamMigrationModalProps {
  teams: Team[];
  workflow: Workflow;
  onInvalidateQuery: () => Promise<void>;
  children: React.ReactNode;
  handleMigration: (
    workflowId: string,
    teamId: string,
    entityId: string,
    createdBy: string,
  ) => void;
}

const TeamMigrationModal: React.FC<TeamMigrationModalProps> = ({
  teams,
  workflow,
  handleMigration,
  children,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState<any>(null);
  const [entityId, setEntityId] = useState("");
  const [createdBy, setCreatedBy] = useState("");

  const handleDropDownOnChange = (teamId: string) => {
    const team = teams.find((t) => t.id === teamId);
    setSelectedValue(team || null);
  };

  return (
    <Modal
      hideCloseButton
      onOpenChange={(value: boolean) => {
        setIsOpen(value);
      }}
      open={isOpen}
      onPointerDownOutside={() => {
        setIsOpen(false);
      }}
      onEscapeKeyDown={() => {
        setIsOpen(false);
      }}
    >
      <Modal.Trigger>{children}</Modal.Trigger>
      <Modal.Header>{t("components.TeamMigrationModal.selectTeamMigrate")}</Modal.Header>
      <Modal.Body>
        <TeamDropdownSelect
          teams={teams}
          selectedValue={selectedValue}
          handleDropDownOnChange={handleDropDownOnChange}
        />
        <div style={{ marginBottom: "1rem" }} />
        <Input
          aria-label={t("components.TeamMigrationModal.newEntityID")}
          defaultValue={entityId}
          onChange={setEntityId}
          value={entityId}
          placeholder="New Entity ID"
        />
        <div style={{ marginBottom: "1rem" }} />
        <Input
          aria-label={t("components.TeamMigrationModal.newCreatedBy")}
          defaultValue={createdBy}
          onChange={setCreatedBy}
          value={createdBy}
          placeholder="New Created By"
        />
      </Modal.Body>
      <Modal.Footer>
        <Modal.FooterCancelBtn
          onClick={() => {
            setIsOpen(false);
            setSelectedValue(null);
          }}
        >
          {t("components.TeamMigrationModal.cancel")}
        </Modal.FooterCancelBtn>
        <Modal.FooterActionBtn
          onClick={() => {
            if (selectedValue) handleMigration(workflow.id, selectedValue.id, entityId, createdBy);
            setIsOpen(false);
            setSelectedValue(null);
          }}
        >
          {t("components.TeamMigrationModal.migrate")}
        </Modal.FooterActionBtn>
      </Modal.Footer>
    </Modal>
  );
};

export default TeamMigrationModal;
