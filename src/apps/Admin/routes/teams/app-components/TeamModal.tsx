import React, { useState } from "react";
import { Modal, Button, Input } from "@floqastinc/flow-ui_core";
import { t } from "@/utils/i18n";

// Define the shape of the props
interface TeamModalProps {
  initialData?: {
    name?: string;
    externalId?: string;
  };
  onSave: (name: string, externalId?: string) => Promise<void> | void;
  buttonText: string;
  modalTitle: string;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  hideButtonTrigger?: boolean;
  teamId?: string;
}

const TeamModal: React.FC<TeamModalProps> = ({
  initialData,
  onSave,
  buttonText,
  modalTitle,
  isOpen,
  setIsOpen,
  hideButtonTrigger,
}) => {
  const [name, setName] = useState(initialData?.name || "");
  const [externalId, setExternalId] = useState(initialData?.externalId || undefined);

  const handleSave = () => {
    onSave(name, externalId);
    setIsOpen(false);
  };

  return (
    <Modal onOpenChange={setIsOpen} open={isOpen}>
      <Modal.Trigger>
        {!hideButtonTrigger && <Button onClick={() => setIsOpen(true)}>{buttonText}</Button>}
      </Modal.Trigger>
      <Modal.Header>{modalTitle}</Modal.Header>
      <Modal.Body>
        <Input
          required
          label={t("components.TeamModal.teamName")}
          onChange={setName}
          value={name}
          placeholder="Team Name"
          autoFocus
        />
        <div style={{ marginBottom: "1rem" }} />
        <Input
          label={t("components.TeamModal.teamExternalID")}
          onChange={setExternalId}
          value={externalId}
          placeholder="Team External ID"
        />
      </Modal.Body>
      <Modal.Footer>
        <Modal.FooterCancelBtn
          onClick={() => setIsOpen(false)}
          onFocus={() => {}}
          onKeyDown={() => {}}
        >
          {t("components.TeamModal.cancel")}
        </Modal.FooterCancelBtn>
        <Modal.FooterActionBtn onClick={handleSave} onFocus={() => {}} onKeyDown={() => {}}>
          {t("components.TeamModal.save")}
        </Modal.FooterActionBtn>
      </Modal.Footer>
    </Modal>
  );
};

export default TeamModal;
