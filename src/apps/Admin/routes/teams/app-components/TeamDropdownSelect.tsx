import React, { useState } from "react";
import { DropdownPanel, DropdownButton } from "@floqastinc/flow-ui_core";
import { t } from "@/utils/i18n";

interface Team {
  id: string;
  name: string;
}

interface TeamDropdownSelectProps {
  teams: Team[];
  selectedValue: Team | null;
  handleDropDownOnChange: (value: string) => void;
}

const TeamDropdownSelect: React.FC<TeamDropdownSelectProps> = ({
  teams,
  selectedValue,
  handleDropDownOnChange,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  return (
    <DropdownPanel
      disableClear
      isOpen={isDropdownOpen}
      onOpenChange={setIsDropdownOpen}
      onChange={(value: string) => {
        handleDropDownOnChange(value);
        setIsDropdownOpen(false);
      }}
      style={{ zIndex: 9050 }}
    >
      <DropdownPanel.Trigger>
        <DropdownButton onClick={() => setIsDropdownOpen(!isDropdownOpen)}>
          {selectedValue?.name || t("components.TeamDropdownSelect.selectTeamMigrate")}
        </DropdownButton>
      </DropdownPanel.Trigger>
      <DropdownPanel.Content style={{ zIndex: 9050 }}>
        {teams.map((team) => (
          <DropdownPanel.Option key={team.id} value={team.id}>
            {team.name}
          </DropdownPanel.Option>
        ))}
      </DropdownPanel.Content>
    </DropdownPanel>
  );
};

export default TeamDropdownSelect;
