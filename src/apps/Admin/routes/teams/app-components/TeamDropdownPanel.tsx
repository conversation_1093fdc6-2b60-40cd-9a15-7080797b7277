import React from "react";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import MoreVert from "@floqastinc/flow-ui_icons/material/MoreVert";
import SettingsOutlined from "@floqastinc/flow-ui_icons/material/SettingsOutlined";
// import MaterialEditIcon from '@floqastinc/flow-ui_icons/material/Edit';
import { DropdownPanel, IconButton } from "@floqastinc/flow-ui_core";
// import MaterialDeleteIcon from '@floqastinc/flow-ui_icons/material/Delete';
import DeleteConfirmationDialog from "./DeleteConfirmationDialog";
import { TEAMS_PAGE_STRINGS } from "@/utils/string";

const IconWrap = styled.div`
  float: right;
  display: flex;
`;

const DropdownPanelText = styled.span`
  margin-top: 3px;
`;

interface TeamDropdownPanelProps {
  teamId: string;
  name: string;
  externalId?: string;
  onDeleteTeam: (teamId: string) => void;
  onEditTeam: (teamId: string, name: string, externalId?: string) => void;
}

const TeamDropdownPanel: React.FC<TeamDropdownPanelProps> = ({
  teamId,
  name,
  externalId,
  onDeleteTeam,
  onEditTeam,
}) => {
  const [dropdownOpen, setDropdownOpen] = React.useState(false);
  const [confirmingDeletion, setConfirmingDeletion] = React.useState(false);
  const navigate = useNavigate();

  return (
    <>
      <DropdownPanel
        disableFilter
        disableClear
        isOpen={dropdownOpen}
        onOpenChange={() => {
          setDropdownOpen(!dropdownOpen);
        }}
        onChange={(value: string) => {
          if (value === "view-team") {
            setDropdownOpen(false);
            navigate(`/admin/teams/${teamId}`);
          } else if (value === "edit-team") {
            onEditTeam(teamId, name, externalId);
          } else if (value === "delete-team") {
            setConfirmingDeletion(true);
          }
        }}
      >
        <DropdownPanel.Trigger>
          <IconButton size="md" onClick={() => {}}>
            <MoreVert color="#1D2433" />
          </IconButton>
        </DropdownPanel.Trigger>
        <DropdownPanel.Content>
          <DropdownPanel.Option value="view-team" key="view-team">
            <IconWrap>
              <SettingsOutlined color="action" />
            </IconWrap>
            <DropdownPanelText>
              {TEAMS_PAGE_STRINGS.DROPDOWN_PANEL_TEXTS.MANAGE_TEAM}
            </DropdownPanelText>
          </DropdownPanel.Option>
          {/* <DropdownPanel.Option value="edit-team" key="edit-team">
            <IconWrap>
              <MaterialEditIcon color="action" />
            </IconWrap>
            <DropdownPanelText>
              {TEAMS_PAGE_STRINGS.DROPDOWN_PANEL_TEXTS.EDIT_TEAM}
            </DropdownPanelText>
          </DropdownPanel.Option>
          <DropdownPanel.Option value="delete-team" key="delete-team">
            <IconWrap>
              <MaterialDeleteIcon color="action" />
            </IconWrap>
            <DropdownPanelText>
              {TEAMS_PAGE_STRINGS.DROPDOWN_PANEL_TEXTS.DELETE_TEAM}
            </DropdownPanelText>
          </DropdownPanel.Option> */}
        </DropdownPanel.Content>
      </DropdownPanel>

      <DeleteConfirmationDialog
        isOpen={confirmingDeletion}
        onConfirm={() => {
          onDeleteTeam(teamId);
          setConfirmingDeletion(false);
        }}
        onClose={() => setConfirmingDeletion(false)} // Use onClose if available
      />
    </>
  );
};

export default TeamDropdownPanel;
