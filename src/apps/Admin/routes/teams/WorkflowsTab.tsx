import React, { useState } from "react";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import {
  Input,
  Flex,
  Table,
  THead,
  TR,
  TH,
  TD,
  TBody,
  Heading,
  Button,
  useToast,
  DropdownPanel,
  IconButton,
} from "@floqastinc/flow-ui_core";
import Search from "@floqastinc/flow-ui_icons/material/SearchOutlined";
import { Team } from "@floqastinc/transform-v0/lib/v0/types";
import { Workflow } from "@floqastinc/transform-v3";
import MoreVert from "@floqastinc/flow-ui_icons/material/MoreVert";
import { match } from "ts-pattern";
import TeamMigrationModal from "./app-components/TeamMigrationModal";
import { t } from "@/utils/i18n";
import { getLambdaEndpoint } from "@/utils/request";

export const StyledLoadMoreBar = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 20px;
`;

export const WorkflowsTabContents: React.FC<{
  teams: Team[];
  workflows: Workflow[];
  hasNextPage: boolean;
  fetchNextPage: () => void;
  handleSearch: (value: string) => void;
  onInvalidateQuery: () => Promise<void>;
}> = ({ teams, workflows, onInvalidateQuery, handleSearch, hasNextPage, fetchNextPage }) => {
  const { showToast, Toast } = useToast();
  const [moreOptionsOpenForWorkflowId, setMoreOptionsOpenForWorkflowId] = useState<string | null>(
    null,
  );
  const navigate = useNavigate();
  const handleMigration = async (
    workflowId: string,
    teamId: string,
    entityId: string,
    createdBy: string,
  ) => {
    try {
      const response = await axios.post(
        `${getLambdaEndpoint("transform_api", true)}/migration/workflows/${workflowId}`,
        {
          teamId: teamId,
          entityId: entityId,
          createdBy: createdBy,
        },
        {
          withCredentials: true,
        },
      );
      console.log("Migration successful:", response.data);

      // Wrapped in setTimeout to ensure the toast displays correctly after invalidating query data
      showToast(
        <Toast type="success">
          <Toast.Title>{t("components.WorkflowsTab.workflowMigrated")}</Toast.Title>
          <Toast.Message>
            {t("components.WorkflowsTab.theWorkflow", {
              workflowName: workflows.find((workflow) => workflow.id === workflowId)?.name,
            })}
            {t("components.WorkflowsTab.hasBeenMigrated", {
              teamName: teams?.find((t) => t?.id === teamId)?.name,
            })}
          </Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
      onInvalidateQuery();
    } catch (error) {
      console.error("Error during migration:", error);
      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.WorkflowsTab.somethingWentWrong")}</Toast.Title>
          <Toast.Message>
            {t("components.WorkflowsTab.unableToMigrateWorkflow", {
              workflowName: workflows.find((workflow) => workflow.id === workflowId)?.name,
              teamName: teams.find((t) => t?.id === teamId)?.name,
            })}
          </Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
    }
  };

  return (
    <Flex direction="column" gap={24} style={{ marginTop: "3rem" }}>
      <Heading variant="h2">{t("components.WorkflowsTab.workflows")}</Heading>
      <Input
        aria-label={t("components.WorkflowsTab.filterWorkflows")}
        isSearchable
        onChange={handleSearch}
        placeholder="Filter workflows"
      >
        <Input.LeftItem>
          <Search size={20} />
        </Input.LeftItem>
      </Input>
      {workflows.length > 0 ? (
        <Table fixed middleBorders>
          <THead>
            <TR>
              <TH>{t("components.WorkflowsTab.ID")}</TH>
              <TH>{t("components.WorkflowsTab.name")}</TH>
              <TH>{t("components.WorkflowsTab.createdAt")}</TH>
              <TH>{t("components.WorkflowsTab.migrate")}</TH>
            </TR>
          </THead>
          <TBody>
            {workflows.map((workflow) => (
              <TR key={workflow.id}>
                <TD style={{ verticalAlign: "middle" }}>{workflow.id}</TD>
                <TD style={{ verticalAlign: "middle" }}>{workflow.name}</TD>
                <TD style={{ verticalAlign: "middle" }}>
                  {new Date(workflow.createdAt).toLocaleDateString()}
                </TD>
                <TD style={{ verticalAlign: "middle" }}>
                  <DropdownPanel
                    disableFilter
                    disableClear
                    isOpen={moreOptionsOpenForWorkflowId === workflow.id}
                    onOpenChange={(isOpen: boolean) => {
                      if (isOpen) {
                        setMoreOptionsOpenForWorkflowId(workflow.id);
                      } else {
                        setMoreOptionsOpenForWorkflowId(null);
                      }
                    }}
                    onChange={(value: string) => {
                      match(value).with("view-scripts", () => {
                        navigate(`/admin/workflows/${workflow.id}/scripts`);
                      });
                    }}
                  >
                    <DropdownPanel.Trigger>
                      <IconButton
                        size="md"
                        data-tracking-id="dashboard-more-options-dropdown-button"
                        onClick={() => {
                          setMoreOptionsOpenForWorkflowId(workflow.id);
                        }}
                      >
                        <MoreVert color="#1D2433" />
                      </IconButton>
                    </DropdownPanel.Trigger>
                    <DropdownPanel.Content>
                      <DropdownPanel.Option value="migrate-workflow" key="migrate-workflow">
                        <TeamMigrationModal
                          teams={teams}
                          workflow={workflow}
                          onInvalidateQuery={onInvalidateQuery}
                          handleMigration={handleMigration}
                        >
                          <div>{t("components.WorkflowsTab.migrate")}</div>
                        </TeamMigrationModal>
                      </DropdownPanel.Option>
                      <DropdownPanel.Option value="view-scripts" key="view-scripts">
                        <div>{t("components.WorkflowsTab.viewScripts")}</div>
                      </DropdownPanel.Option>
                    </DropdownPanel.Content>
                  </DropdownPanel>
                </TD>
              </TR>
            ))}
          </TBody>
        </Table>
      ) : (
        <Flex justify="center">
          <p>{t("components.WorkflowsTab.noWorkflowsFound")}</p>
        </Flex>
      )}
      {hasNextPage ? (
        <StyledLoadMoreBar>
          <Button onClick={() => fetchNextPage()}>{t("components.WorkflowsTab.loadMore")}</Button>
        </StyledLoadMoreBar>
      ) : null}
    </Flex>
  );
};
