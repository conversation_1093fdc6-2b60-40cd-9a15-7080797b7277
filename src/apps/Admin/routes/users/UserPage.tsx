import React, { useCallback, useState } from "react";
import { useSuspenseQuery, useQueryClient } from "@tanstack/react-query";
import {
  Button,
  Dialog,
  DropdownButton,
  DropdownPanel,
  Flex,
  Heading,
  useToast,
  TableStatusBadge,
} from "@floqastinc/flow-ui_core";
import { Link, useParams } from "react-router-dom";
import { Team, UserRole } from "@floqastinc/transform-v0";
import v0, { ApiError } from "@/services/v0";
import { t } from "@/utils/i18n";
import { useCurrentUser } from "@/utils/principal";

const options: Record<UserRole, string> = {
  ADMIN: "ADMIN",
  OMNI: "OMNI",
  SUPER: "SUPER",
  USER: "USER",
};

export const UserPage: React.FC = () => {
  const { userId } = useParams();

  if (!userId) return null;

  return <WrappedUserPage userId={userId} />;
};

const WrappedUserPage: React.FC<{ userId: string }> = ({ userId }) => {
  const queryClient = useQueryClient();
  const {
    data: { user, team },
  } = useSuspenseQuery({
    queryKey: ["users", userId],
    queryFn: async () => {
      const userRes = await v0.users.getUser({ userId });
      if (userRes.errors.length) throw new ApiError(userRes.errors);

      if (!userRes.data) return { user: undefined, team: undefined };

      const teamRes = await v0.teams.getTeam({ teamId: userRes.data.teamId });
      if (teamRes.errors.length) throw new ApiError(teamRes.errors);

      return { user: userRes.data, team: teamRes.data };
    },
  });

  const handleChangeRole = useCallback(async (e: React.ChangeEvent<HTMLSelectElement>) => {
    console.log("change role", e.target.value);

    const res = await v0.users.updateUser({
      userId,
      user: {
        role: e.target.value as UserRole,
      },
    });

    if (res.errors.length) {
      console.error(res.errors);
      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.UserPage.somethingWentWrong")}</Toast.Title>
          <Toast.Message>{t("components.UserPage.unableToUpdateUserRole")}</Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
    } else {
      showToast(
        <Toast type="success">
          <Toast.Title>{t("components.UserPage.roleUpdated")}</Toast.Title>
          <Toast.Message>{t("components.UserPage.userRoleUpdated")}</Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
    }
  }, []);

  const { showToast, Toast } = useToast();

  const [resendingEmail, setResendingEmail] = React.useState(false);
  const handleResendEmail = useCallback(async () => {
    setResendingEmail(true);
    const res = await v0.users.resendInvite({ userId });
    setResendingEmail(false);
    if (res.errors.length) {
      console.error(res.errors);
      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.UserPage.somethingWentWrong")}</Toast.Title>
          <Toast.Message>
            {t("components.UserPage.unableTempPassword", { userEmail: user?.email })}
          </Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
    } else {
      showToast(
        <Toast type="success">
          <Toast.Title>{t("components.UserPage.emailSent")}</Toast.Title>
          <Toast.Message>
            {t("components.UserPage.emailedTempPassword", { userEmail: user?.email })}
          </Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
    }
  }, [userId]);

  const [confirmation, setConfirmation] = React.useState("");
  const handleConfirmationChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setConfirmation(e.target.value);
  }, []);

  const [confirmingDeletion, setConfirmingDeletion] = React.useState(false);

  const handleCancelDeleteClick = useCallback(async () => {
    setConfirmation("");
    setConfirmingDeletion(false);
  }, []);

  const [deleting, setDeleting] = React.useState(false);

  const handleConfirmDeleteClick = useCallback(async () => {
    setConfirmation("");
    setConfirmingDeletion(false);
    setDeleting(true);
    const res = await v0.users.deleteUser({ userId });

    if (res.errors.length) {
      console.error(res.errors);
      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.UserPage.somethingWentWrong")}</Toast.Title>
          <Toast.Message>
            {t("components.UserPage.unableDeleteUser", { userEmail: user?.email })}
          </Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
    } else {
      showToast(
        <Toast type="success">
          <Toast.Title>{t("components.UserPage.userDeleted")}</Toast.Title>
          <Toast.Message>
            {t("components.UserPage.userDeletedMessage", { userEmail: user?.email })}
          </Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
    }
  }, []);

  const currentUser = useCurrentUser();

  const { data: teams } = useSuspenseQuery({
    queryKey: ["teams"],
    queryFn: async () => {
      const res = await v0.teams.getTeams({ first: 100 });
      if (res.errors.length) throw new ApiError(res.errors);
      return res.data ?? [];
    },
  });

  const [teamToSwitchTo, setTeamToSwitchTo] = useState<Team>();
  const [isConfirmingTeamSwitch, setIsConfirmingTeamSwitch] = useState<boolean>();
  const [isTeamSwitcherOpen, setIsTeamSwitcherOpen] = useState<boolean>();

  const handleConfirmSwitchTeams = useCallback(
    (teamId: string) => {
      if (user && teamId !== user.teamId) {
        setTeamToSwitchTo(teams.find((t) => t.id === teamId));
        setIsConfirmingTeamSwitch(true);
      }
      setIsTeamSwitcherOpen(false);
    },
    [user?.teamId],
  );

  const handleSwitchTeams = useCallback(
    async (teamId: string | undefined) => {
      if (!teamId) return;

      const res = await v0.users.updateUser({ userId, user: { teamId } });
      if (!res.errors.length && res.data) {
        if (userId === currentUser?.id) {
          // signOut();
        } else {
          setIsConfirmingTeamSwitch(false);
          queryClient.invalidateQueries(["users", userId] as any); // Expects a string, but this is the correct query key
        }
      } else {
        console.error(res.errors);
      }
    },
    [userId, currentUser?.id],
  );

  if (!user)
    return (
      <div>
        <Heading>{t("components.UserPage.team")}</Heading>
        <Heading variant="h2">{t("components.UserPage.notFound")}</Heading>
      </div>
    );

  return (
    <div>
      <Heading>
        <Flex gap="8">
          <span>{user.email}</span>
          {user.id === currentUser?.id && (
            <TableStatusBadge>{t("components.UserPage.you")}</TableStatusBadge>
          )}
        </Flex>
      </Heading>
      <div style={{ marginTop: 24 }}>
        {t("components.UserPage.roleColon")}
        <select name="userRole" id="userRole" onChange={handleChangeRole}>
          {Object.entries(options).map(([key, value]) => (
            <option key={key} value={key} selected={user.role === key}>
              {value}
            </option>
          ))}
        </select>
      </div>
      <div>{t("components.UserPage.userStatus", { status: user.status })}</div>
      {!!team && (
        <div>
          <Flex gap={8}>
            <span>{t("components.UserPage.teamColon")}</span>
            <Link to={`/admin/teams/${team.id}`}>{team.name}</Link>
            <DropdownPanel
              selectedValues={user.teamId}
              isOpen={isTeamSwitcherOpen}
              onChange={handleConfirmSwitchTeams}
              onOpenChange={setIsTeamSwitcherOpen}
              disableClear
              disableFilter
            >
              <DropdownPanel.Trigger>
                <DropdownButton>{t("components.UserPage.switchTeams")}</DropdownButton>
              </DropdownPanel.Trigger>
              <DropdownPanel.Content>
                {teams.map((team) => (
                  <DropdownPanel.Option key={team.id} value={team.id}>
                    {team.name}
                  </DropdownPanel.Option>
                ))}
              </DropdownPanel.Content>
            </DropdownPanel>
            <Dialog
              open={isConfirmingTeamSwitch}
              onOpenChange={setIsConfirmingTeamSwitch}
              type="danger"
            >
              <Dialog.Header>{t("components.UserPage.changeTeam")}</Dialog.Header>
              <Dialog.Body>
                {user.id === currentUser?.id ? (
                  <p>
                    {t("components.UserPage.actionWillMoveYou", {
                      userEmail: user.email,
                      teamName: teamToSwitchTo?.name,
                    })}
                  </p>
                ) : (
                  <p>
                    {t("components.UserPage.actionWillMoveUser", {
                      userEmail: user.email,
                      teamName: teamToSwitchTo?.name,
                    })}{" "}
                  </p>
                )}
              </Dialog.Body>
              <Dialog.Footer>
                <Dialog.FooterCancelBtn onClick={() => setIsConfirmingTeamSwitch(false)}>
                  {t("components.UserPage.cancel")}
                </Dialog.FooterCancelBtn>
                <Dialog.FooterActionBtn
                  color="danger"
                  onClick={() => handleSwitchTeams(teamToSwitchTo?.id)}
                >
                  {user.id === currentUser?.id
                    ? t("components.UserPage.changeTeamAndLogOut")
                    : t("components.UserPage.changeTeam")}
                </Dialog.FooterActionBtn>
              </Dialog.Footer>
            </Dialog>
          </Flex>
        </div>
      )}
      <div style={{ marginTop: 24 }}>
        <Heading variant="h2">{t("components.UserPage.actions")}</Heading>
        <Flex gap={8} style={{ marginTop: ".5rem" }}>
          {user.status === "FORCE_CHANGE_PASSWORD" && (
            <Button disabled={resendingEmail} onClick={handleResendEmail}>
              {t("components.UserPage.resendEmail")}
            </Button>
          )}
          <Dialog open={confirmingDeletion} onOpenChange={setConfirmingDeletion} type="danger">
            <Dialog.Trigger color="danger">
              <Button disabled={deleting}>{t("components.UserPage.deleteUser")}</Button>
            </Dialog.Trigger>
            <Dialog.Header>{t("components.UserPage.permDeleteUser")}</Dialog.Header>
            <Dialog.Body>
              <p>{t("components.UserPage.userDeleteUndone")}</p>
              <p style={{ marginTop: "8px" }}>
                {t("components.UserPage.typeDelete")}
                <input
                  style={{ border: "1px solid grey" }}
                  type="text"
                  placeholder="delete"
                  value={confirmation}
                  onChange={handleConfirmationChange}
                />
              </p>
            </Dialog.Body>
            <Dialog.Footer>
              <Dialog.FooterCancelBtn onClick={handleCancelDeleteClick}>
                {t("components.UserPage.cancel")}
              </Dialog.FooterCancelBtn>
              <Dialog.FooterActionBtn
                disabled={confirmation !== "delete"}
                color="danger"
                onClick={handleConfirmDeleteClick}
              >
                {t("components.UserPage.permanentlyDelete")}
              </Dialog.FooterActionBtn>
            </Dialog.Footer>
          </Dialog>
        </Flex>
      </div>
    </div>
  );
};
