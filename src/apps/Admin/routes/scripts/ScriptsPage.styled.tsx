import styled from "styled-components";
import { Divider, InlineAlert } from "@floqastinc/flow-ui_core";
import { Loading } from "@/components/Loading";

export const PageContainer = styled.div`
  padding: 32px;
  max-width: 1200px;
  margin: 0 auto;
`;

export const EditorContainer = styled.div`
  margin: 24px 0;
  border: 1px solid var(--flo-base-color-neutral-200);
  border-radius: 4px;
  overflow: hidden;
  min-height: 500px;
`;

export const ButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
`;

export const PaddedDivider = styled(Divider)`
  margin: 16px 0;
`;

export const AlertContainer = styled(InlineAlert)`
  margin-bottom: 16px;
`;

export const PaddedLoading = styled(Loading)`
  margin: 24px 0 !important;
`;
