import { useEffect, useState } from "react";
import { DropdownPanel, DropdownButton } from "@floqastinc/flow-ui_core";
import { Task } from "@floqastinc/transform-v3";
import { t } from "@/utils/i18n";

type ScriptsDropdownProps = {
  tasks: Task[];
  selectedValue: string | null;
  setSelectedValue: (value: string | null) => void;
  getTaskScript: (taskId: string | undefined) => void;
};

const ScriptsDropdown = ({
  tasks,
  selectedValue,
  setSelectedValue,
  getTaskScript,
}: ScriptsDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTaskName, setSelectedTaskName] = useState<string | undefined>();

  const handleOnChange = (value: string | undefined) => {
    setSelectedValue(value || null);
    setIsOpen(false);
    setSelectedTaskName(tasks.find((task) => task.id === value)?.name);
    getTaskScript(value);
  };

  useEffect(() => {
    if (!selectedValue) {
      return;
    }
  }, [selectedValue]);

  return (
    <DropdownPanel
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      onChange={handleOnChange}
      selectionMode="single"
      disableFilter
      disableClear
    >
      <DropdownPanel.Trigger>
        <DropdownButton>
          {selectedTaskName || t("components.ScriptsDropdown.selectStep")}
        </DropdownButton>
      </DropdownPanel.Trigger>
      <DropdownPanel.Content>
        {tasks.map(
          (task, index) =>
            task.strategy.kind === "SCRIPT" && (
              <DropdownPanel.Option key={task.id} value={task.id} name={task.name}>
                {`Step ${index + 1}: ${task.name}`}
              </DropdownPanel.Option>
            ),
        )}
      </DropdownPanel.Content>
    </DropdownPanel>
  );
};

export default ScriptsDropdown;
