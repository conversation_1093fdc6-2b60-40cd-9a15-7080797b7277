import { Decoration, DecorationSet, EditorView, ViewPlugin, ViewUpdate } from "@codemirror/view";

// Patterns for highlighting and tooltips
const SCHEMA_REGEX = /\{([^{}]+)\}/g;

export const createSchemaHighlighter = () => {
  const schemaDecoration = Decoration.mark({
    class: "cm-schema-reference",
  });

  return ViewPlugin.fromClass(
    class {
      decorations: DecorationSet;

      constructor(view: EditorView) {
        this.decorations = this.buildDecorations(view);
      }

      update(update: ViewUpdate) {
        if (update.docChanged || update.viewportChanged) {
          this.decorations = this.buildDecorations(update.view);
        }
      }

      buildDecorations(view: EditorView) {
        const decorations = [];
        const { doc } = view.state;

        for (let i = 0; i < doc.lines; i++) {
          const line = doc.line(i + 1);
          const text = line.text;

          // Apply regex to find all schema references
          let match;
          SCHEMA_REGEX.lastIndex = 0;

          while ((match = SCHEMA_REGEX.exec(text)) !== null) {
            const start = line.from + match.index;
            const end = start + match[0].length;
            decorations.push(schemaDecoration.range(start, end));
          }
        }

        return Decoration.set(decorations);
      }
    },
    {
      decorations: (view) => view.decorations,
    },
  );
};
