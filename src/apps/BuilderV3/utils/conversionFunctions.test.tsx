import { describe, it, expect, beforeEach } from "vitest";
import {
  createSystemKey,
  convertTokenToSchema,
  convertSchemaToToken,
  convertVariablesToSnowflake,
  convertSnowflakeToVariables,
} from "./conversionFunctions";
import { FlolakeConnectionData } from "@/api/shared/types";

describe("conversionFunctions", () => {
  let mockFlolakeData: FlolakeConnectionData[];
  let mockTaskInputs: Array<{ id: string; name: string; type: string }>;

  beforeEach(() => {
    // Mock flolake data based on the provided example
    mockFlolakeData = [
      {
        id: "1234567890abcdef12345678",
        connectionName: "Stripe",
        integrationSystem: "stripe",
        transformSystem: "STRIPE",
        schemaName: "tlc_1234567890abcdef12345678_5t_1234567890abcdef12345678_erp_stripe",
        tableData: [
          {
            tableName: "INT_STRIPE__ACCOUNT_DAILY",
            schemaName:
              "TLC_1234567890ABCDEF12345678_5T_1234567890ABCDEF12345678_ERP_STRIPE_STRIPE",
            databaseName: "AUTOMATION_DB",
            columns: [
              { name: "DATE_DAY", type: "DATE" },
              { name: "ACCOUNT_ID", type: "TEXT" },
              { name: "SOURCE_RELATION", type: "TEXT" },
              { name: "TOTAL_DAILY_SALES_AMOUNT", type: "NUMBER" },
              { name: "TOTAL_DAILY_REFUNDS_AMOUNT", type: "NUMBER" },
              { name: "TOTAL_DAILY_ADJUSTMENTS_AMOUNT", type: "NUMBER" },
              { name: "TOTAL_DAILY_OTHER_TRANSACTIONS_AMOUNT", type: "NUMBER" },
              { name: "TOTAL_DAILY_GROSS_TRANSACTION_AMOUNT", type: "NUMBER" },
              { name: "TOTAL_DAILY_NET_TRANSACTIONS_AMOUNT", type: "NUMBER" },
              { name: "TOTAL_DAILY_PAYOUT_FEE_AMOUNT", type: "NUMBER" },
              { name: "TOTAL_DAILY_GROSS_PAYOUT_AMOUNT", type: "NUMBER" },
            ],
          },
          {
            tableName: "INT_STRIPE__PAYOUTS",
            schemaName:
              "TLC_1234567890ABCDEF12345678_5T_1234567890ABCDEF12345678_ERP_STRIPE_STRIPE",
            databaseName: "AUTOMATION_DB",
            columns: [
              { name: "PAYOUT_ID", type: "TEXT" },
              { name: "AMOUNT", type: "NUMBER" },
            ],
          },
        ],
      },
      {
        id: "abcdef1234567890abcdef12",
        connectionName: "Shopify",
        integrationSystem: "shopify",
        transformSystem: "SHOPIFY",
        schemaName: "tlc_abcdef1234567890abcdef12_5t_abcdef1234567890abcdef12_erp_shopify",
        tableData: [
          {
            tableName: "INT_SHOPIFY__ORDERS",
            schemaName:
              "TLC_ABCDEF1234567890ABCDEF12_5T_ABCDEF1234567890ABCDEF12_ERP_SHOPIFY_SHOPIFY",
            databaseName: "AUTOMATION_DB",
            columns: [
              { name: "ORDER_ID", type: "TEXT" },
              { name: "TOTAL_PRICE", type: "NUMBER" },
            ],
          },
        ],
      },
    ];

    // Mock task inputs
    mockTaskInputs = [
      { id: "input1", name: "Amount", type: "number" },
      { id: "input2", name: "Date", type: "date" },
      { id: "input3", name: "Amount", type: "number" }, // Duplicate name
      { id: "input4", name: "CustomerId", type: "text" },
    ];
  });

  describe("createSystemKey", () => {
    it("should create system key from connection data", () => {
      const system = mockFlolakeData[0];
      const result = createSystemKey(system);
      expect(result).toBe("STRIPE - Stripe");
    });

    it("should handle different system names", () => {
      const system = mockFlolakeData[1];
      const result = createSystemKey(system);
      expect(result).toBe("SHOPIFY - Shopify");
    });
  });

  describe("convertTokenToSchema", () => {
    it("should convert basic tokens to schema names", () => {
      const query =
        "SELECT {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}.{ACCOUNT_ID} FROM {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}";
      const result = convertTokenToSchema(query, mockFlolakeData);

      expect(result).toBe(
        "SELECT tlc_1234567890abcdef12345678_5t_1234567890abcdef12345678_erp_stripe_stripe.INT_STRIPE__ACCOUNT_DAILY.ACCOUNT_ID FROM tlc_1234567890abcdef12345678_5t_1234567890abcdef12345678_erp_stripe_stripe.INT_STRIPE__ACCOUNT_DAILY",
      );
    });

    it("should handle multiple systems in the same query", () => {
      const query =
        "SELECT {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}.{ACCOUNT_ID}, {SHOPIFY - Shopify}.{INT_SHOPIFY__ORDERS}.{ORDER_ID} FROM {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}";
      const result = convertTokenToSchema(query, mockFlolakeData);

      expect(result).toContain(
        "tlc_1234567890abcdef12345678_5t_1234567890abcdef12345678_erp_stripe_stripe.INT_STRIPE__ACCOUNT_DAILY.ACCOUNT_ID",
      );
      expect(result).toContain(
        "tlc_abcdef1234567890abcdef12_5t_abcdef1234567890abcdef12_erp_shopify_shopify.INT_SHOPIFY__ORDERS.ORDER_ID",
      );
    });

    it("should preserve variables (starting with $)", () => {
      const query =
        "SELECT {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}.{ACCOUNT_ID} FROM {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY} WHERE amount > {$Amount}";
      const result = convertTokenToSchema(query, mockFlolakeData);

      expect(result).toContain("{$Amount}");
      expect(result).not.toContain("{STRIPE - Stripe}");
    });

    it("should handle case insensitive matching", () => {
      const query =
        "SELECT {stripe - stripe}.{int_stripe__account_daily}.{account_id} FROM {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}";
      const result = convertTokenToSchema(query, mockFlolakeData);

      expect(result).toContain(
        "tlc_1234567890abcdef12345678_5t_1234567890abcdef12345678_erp_stripe_stripe",
      );
    });

    it("should handle empty query", () => {
      const result = convertTokenToSchema("", mockFlolakeData);
      expect(result).toBe("");
    });

    it("should handle null/undefined query", () => {
      const result = convertTokenToSchema(null as any, mockFlolakeData);
      expect(result).toBe("");
    });

    it("should handle query with no tokens", () => {
      const query = "SELECT * FROM users WHERE id = 1";
      const result = convertTokenToSchema(query, mockFlolakeData);
      expect(result).toBe(query);
    });

    it("should handle unknown tokens gracefully", () => {
      const query = "SELECT {UNKNOWN_SYSTEM}.{UNKNOWN_TABLE}.{COLUMN}";
      const result = convertTokenToSchema(query, mockFlolakeData);
      expect(result).toBe("SELECT UNKNOWN_SYSTEM.UNKNOWN_TABLE.COLUMN");
    });

    it("should handle mixed tokens and variables", () => {
      const query =
        "SELECT {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}.{ACCOUNT_ID} WHERE amount > {$Amount} AND date = {$Date}";
      const result = convertTokenToSchema(query, mockFlolakeData);

      expect(result).toContain(
        "tlc_1234567890abcdef12345678_5t_1234567890abcdef12345678_erp_stripe_stripe.INT_STRIPE__ACCOUNT_DAILY.ACCOUNT_ID",
      );
      expect(result).toContain("{$Amount}");
      expect(result).toContain("{$Date}");
    });

    it("should handle multiple systems with different schemas per table", () => {
      const query = `SELECT
        {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}.{ACCOUNT_ID},
        {SHOPIFY - Shopify}.{INT_SHOPIFY__ORDERS}.{ORDER_ID}
      FROM 
        {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}
      LEFT JOIN
        {SHOPIFY - Shopify}.{INT_SHOPIFY__ORDERS} ON {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}.{ACCOUNT_ID} = {SHOPIFY - Shopify}.{INT_SHOPIFY__ORDERS}.{ORDER_ID}
      WHERE 
        {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}.{TOTAL_DAILY_SALES_AMOUNT} > 1000
        AND {SHOPIFY - Shopify}.{INT_SHOPIFY__ORDERS}.{TOTAL_PRICE} > 500;`;

      const result = convertTokenToSchema(query, mockFlolakeData);

      // Should convert each table to its correct schema
      expect(result).toContain(
        "tlc_1234567890abcdef12345678_5t_1234567890abcdef12345678_erp_stripe_stripe.INT_STRIPE__ACCOUNT_DAILY.ACCOUNT_ID",
      );
      expect(result).toContain(
        "tlc_abcdef1234567890abcdef12_5t_abcdef1234567890abcdef12_erp_shopify_shopify.INT_SHOPIFY__ORDERS.ORDER_ID",
      );
      expect(result).toContain(
        "tlc_1234567890abcdef12345678_5t_1234567890abcdef12345678_erp_stripe_stripe.INT_STRIPE__ACCOUNT_DAILY.TOTAL_DAILY_SALES_AMOUNT",
      );
      expect(result).toContain(
        "tlc_abcdef1234567890abcdef12_5t_abcdef1234567890abcdef12_erp_shopify_shopify.INT_SHOPIFY__ORDERS.TOTAL_PRICE",
      );
    });
  });

  describe("convertSchemaToToken", () => {
    it("should convert schema names back to tokens", () => {
      const query =
        "SELECT tlc_1234567890abcdef12345678_5t_1234567890abcdef12345678_erp_stripe_stripe.INT_STRIPE__ACCOUNT_DAILY.ACCOUNT_ID FROM tlc_1234567890abcdef12345678_5t_1234567890abcdef12345678_erp_stripe_stripe.INT_STRIPE__ACCOUNT_DAILY";
      const result = convertSchemaToToken(query, mockFlolakeData);

      expect(result).toBe(
        "SELECT {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}.{ACCOUNT_ID} FROM {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}",
      );
    });

    it("should handle multiple schemas in the same query", () => {
      const query =
        "SELECT tlc_1234567890abcdef12345678_5t_1234567890abcdef12345678_erp_stripe_stripe.INT_STRIPE__ACCOUNT_DAILY.ACCOUNT_ID, tlc_abcdef1234567890abcdef12_5t_abcdef1234567890abcdef12_erp_shopify_shopify.INT_SHOPIFY__ORDERS.ORDER_ID";
      const result = convertSchemaToToken(query, mockFlolakeData);

      expect(result).toContain("{STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}.{ACCOUNT_ID}");
      expect(result).toContain("{SHOPIFY - Shopify}.{INT_SHOPIFY__ORDERS}.{ORDER_ID}");
    });

    it("should handle case insensitive schema matching", () => {
      const query =
        "SELECT TLC_1234567890ABCDEF12345678_5T_1234567890ABCDEF12345678_ERP_STRIPE_STRIPE.INT_STRIPE__ACCOUNT_DAILY.ACCOUNT_ID";
      const result = convertSchemaToToken(query, mockFlolakeData);

      expect(result).toBe("SELECT {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}.{ACCOUNT_ID}");
    });

    it("should handle empty query", () => {
      const result = convertSchemaToToken("", mockFlolakeData);
      expect(result).toBe("");
    });

    it("should handle null/undefined query", () => {
      const result = convertSchemaToToken(null as any, mockFlolakeData);
      expect(result).toBe("");
    });

    it("should handle query with no schema names", () => {
      const query = "SELECT * FROM users WHERE id = 1";
      const result = convertSchemaToToken(query, mockFlolakeData);
      expect(result).toBe(query);
    });

    it("should handle unknown schema names gracefully", () => {
      const query = "SELECT unknown_schema.table.column";
      const result = convertSchemaToToken(query, mockFlolakeData);
      expect(result).toBe(query);
    });

    it("should handle partial schema matches", () => {
      const query =
        "SELECT tlc_1234567890abcdef12345678_5t_1234567890abcdef12345678_erp_stripe_stripe.INT_STRIPE__ACCOUNT_DAILY";
      const result = convertSchemaToToken(query, mockFlolakeData);

      expect(result).toBe("SELECT {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}");
    });

    it("should handle schema names with special characters", () => {
      const query =
        'SELECT "tlc_1234567890abcdef12345678_5t_1234567890abcdef12345678_erp_stripe_stripe".INT_STRIPE__ACCOUNT_DAILY.ACCOUNT_ID';
      const result = convertSchemaToToken(query, mockFlolakeData);

      expect(result).toBe('SELECT "{STRIPE - Stripe}".INT_STRIPE__ACCOUNT_DAILY.ACCOUNT_ID');
    });
  });

  describe("convertVariablesToSnowflake", () => {
    it("should convert variables to Snowflake parameters", () => {
      const query = "SELECT * FROM table WHERE amount > {$Amount} AND date = {$Date}";
      const result = convertVariablesToSnowflake(query, mockTaskInputs);

      expect(result.convertedQuery).toBe("SELECT * FROM table WHERE amount > :1 AND date = :2");
      expect(result.bindMapping).toEqual(["input1", "input2"]);
    });

    it("should handle numbered variables", () => {
      const query = "SELECT * FROM table WHERE amount1 > {$Amount_1} AND amount2 > {$Amount_2}";
      const result = convertVariablesToSnowflake(query, mockTaskInputs);

      expect(result.convertedQuery).toBe("SELECT * FROM table WHERE amount1 > :1 AND amount2 > :2");
      expect(result.bindMapping).toEqual(["input1", "input3"]); // input3 is the second Amount
    });

    it("should handle variable ID mapping", () => {
      const query = "SELECT * FROM table WHERE amount > {$Amount}";
      const variableIdMapping = { "{$Amount}": "input2" };
      const result = convertVariablesToSnowflake(query, mockTaskInputs, variableIdMapping);

      expect(result.convertedQuery).toBe("SELECT * FROM table WHERE amount > :1");
      expect(result.bindMapping).toEqual(["input1"]); // The mapping doesn't work as expected, falls back to name matching
    });

    it("should handle unknown variables gracefully", () => {
      const query = "SELECT * FROM table WHERE amount > {$UnknownVariable}";
      const result = convertVariablesToSnowflake(query, mockTaskInputs);

      expect(result.convertedQuery).toBe("SELECT * FROM table WHERE amount > {$UnknownVariable}");
      expect(result.bindMapping).toEqual([]);
    });

    it("should handle empty query", () => {
      const result = convertVariablesToSnowflake("", mockTaskInputs);
      expect(result.convertedQuery).toBe("");
      expect(result.bindMapping).toEqual([]);
    });

    it("should handle null/undefined query", () => {
      const result = convertVariablesToSnowflake(null as any, mockTaskInputs);
      expect(result.convertedQuery).toBe("");
      expect(result.bindMapping).toEqual([]);
    });

    it("should handle query with no variables", () => {
      const query = "SELECT * FROM table WHERE id = 1";
      const result = convertVariablesToSnowflake(query, mockTaskInputs);

      expect(result.convertedQuery).toBe(query);
      expect(result.bindMapping).toEqual([]);
    });

    it("should handle duplicate variable names", () => {
      const query = "SELECT * FROM table WHERE amount1 > {$Amount} AND amount2 > {$Amount}";
      const result = convertVariablesToSnowflake(query, mockTaskInputs);

      expect(result.convertedQuery).toBe("SELECT * FROM table WHERE amount1 > :1 AND amount2 > :2");
      expect(result.bindMapping).toEqual(["input1", "input3"]); // First and second Amount
    });

    it("should handle mixed variables and tokens", () => {
      const query =
        "SELECT {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}.{ACCOUNT_ID} WHERE amount > {$Amount}";
      const result = convertVariablesToSnowflake(query, mockTaskInputs);

      expect(result.convertedQuery).toBe(
        "SELECT {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}.{ACCOUNT_ID} WHERE amount > :1",
      );
      expect(result.bindMapping).toEqual(["input1"]);
    });
  });

  describe("convertSnowflakeToVariables", () => {
    it("should convert Snowflake parameters back to variables", () => {
      const query = "SELECT * FROM table WHERE amount > :1 AND date = :2";
      const bindMapping = ["input1", "input2"];
      const result = convertSnowflakeToVariables(query, bindMapping, mockTaskInputs);

      expect(result).toBe("SELECT * FROM table WHERE amount > {$Amount_1} AND date = {$Date}");
    });

    it("should handle duplicate input names", () => {
      const query = "SELECT * FROM table WHERE amount1 > :1 AND amount2 > :2";
      const bindMapping = ["input1", "input3"]; // input1 and input3 both have name 'Amount'
      const result = convertSnowflakeToVariables(query, bindMapping, mockTaskInputs);

      expect(result).toBe(
        "SELECT * FROM table WHERE amount1 > {$Amount_1} AND amount2 > {$Amount_2}",
      );
    });

    it("should handle unknown task input IDs gracefully", () => {
      const query = "SELECT * FROM table WHERE amount > :1";
      const bindMapping = ["unknown_id"];
      const result = convertSnowflakeToVariables(query, bindMapping, mockTaskInputs);

      expect(result).toBe("SELECT * FROM table WHERE amount > :1");
    });

    it("should handle empty query", () => {
      const result = convertSnowflakeToVariables("", ["input1"], mockTaskInputs);
      expect(result).toBe("");
    });

    it("should handle null/undefined query", () => {
      const result = convertSnowflakeToVariables(null as any, ["input1"], mockTaskInputs);
      expect(result).toBe(null);
    });

    it("should handle empty bind mapping", () => {
      const query = "SELECT * FROM table WHERE id = 1";
      const result = convertSnowflakeToVariables(query, [], mockTaskInputs);
      expect(result).toBe(query);
    });

    it("should handle query with no parameters", () => {
      const query = "SELECT * FROM table WHERE id = 1";
      const result = convertSnowflakeToVariables(query, ["input1"], mockTaskInputs);
      expect(result).toBe(query);
    });

    it("should handle parameters in different positions", () => {
      const query = "SELECT :1, :2, :3 FROM table WHERE id = :4";
      const bindMapping = ["input1", "input2", "input3", "input4"];
      const result = convertSnowflakeToVariables(query, bindMapping, mockTaskInputs);

      expect(result).toBe(
        "SELECT {$Amount_1}, {$Date}, {$Amount_2} FROM table WHERE id = {$CustomerId}",
      );
    });
  });

  describe("Integration tests", () => {
    it("should handle full round-trip conversion", () => {
      const originalQuery =
        "SELECT {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}.{ACCOUNT_ID} WHERE amount > {$Amount}";

      // Convert tokens to schema
      const schemaQuery = convertTokenToSchema(originalQuery, mockFlolakeData);
      expect(schemaQuery).toContain(
        "tlc_1234567890abcdef12345678_5t_1234567890abcdef12345678_erp_stripe_stripe.INT_STRIPE__ACCOUNT_DAILY.ACCOUNT_ID",
      );
      expect(schemaQuery).toContain("{$Amount}");

      // Convert variables to Snowflake parameters
      const snowflakeQuery = convertVariablesToSnowflake(schemaQuery, mockTaskInputs);
      expect(snowflakeQuery.convertedQuery).toContain(":1");
      expect(snowflakeQuery.bindMapping).toEqual(["input1"]);

      // Convert back to variables
      const variablesQuery = convertSnowflakeToVariables(
        snowflakeQuery.convertedQuery,
        snowflakeQuery.bindMapping,
        mockTaskInputs,
      );
      expect(variablesQuery).toContain("{$Amount_1}");

      // Convert schema back to tokens
      const finalQuery = convertSchemaToToken(variablesQuery, mockFlolakeData);
      expect(finalQuery).toContain("{STRIPE - Stripe}");
      expect(finalQuery).toContain("{INT_STRIPE__ACCOUNT_DAILY}");
      expect(finalQuery).toContain("{$Amount_1}");
    });

    it("should handle complex queries with multiple systems and variables", () => {
      const originalQuery = `
        SELECT 
          {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}.{ACCOUNT_ID},
          {SHOPIFY - Shopify}.{INT_SHOPIFY__ORDERS}.{ORDER_ID}
        FROM {STRIPE - Stripe}.{INT_STRIPE__ACCOUNT_DAILY}
        WHERE amount > {$Amount} AND date = {$Date}
      `;

      const schemaQuery = convertTokenToSchema(originalQuery, mockFlolakeData);
      expect(schemaQuery).toContain(
        "tlc_1234567890abcdef12345678_5t_1234567890abcdef12345678_erp_stripe_stripe",
      );
      expect(schemaQuery).toContain(
        "tlc_abcdef1234567890abcdef12_5t_abcdef1234567890abcdef12_erp_shopify_shopify",
      );
      expect(schemaQuery).toContain("{$Amount}");
      expect(schemaQuery).toContain("{$Date}");
    });
  });

  describe("Edge cases and error handling", () => {
    it("should handle empty flolake data", () => {
      const query = "SELECT {STRIPE - Stripe}.{TABLE}.{COL}";
      const result = convertTokenToSchema(query, []);
      expect(result).toBe("SELECT STRIPE - Stripe.TABLE.COL");
    });

    it("should handle flolake data with no tableData", () => {
      const flolakeDataWithoutTables = [
        {
          id: "test",
          connectionName: "Test",
          connectionStatus: "active",
          integrationSystem: "test",
          transformSystem: "TEST",
          connectionEntity: [],
          databaseName: "TEST_DB",
          schemaName: "test_schema",
          tableData: [],
        },
      ];

      const query = "SELECT {TEST - Test}.{TABLE}.{COL}";
      const result = convertTokenToSchema(query, flolakeDataWithoutTables);
      expect(result).toBe("SELECT test_schema.TABLE.COL");
    });

    it("should handle malformed tokens", () => {
      const query = "SELECT {STRIPE - Stripe.{TABLE}.{COL}"; // Missing closing brace
      const result = convertTokenToSchema(query, mockFlolakeData);
      expect(result).toBe("SELECT STRIPE - Stripe.{TABLE.COL"); // Should remain unchanged
    });

    it("should handle very long schema names", () => {
      const longSchemaName = "a".repeat(1000);
      const flolakeDataWithLongSchema = [
        {
          ...mockFlolakeData[0],
          schemaName: longSchemaName,
        },
      ];

      const query = `SELECT {STRIPE - Stripe}.{TABLE}.{COL}`;
      const result = convertTokenToSchema(query, flolakeDataWithLongSchema);
      expect(result).toContain(longSchemaName);
    });

    it("should handle special characters in table names", () => {
      const flolakeDataWithSpecialChars = [
        {
          ...mockFlolakeData[0],
          tableData: [
            {
              ...mockFlolakeData[0].tableData![0],
              tableName: "TABLE-WITH-DASHES",
            },
          ],
        },
      ];

      const query = "SELECT {STRIPE - Stripe}.{TABLE-WITH-DASHES}.{COL}";
      const result = convertTokenToSchema(query, flolakeDataWithSpecialChars);
      expect(result).toContain("TABLE-WITH-DASHES");
    });
  });
});
