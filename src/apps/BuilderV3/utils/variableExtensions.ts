import { EditorView } from "codemirror";
import { Decoration, DecorationSet, ViewPlugin, ViewUpdate, WidgetType } from "@codemirror/view";
import { RangeSetBuilder } from "@codemirror/state";
import { autocompletion, CompletionContext } from "@codemirror/autocomplete";

export type TaskInputVariable = {
  id: string;
  name: string;
  type: string;
};

// Variable highlighting regex - matches {$variableName}
const VARIABLE_REGEX = /\{\$([^}]+)\}/g;

export const createVariableHighlighter = () => {
  return ViewPlugin.fromClass(
    class {
      decorations: DecorationSet;

      constructor(view: EditorView) {
        this.decorations = this.buildDecorations(view);
      }

      update(update: ViewUpdate) {
        if (update.docChanged || update.viewportChanged) {
          this.decorations = this.buildDecorations(update.view);
        }
      }

      buildDecorations(view: EditorView) {
        const builder = new RangeSetBuilder<Decoration>();
        const doc = view.state.doc;

        for (let pos = 0; pos < doc.length; ) {
          const line = doc.lineAt(pos);
          const text = line.text;
          let match;

          VARIABLE_REGEX.lastIndex = 0; // Reset regex
          while ((match = VARIABLE_REGEX.exec(text)) !== null) {
            const from = line.from + match.index;
            const to = from + match[0].length;

            // Just highlight the entire variable reference
            builder.add(from, to, Decoration.mark({ class: "cm-variable-reference" }));
          }
          pos = line.to + 1;
        }

        return builder.finish();
      }
    },
    {
      decorations: (v) => v.decorations,
    },
  );
};

export const createVariableCompletion = (taskInputs: TaskInputVariable[]) => {
  return autocompletion({
    override: [
      (context: CompletionContext) => {
        const { state, pos } = context;
        const line = state.doc.lineAt(pos);
        const lineText = line.text;
        const linePos = pos - line.from;

        // Check if we're typing a variable (after {$)
        const beforeCursor = lineText.slice(0, linePos);
        const match = beforeCursor.match(/\{\$([^}]*)$/);

        if (!match) return null;

        const prefix = match[1].toLowerCase();
        const from = pos - prefix.length;

        const options = taskInputs
          .filter((input) => input.name.toLowerCase().includes(prefix))
          .map((input) => ({
            label: input.name,
            type: "variable",
            info: `Task Input (${input.type})`,
            apply: `${input.name}}`, // Complete with just name}
          }));

        return {
          from,
          options,
        };
      },
    ],
  });
};

/**
 * Theme for variable highlighting
 */
const variableReferenceTheme = EditorView.baseTheme({
  ".cm-variable-reference": {
    backgroundColor: "rgba(34, 139, 34, 0.2)",
    padding: "0 2px",
    color: "#228B22",
    fontWeight: "bold",
  },
});

/**
 * Creates CodeMirror extensions for variable reference highlighting and autocompletion
 */
export const createVariableReferenceExtensions = (taskInputs: TaskInputVariable[]) => {
  return [
    variableReferenceTheme,
    createVariableHighlighter(),
    createVariableCompletion(taskInputs),
  ];
};
