import { <PERSON><PERSON>ie<PERSON> } from "codemirror";
import { Decoration, DecorationSet, ViewPlugin, ViewUpdate } from "@codemirror/view";
import { autocompletion, CompletionContext } from "@codemirror/autocomplete";
import { FlolakeConnectionData } from "@/api/shared/types";
import { createSystemKey } from "./conversionFunctions";

export type TaskInputVariable = {
  id: string;
  name: string;
  type: string;
};

// Combined patterns for highlighting
const VARIABLE_REGEX = /\{\$([^{}]+)\}/g;
const SCHEMA_REGEX = /\{([^${}][^{}]*)\}(?:\.{([^{}]+)})?(?:\.{([^{}]+)})?/g;

/**
 * Generic highlighter that can handle multiple patterns
 */
const createGenericHighlighter = (patterns: Array<{ regex: RegExp; className: string }>) => {
  return ViewPlugin.fromClass(
    class {
      decorations: DecorationSet;

      constructor(view: EditorView) {
        this.decorations = this.buildDecorations(view);
      }

      update(update: ViewUpdate) {
        if (update.docChanged || update.viewportChanged) {
          this.decorations = this.buildDecorations(update.view);
        }
      }

      buildDecorations(view: EditorView) {
        const decorations: any[] = [];
        const { doc } = view.state;

        for (let i = 0; i < doc.lines; i++) {
          const line = doc.line(i + 1);
          const text = line.text;

          // Apply each pattern
          patterns.forEach(({ regex, className }) => {
            const decoration = Decoration.mark({ class: className });
            let match;
            regex.lastIndex = 0;

            while ((match = regex.exec(text)) !== null) {
              const start = line.from + match.index;
              const end = start + match[0].length;
              decorations.push(decoration.range(start, end));
            }
          });
        }

        return Decoration.set(decorations);
      }
    },
    {
      decorations: (view) => view.decorations,
    },
  );
};

/**
 * Generic completion helper
 */
const getTextBeforeCursor = (context: CompletionContext): string => {
  const { state, pos } = context;
  return state.sliceDoc(0, pos);
};

/**
 * Variable completion logic
 */
const createVariableCompletion = (taskInputs: TaskInputVariable[]) => {
  const variableCompletions = (context: CompletionContext) => {
    const textBefore = getTextBeforeCursor(context);

    // Check if we're inside a variable reference
    const lastOpenBrace = textBefore.lastIndexOf("{$");
    if (lastOpenBrace === -1) return null;

    const afterBrace = textBefore.slice(lastOpenBrace + 2);
    const closingBrace = afterBrace.indexOf("}");

    // Only complete if we haven't closed the brace yet
    if (closingBrace !== -1) return null;

    const prefix = afterBrace;

    const variableOptions = taskInputs
      .filter((input) => input.name.toLowerCase().includes(prefix.toLowerCase()))
      .map((input) => ({
        label: input.name,
        type: "variable",
        info: `${input.type} variable`,
        apply: (view: EditorView, completion: any, from: number, to: number) => {
          const insertText = `${input.name}}`;
          view.dispatch({
            changes: { from, to, insert: insertText },
            selection: { anchor: from + insertText.length, head: from + insertText.length },
          });
          return true;
        },
      }));

    return {
      from: context.pos - prefix.length,
      options: variableOptions,
    };
  };

  return autocompletion({
    activateOnTyping: true,
    override: [variableCompletions],
  });
};

/**
 * Schema completion logic
 */
const createSchemaCompletion = (connections: FlolakeConnectionData[]) => {
  const schemaCompletions = (context: CompletionContext) => {
    const textBefore = getTextBeforeCursor(context);

    // Check if we're inside a schema reference (but not a variable)
    const lastOpenBrace = textBefore.lastIndexOf("{");
    if (lastOpenBrace === -1) return null;

    const afterBrace = textBefore.slice(lastOpenBrace + 1);

    // Skip if this is a variable reference
    if (afterBrace.startsWith("$")) return null;

    const closingBrace = afterBrace.indexOf("}");

    // Only complete if we haven't closed the brace yet
    if (closingBrace !== -1) return null;

    const prefix = afterBrace;

    const schemaOptions = connections
      .filter((conn) => {
        const systemKey = createSystemKey(conn);
        return systemKey.toLowerCase().includes(prefix.toLowerCase());
      })
      .map((conn) => ({
        label: createSystemKey(conn),
        type: "schema",
        info: `${conn.transformSystem} connection`,
        apply: (view: EditorView, completion: any, from: number, to: number) => {
          const insertText = `${createSystemKey(conn)}}`;
          view.dispatch({
            changes: { from, to, insert: insertText },
            selection: { anchor: from + insertText.length, head: from + insertText.length },
          });
          return true;
        },
      }));

    return {
      from: context.pos - prefix.length,
      options: schemaOptions,
    };
  };

  return autocompletion({
    activateOnTyping: true,
    override: [schemaCompletions],
  });
};

/**
 * Combined theme for both variable and schema highlighting (reusing existing colors)
 */
const combinedTheme = EditorView.baseTheme({
  // Variable theme (from variableExtensions.ts)
  ".cm-variable-reference": {
    backgroundColor: "rgba(34, 139, 34, 0.2)",
    padding: "0 2px",
    color: "#228B22",
    fontWeight: "bold",
  },
  // Schema theme (from schemaReferenceExtensions.ts)
  ".cm-schema-reference": {
    backgroundColor: "rgba(65, 105, 225, 0.2)",
    padding: "0 2px",
    color: "#4169e1",
    fontWeight: "bold",
  },
  // Tooltip styles (from schemaReferenceExtensions.ts)
  ".cm-tooltip-info": {
    fontFamily: "monospace",
    backgroundColor: "#f8f9fa",
    color: "#212529",
    padding: "8px",
    fontSize: "12px",
    maxWidth: "300px",
  },
  ".cm-tooltip-warning": {
    fontFamily: "monospace",
    backgroundColor: "#fff3cd",
    color: "#856404",
    border: "1px solid #ffeeba",
    padding: "8px",
    fontSize: "12px",
  },
});

/**
 * Creates combined CodeMirror extensions for both variables and schema references
 */
export const createCombinedReferenceExtensions = (
  taskInputs: TaskInputVariable[],
  connections: FlolakeConnectionData[],
) => {
  const highlightPatterns = [
    { regex: VARIABLE_REGEX, className: "cm-variable-reference" },
    { regex: SCHEMA_REGEX, className: "cm-schema-reference" },
  ];

  return [
    combinedTheme,
    createGenericHighlighter(highlightPatterns),
    createVariableCompletion(taskInputs),
    createSchemaCompletion(connections),
  ];
};

/**
 * Legacy exports for backward compatibility
 */
export const createVariableReferenceExtensions = (taskInputs: TaskInputVariable[]) => {
  return createCombinedReferenceExtensions(taskInputs, []);
};

export const createSchemaReferenceExtensions = (connections: FlolakeConnectionData[]) => {
  return createCombinedReferenceExtensions([], connections);
};
