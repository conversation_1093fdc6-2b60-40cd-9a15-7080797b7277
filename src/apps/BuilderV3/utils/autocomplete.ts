/* eslint-disable no-restricted-syntax */
import {
  acceptCompletion,
  autocompletion,
  Completion,
  CompletionContext,
  CompletionResult,
} from "@codemirror/autocomplete";
import { EditorView, keymap } from "@codemirror/view";
import { ColumnData, FlolakeConnectionData, TableData } from "@/api/shared/types";

export const createSchemaCompletion = (
  flolakeData: FlolakeConnectionData[],
  createSystemKey: (sys: FlolakeConnectionData) => string,
) => {
  // Shared helper functions
  //-------------------------------------------------
  function extractTokens(text: string): string[] {
    const tokens: string[] = [];
    let idx = 0;
    while (idx < text.length) {
      const open = text.indexOf("{", idx);
      if (open === -1) break;
      const close = text.indexOf("}", open);
      if (close === -1) break;
      tokens.push(text.slice(open + 1, close));
      idx = close + 1;
    }
    return tokens;
  }

  const getTextBeforeCursor = (context: CompletionContext): string => {
    const { state, pos } = context;
    return state.sliceDoc(0, pos);
  };

  function extractPrefixAndBraces(after: string) {
    if (after.startsWith(".{")) {
      return { hasBraces: true, prefix: after.slice(2) };
    } else if (after.startsWith(".")) {
      return { hasBraces: false, prefix: after.slice(1) };
    }
    return { hasBraces: false, prefix: "" };
  }

  // New helper function to detect if we're editing within existing braces
  function isEditingWithinBraces(context: CompletionContext): {
    withinBraces: boolean;
    closingBracePos: number;
  } {
    const { state, pos } = context;
    const textBefore = state.sliceDoc(0, pos);
    const textAfter = state.sliceDoc(pos);

    // Find the last opening brace before cursor
    const lastOpenBrace = textBefore.lastIndexOf("{");

    // Find the first closing brace after cursor
    const nextCloseBrace = textAfter.indexOf("}");

    // We're editing within braces if:
    // 1. There's an opening brace before cursor
    // 2. There's a closing brace after cursor
    // 3. There's no closing brace between the opening brace and cursor
    if (lastOpenBrace !== -1 && nextCloseBrace !== -1) {
      const textBetween = textBefore.slice(lastOpenBrace + 1);
      const hasClosingBraceInBetween = textBetween.includes("}");

      if (!hasClosingBraceInBetween) {
        return { withinBraces: true, closingBracePos: pos + nextCloseBrace };
      }
    }

    return { withinBraces: false, closingBracePos: -1 };
  }

  // Helper to check if token matches a system
  function isSystemToken(token: string, system: FlolakeConnectionData): boolean {
    if (!token) return false;

    const systemKey = createSystemKey(system);
    return systemKey === token || systemKey.toLowerCase() === token.toLowerCase();
  }

  // Helper to find a table by name
  function findTable(system: FlolakeConnectionData, tableName: string): TableData | undefined {
    return system.tableData.find((tbl) => tbl.tableName.toUpperCase() === tableName?.toUpperCase());
  }

  // Create completion options with consistent formatting
  function createCompletionOptions(
    items: any[],
    prefix: string,
    itemType: "schema" | "table" | "column",
    hasBraces: boolean,
    withinBraces: boolean,
    closingBracePos: number,
    labelFormatter: (item: any) => string,
    valueFormatter: (item: any) => string,
  ) {
    return items
      .filter((item) => labelFormatter(item).toLowerCase().includes(prefix.toLowerCase()))
      .map((item) => ({
        label: labelFormatter(item),
        type: itemType,
        apply: (view: EditorView, completion: Completion, from: number, to: number) => {
          const value = valueFormatter(item);
          const state = view.state;
          const line = state.doc.lineAt(from);
          const lineText = line.text;
          const offset = from - line.from;

          const textAfterCursor = lineText.slice(offset);
          const closingBraceIndex = textAfterCursor.indexOf("}");
          const hasClosingBrace = closingBraceIndex !== -1;

          let insertText: string;
          if (hasBraces) {
            insertText = value;
          } else if (hasClosingBrace) {
            insertText = value;
          } else {
            insertText = `{${value}}`;
          }

          view.dispatch({
            changes: { from, to, insert: insertText },
            selection: { anchor: from + insertText.length, head: from + insertText.length },
          });
          return true;
        },
      }));
  }

  // Handle completion acceptance (shared between Tab and Enter)
  function handleCompletionAccept(view: EditorView) {
    const result = acceptCompletion(view);
    if (result) {
      const { state } = view;
      const pos = state.selection.main.head;
      const line = state.doc.lineAt(pos);
      const textAfter = line.text.slice(pos - line.from);

      if (textAfter.startsWith("}")) {
        view.dispatch({
          selection: { anchor: pos + 1, head: pos + 1 },
        });
      }
    }
    return result;
  }

  // ------------------------------------------------
  const systemCompletions = (context: CompletionContext) => {
    const textBefore = getTextBeforeCursor(context);
    const openBraceIndex = textBefore.lastIndexOf("{");
    if (openBraceIndex === -1) return null;

    const prefix = textBefore.slice(openBraceIndex + 1);

    const systemOptions = flolakeData.flatMap((system) => {
      const systemKey = createSystemKey(system);
      if (!systemKey.toLowerCase().includes(prefix.toLowerCase())) return [];

      return [
        {
          label: systemKey,
          type: "schema",
          apply: (view: EditorView, completion: Completion, from: number, to: number) => {
            const systemName = systemKey;
            const state = view.state;
            const line = state.doc.lineAt(from);
            const lineText = line.text;
            const offset = from - line.from;

            const textAfterCursor = lineText.slice(offset);
            const closingBraceIndex = textAfterCursor.indexOf("}");
            const hasClosingBrace = closingBraceIndex !== -1;

            if (hasClosingBrace) {
              view.dispatch({
                changes: { from, to, insert: systemName },
                selection: {
                  anchor: from + systemName.length + 1,
                  head: from + systemName.length + 1,
                },
              });
            } else {
              view.dispatch({
                changes: { from, to, insert: `${systemName}}` },
                selection: {
                  anchor: from + systemName.length + 1,
                  head: from + systemName.length + 1,
                },
              });
            }
            return true;
          },
        },
      ];
    });

    return { from: context.pos - prefix.length, options: systemOptions };
  };

  const tableCompletions = (context: CompletionContext, tableData: TableData[]) => {
    if (!tableData) return null;

    const textBefore = getTextBeforeCursor(context);
    const lastClose = textBefore.lastIndexOf("}");
    const afterSystem = textBefore.slice(lastClose + 1);
    const { hasBraces, prefix } = extractPrefixAndBraces(afterSystem);
    const { withinBraces, closingBracePos } = isEditingWithinBraces(context);

    const tableOptions = createCompletionOptions(
      tableData,
      prefix,
      "table",
      hasBraces,
      withinBraces,
      closingBracePos,
      (table) => `{${table.tableName.toUpperCase()}}`,
      (table) => table.tableName.toUpperCase(),
    );

    return { from: context.pos - prefix.length, options: tableOptions };
  };

  const columnCompletions = (context: CompletionContext, columns: ColumnData[]) => {
    if (!columns) return null;

    const textBefore = getTextBeforeCursor(context);
    const afterTable = textBefore.split("}").pop() || "";
    const { hasBraces, prefix } = extractPrefixAndBraces(afterTable);
    const { withinBraces, closingBracePos } = isEditingWithinBraces(context);

    const columnOptions = createCompletionOptions(
      columns,
      prefix,
      "column",
      hasBraces,
      withinBraces,
      closingBracePos,
      (column) => `{${column.name.toUpperCase()}}`,
      (column) => column.name.toUpperCase(),
    );

    return { from: context.pos - prefix.length, options: columnOptions };
  };

  const sqlCompletions = (context: CompletionContext) => {
    const textBefore = getTextBeforeCursor(context);
    const wordMatch = textBefore.match(/[\w\d]*$/);
    const typedWord = wordMatch ? wordMatch[0].toLowerCase() : "";

    const languageSources = context.state.languageDataAt("autocomplete", context.pos);
    const sqlResults = languageSources.flatMap((fn: any) => {
      const result = (fn as (ctx: CompletionContext) => CompletionResult | null)(context);
      return result ? result.options : [];
    });

    const filteredSqlResults = typedWord
      ? sqlResults.filter((option: any) => option.label.toLowerCase().includes(typedWord))
      : sqlResults;

    if (filteredSqlResults.length > 0) {
      const wordStart = typedWord ? context.pos - typedWord.length : context.pos;
      return { from: wordStart, options: filteredSqlResults };
    }

    return null;
  };

  function getContext(text: string, flolakeData: FlolakeConnectionData[]) {
    const tokens = extractTokens(text);
    const last = tokens[tokens.length - 1];
    const secondLast = tokens[tokens.length - 2];
    const thirdLast = tokens[tokens.length - 3];

    const lastClose = text.lastIndexOf("}");

    // Check if there's an unclosed brace
    const openBracePos = text.lastIndexOf("{");
    if (openBracePos > lastClose) {
      const textFromOpenBrace = text.slice(openBracePos);
      const nextCloseBrace = textFromOpenBrace.indexOf("}");

      if (nextCloseBrace !== -1) {
        return { context: "system", system: null, table: null };
      }

      const textBeforeBrace = text.slice(0, openBracePos);
      const charBeforeBrace = textBeforeBrace.slice(-1);

      // If there's a period before the brace, it's a table or column reference
      if (charBeforeBrace === ".") {
        // Find the most recent system reference
        const system = flolakeData.find(
          (sys) =>
            isSystemToken(last, sys) ||
            isSystemToken(secondLast, sys) ||
            isSystemToken(thirdLast, sys),
        );

        if (!system) {
          return { context: "system", system: null, table: null };
        }

        // If last token is a system, then we're looking at a table
        if (isSystemToken(last, system)) {
          return { context: "table", system, table: null };
        }

        // If second-to-last token is a system and last is a table, we're looking at a column
        if (isSystemToken(secondLast, system) && last) {
          const table = findTable(system, last);
          return table
            ? { context: "column", system, table }
            : { context: "table", system, table: null };
        }

        return { context: "system", system: null, table: null };
      }

      // If there's no period, it's a system reference
      return { context: "system", system: null, table: null };
    }

    // After a closing brace
    const after = lastClose !== -1 ? text.slice(lastClose + 1) : text;

    // Check if we've moved on to SQL by detecting space after a completed reference
    if (after.match(/^\s+[^.{]/)) {
      return { context: "sql", system: null, table: null };
    }

    // Period after a complete reference indicates start of a table or column reference
    if (after.startsWith(".")) {
      const system = flolakeData.find((sys) => isSystemToken(last, sys));
      if (system) {
        return { context: "table", system, table: null };
      }

      // Check if we're adding a column after a table
      const tableSystem = flolakeData.find((sys) => isSystemToken(secondLast, sys));
      if (tableSystem && last) {
        const table = findTable(tableSystem, last);
        if (table) {
          return { context: "column", system: tableSystem, table };
        }
      }
    }

    // Starting a new reference
    const isSystemContext = after.trim() === "" || after.match(/^\s*\{$/);

    if (isSystemContext) {
      return { context: "system", system: null, table: null };
    }

    // Default to SQL context if nothing else matches
    return { context: "sql", system: null, table: null };
  }

  const combinedCompletions = (context: CompletionContext) => {
    const textBefore = getTextBeforeCursor(context);
    const { context: ctx, system, table } = getContext(textBefore, flolakeData);

    // If we're in SQL context (space after a reference), don't show schema completions
    if (ctx === "sql") {
      return sqlCompletions(context);
    }

    // Try each completion provider in order of context specificity
    const result =
      (ctx === "column" && system && table && columnCompletions(context, table.columns)) ||
      (ctx === "table" && system && tableCompletions(context, system.tableData)) ||
      (ctx === "system" && systemCompletions(context)) ||
      sqlCompletions(context);

    return result;
  };
  return [
    autocompletion({
      activateOnTyping: true,
      defaultKeymap: true,
      override: [combinedCompletions],
      closeOnBlur: true,
    }),
    keymap.of([
      { key: "Tab", run: handleCompletionAccept },
      { key: "Enter", run: handleCompletionAccept },
    ]),
  ];
};
