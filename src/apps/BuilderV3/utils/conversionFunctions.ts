/* eslint-disable no-restricted-syntax */
/* eslint-disable no-useless-escape */
import { FlolakeConnectionData } from "@/api/shared/types";

export const createSystemKey = (system: FlolakeConnectionData): string => {
  return `${system.transformSystem} - ${system.connectionName}`;
};

export const convertTokenToSchema = (
  query: string,
  flolakeData: FlolakeConnectionData[],
): string => {
  if (!query) return "";

  // TODO: We should format this data upon fetching connections
  const systemTableToSchema = new Map<string, Map<string, string>>();
  const systemToSchema = new Map<string, string>();

  for (const system of flolakeData) {
    const systemKey = createSystemKey(system).toLowerCase();
    const tableMap = new Map<string, string>();

    systemToSchema.set(systemKey, system.schemaName.toLowerCase());

    if (system.tableData) {
      for (const table of system.tableData) {
        tableMap.set(table.tableName.toLowerCase(), table.schemaName.toLowerCase());
      }
    }
    systemTableToSchema.set(systemKey, tableMap);
  }

  return query.replace(
    /\{([^}]+)\}\.\{([^}]+)\}(?:\.\{([^}]+)\})?/g,
    (match, systemToken, tableToken, columnToken) => {
      const systemKey = systemToken.toLowerCase();
      const tableName = tableToken.toLowerCase();
      const tableMap = systemTableToSchema.get(systemKey);

      if (tableMap && tableMap.has(tableName)) {
        const schema = tableMap.get(tableName);
        let result = `${schema}.${tableToken}`;
        if (columnToken) {
          result += `.${columnToken}`;
        }
        return result;
      }

      const systemSchema = systemToSchema.get(systemKey);
      if (systemSchema) {
        let result = `${systemSchema}.${tableToken}`;
        if (columnToken) {
          result += `.${columnToken}`;
        }
        return result;
      }

      let result = `${systemToken}.${tableToken}`;
      if (columnToken) {
        result += `.${columnToken}`;
      }
      return result;
    },
  );
};

export const convertVariablesToSnowflake = (
  query: string,
  taskInputs: Array<{ id: string; name: string; type: string }>,
  variableIdMapping?: Record<string, string>,
): { convertedQuery: string; bindMapping: string[] } => {
  if (!query) return { convertedQuery: "", bindMapping: [] };

  const bindMapping: string[] = [];
  const variableRegex = /\{\$([^}]+)\}/g;
  let parameterIndex = 1;

  const variableNameCounts: Record<string, number> = {};

  const convertedQuery = query.replace(variableRegex, (match, variableName) => {
    const numberedMatch = variableName.match(/^(.+)_(\d+)$/);
    const baseVariableName = numberedMatch ? numberedMatch[1] : variableName;
    const explicitIndex = numberedMatch ? parseInt(numberedMatch[2], 10) : null;

    variableNameCounts[baseVariableName] = (variableNameCounts[baseVariableName] || 0) + 1;
    const occurrenceIndex = variableNameCounts[baseVariableName];

    let taskInput;

    if (variableIdMapping && variableIdMapping[variableName]) {
      taskInput = taskInputs.find((input) => input.id === variableIdMapping[variableName]);
    }

    if (!taskInput) {
      const matchingInputs = taskInputs.filter((input) => input.name === baseVariableName);

      if (matchingInputs.length === 1) {
        taskInput = matchingInputs[0];
      } else if (matchingInputs.length > 1) {
        let inputIndex;

        if (explicitIndex !== null) {
          inputIndex = Math.min(explicitIndex - 1, matchingInputs.length - 1);
        } else {
          inputIndex = Math.min(occurrenceIndex - 1, matchingInputs.length - 1);
        }

        taskInput = matchingInputs[inputIndex];

        console.warn(
          `Multiple inputs found with name "${baseVariableName}". Using ${explicitIndex ? `explicit index ${explicitIndex}` : `occurrence ${occurrenceIndex}`} → input ID: ${taskInput.id}`,
        );
      }

      if (taskInput && variableIdMapping) {
        console.warn(
          `Variable "${variableName}" not found in mapping, fell back to name matching with ID: ${taskInput.id}`,
        );
      }
    }

    if (taskInput) {
      bindMapping.push(taskInput.id);
      return `:${parameterIndex++}`;
    }

    console.warn(`No matching task input found for variable: ${variableName}`);
    return match;
  });

  return { convertedQuery, bindMapping };
};

export const convertSnowflakeToVariables = (
  query: string,
  bindMapping: string[],
  taskInputs: Array<{ id: string; name: string; type: string }>,
): string => {
  if (!query || !bindMapping.length) return query;

  let convertedQuery = query;

  const inputsByName: Record<string, Array<{ id: string; name: string; type: string }>> = {};
  taskInputs.forEach((input) => {
    if (!inputsByName[input.name]) {
      inputsByName[input.name] = [];
    }
    inputsByName[input.name].push(input);
  });

  bindMapping.forEach((taskInputId, index) => {
    const taskInput = taskInputs.find((input) => input.id === taskInputId);
    if (taskInput) {
      const parameterPattern = new RegExp(`:${index + 1}\\b`, "g");

      const inputsWithSameName = inputsByName[taskInput.name];
      let variableName = taskInput.name;

      if (inputsWithSameName && inputsWithSameName.length > 1) {
        const inputIndex = inputsWithSameName.findIndex((input) => input.id === taskInputId);
        if (inputIndex >= 0) {
          variableName = `${taskInput.name}_${inputIndex + 1}`;
        }
      }

      convertedQuery = convertedQuery.replace(parameterPattern, `{$${variableName}}`);
    } else {
      console.warn(`    No task input found for ID: ${taskInputId}`);
    }
  });

  return convertedQuery;
};

export const convertSchemaToToken = (
  query: string,
  flolakeData: FlolakeConnectionData[],
): string => {
  if (!query) return "";
  // TODO: We should format this data upon fetching connections
  const schemaToToken = new Map<string, string>();

  for (const system of flolakeData) {
    const systemKey = createSystemKey(system);

    if (system.tableData) {
      for (const table of system.tableData) {
        schemaToToken.set(table.schemaName.toLowerCase(), systemKey);
        schemaToToken.set(system.schemaName.toLowerCase(), systemKey);
      }
    }
  }

  const sortedSchemas = Array.from(schemaToToken.keys()).sort((a, b) => b.length - a.length);

  if (sortedSchemas.length === 0) return query;

  const schemaPattern = sortedSchemas
    .map((schema) => schema.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&"))
    .join("|");

  const regex = new RegExp(
    `\\b(${schemaPattern})(?:\\.([A-Za-z0-9_]+))?(?:\\.([A-Za-z0-9_]+))?\\b`,
    "gi",
  );

  return query.replace(regex, (match, schema, table, column) => {
    const schemaLower = schema.toLowerCase();
    const systemKey = schemaToToken.get(schemaLower);

    if (!systemKey) return match;

    let result = `{${systemKey}}`;
    if (table) {
      result += `.{${table.toUpperCase()}}`;
    }
    if (column) {
      result += `.{${column.toUpperCase()}}`;
    }
    return result;
  });
};
