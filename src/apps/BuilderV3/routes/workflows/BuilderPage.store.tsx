import { atom } from "jotai";

export const selectedFileAtom = atom<{
  id: string;
  name: string;
  type: string;
} | null>(null);

export const selectedRangeAtom = atom<string | undefined>();
export const activeSheetAtom = atom<string | undefined>();

export type ViewType = "split" | "chat" | "file";
export const collapsedPanelAtom = atom<ViewType | null>(null);
export type PanelView = "Chat" | "File" | "Steps" | "Inputs";

export const fileAtom = atom<File | null>(null);

export const versionOptionsOpenAtom = atom(false);

export const shouldRefetchTasksAtom = atom(false);
