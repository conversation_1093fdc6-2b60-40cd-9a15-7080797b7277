import {
  Accordion,
  DropdownButton,
  DropdownPanel,
  Heading,
  Input,
  Text,
  TextArea,
} from "@floqastinc/flow-ui_core";
import { Entity, Experiment, Workflow } from "@floqastinc/transform-v3";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { match } from "ts-pattern";
import * as Styled from "../styled";
import { t } from "@/utils/i18n";
import { v3, ApiError } from "@/services/v3";
import { useFeatureFlags } from "@/components/FeatureFlag";
import { Modes } from "@Transform/components/SettingsSideDrawer/ui/Modes";
import { Experiments } from "@Transform/components/Experiments";

type WorkflowFormProps = {
  workflow: Pick<Workflow, "name" | "description" | "entityId" | "settings" | "id">;
  setWorkflow: (workflow: Pick<Workflow, "name" | "description" | "entityId" | "settings">) => void;
  experimentAssignments: Record<string, string>;
  setExperimentAssignments: React.Dispatch<React.SetStateAction<Record<string, string>>>;
};

export const WorkflowForm = ({
  workflow,
  setWorkflow,
  setExperimentAssignments,
}: WorkflowFormProps) => {
  const [workflowNameError, setWorkflowNameError] = useState("");
  const [selectedEntity, setSelectedEntity] = useState<string | null>(null);
  const [entityDropdownIsOpen, setEntityDropdownIsOpen] = useState(false);
  const [openExperimentDropdown, setOpenExperimentDropdown] = useState<string | null>(null);

  // Feature flags
  const { getFlag } = useFeatureFlags();
  const transformExperimentsEnabled = getFlag("enable-experiments", false);

  const {
    isLoading,
    isError,
    error,
    data: entityOptions = [],
  } = useQuery<Entity[], Error>({
    queryKey: ["entities", "list"],
    queryFn: async () => {
      const response = await v3.entities.getEntities();
      if (response.errors.length) {
        throw new ApiError(response.errors);
      }
      return response.data;
    },
    retry: false,
  });

  const handleEntityChange = (value: string) => {
    setSelectedEntity(value);
    setWorkflow({ ...workflow, entityId: value });
    setEntityDropdownIsOpen(false);
  };

  const onExperimentAssignmentsChange = (experimentName: string, variantId: string) => {
    setExperimentAssignments((prev) => ({
      ...prev,
      [experimentName]: variantId,
    }));
  };

  const handleSettings = (name: keyof Workflow["settings"], value: boolean) => {
    setWorkflow({
      ...workflow,
      settings: {
        ...workflow.settings,
        [name]: value,
      },
    });
  };

  return (
    <Styled.WorkflowForm>
      <Heading variant="h5">{t("components.WorkflowForm.nameAgent")}</Heading>
      <Styled.WorkFlowFormInput>
        <Input
          label={t("components.WorkflowForm.agentName")}
          isRequired
          data-testid="agent-name-input"
          onChange={(name: string) => {
            if (name) setWorkflowNameError("");
            setWorkflow({ ...workflow, name, entityId: selectedEntity ?? "" });
          }}
          placeholder={t("components.WorkflowForm.agentName")}
          value={workflow.name}
          onBlur={() => {
            if (!workflow.name) {
              setWorkflowNameError(t("components.WorkflowForm.Errors.agentNameRequired"));
            }
          }}
          isInvalid={workflowNameError}
        />
        <DropdownPanel
          onChange={handleEntityChange}
          onOpenChange={setEntityDropdownIsOpen}
          isOpen={entityDropdownIsOpen}
          selectedValue={selectedEntity}
          disableClear={true}
          data-testid="entity-dropdown-panel"
        >
          <DropdownPanel.Trigger>
            <DropdownButton
              isRequired
              label={t("components.WorkflowForm.entity")}
              variant="default"
              open={entityDropdownIsOpen}
              data-testid="entity-dropdown-button"
            >
              {match({ isLoading, isError, data: entityOptions })
                .with({ isLoading: true }, () => t("components.WorkflowForm.loading"))
                .with({ data: [] }, () => t("components.WorkflowForm.noDataAvailable"))
                .with({ isError: true }, () =>
                  t("components.WorkflowForm.Errors.errorFetchingEntities", {
                    error: error?.message,
                  }),
                )
                .otherwise(() =>
                  selectedEntity
                    ? entityOptions.find((option) => option.id === selectedEntity)?.name
                    : t("components.WorkflowForm.selectEntity"),
                )}
            </DropdownButton>
          </DropdownPanel.Trigger>

          <DropdownPanel.Content
            portal={false}
            style={{ maxHeight: "300px", overflowY: "auto", textAlign: "left" }}
            data-testid="entity-dropdown-content"
          >
            {match({ isLoading, isError, data: entityOptions })
              .with({ isLoading: true }, () => (
                <DropdownPanel.Option value="loading" disabled data-testid="entity-loading-option">
                  {t("components.WorkflowForm.loading")}
                </DropdownPanel.Option>
              ))
              .with({ isError: true }, () => (
                <DropdownPanel.Option value="error" disabled data-testid="entity-error-option">
                  {t("components.WorkflowForm.Errors.errorFetchingEntities")} {error?.message}
                </DropdownPanel.Option>
              ))
              .otherwise(() =>
                entityOptions.length > 0 ? (
                  entityOptions.map((entity) => (
                    <DropdownPanel.Option
                      key={entity.id}
                      value={entity.id}
                      data-testid={`entity-option-${entity.id}`}
                    >
                      {entity.name}
                    </DropdownPanel.Option>
                  ))
                ) : (
                  <DropdownPanel.Option
                    value="no-data"
                    disabled
                    data-testid="entity-no-data-option"
                  >
                    {t("components.WorkflowForm.noEntityAvailable")}
                  </DropdownPanel.Option>
                ),
              )}
          </DropdownPanel.Content>
        </DropdownPanel>
        <TextArea
          styleOverrides={{
            root: {
              width: "100%",
            },
            textarea: {
              width: "100%",
            },
          }}
          data-testid="workflow-description-textarea"
          isLabelVisible
          label={t("components.WorkflowForm.optionalDescription")}
          onChange={(description: string) => {
            setWorkflow({ ...workflow, description });
          }}
          placeholder={t("components.WorkflowForm.description")}
          value={workflow.description}
        />
      </Styled.WorkFlowFormInput>
      <Styled.AdvancedSetup>
        <Accordion collapsible>
          <Accordion.Item value="advancedSetup">
            <Accordion.Trigger>{t("components.WorkflowForm.advancedSetup")}</Accordion.Trigger>
            <Accordion.Content>
              <Modes settings={workflow.settings} setSettings={handleSettings} />
              <Experiments workflowId={workflow.id} onChange={onExperimentAssignmentsChange} />
            </Accordion.Content>
          </Accordion.Item>
        </Accordion>
      </Styled.AdvancedSetup>
    </Styled.WorkflowForm>
  );
};
