import { DropdownPanel } from "@floqastinc/flow-ui_core";
import { t } from "@/utils/i18n";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { useNavigate, useParams } from "react-router";
import { getExamplesQuery } from "@BuilderV3/api/examples";

type VersionDropdownProps = {
  taskId: string;
  selectedExampleId?: string;
  onChange: (newExampleId: string) => void;
  trigger: (selectedValue?: string, versionNo?: number) => React.ReactNode;
};
export const VersionDropdown = ({
  taskId,
  selectedExampleId,
  onChange,
  trigger,
}: VersionDropdownProps) => {
  const { workflowId } = useParams();

  if (!workflowId) {
    console.error(t("components.VersionDropdown.Errors.noIDsprovided"));
    throw new Error(t("components.VersionDropdown.Errors.noIDsprovided"));
  }

  const [isOpen, setIsOpen] = useState(false);

  const examplesQuery = useQuery(getExamplesQuery({ workflowId, taskId }));

  const dropdownOptions =
    examplesQuery.data?.map((example) => {
      return {
        id: example.id,
        name:
          example.name ||
          `Version ${example.createdAt.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
            hour: "numeric",
            minute: "2-digit",
            second: "2-digit",
          })}`,
      };
    }) ?? [];

  const indexOfSelectedExample =
    examplesQuery.data?.findIndex((example) => example.id === selectedExampleId) ?? -1;
  const selectedExample = examplesQuery.data?.[indexOfSelectedExample];

  const indexOfActiveExample =
    examplesQuery.data?.findIndex((example) => example.id === selectedExampleId) ?? -1;
  const activeExample = examplesQuery.data?.[indexOfActiveExample];

  const latestExample = examplesQuery.data?.[examplesQuery.data?.length - 1];
  const indexOfLatestExample = (examplesQuery.data?.length ?? 0) - 1;

  const exampleToUse = selectedExample ?? activeExample ?? latestExample;
  const indexOfExampleToUse =
    indexOfSelectedExample !== -1
      ? indexOfSelectedExample
      : indexOfActiveExample !== -1
        ? indexOfActiveExample
        : indexOfLatestExample;

  return (
    <DropdownPanel
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      disableClear
      disableFilter
      onChange={(newExampleId: string) => {
        setIsOpen(false);
        onChange(newExampleId);
      }}
      selectionMode="single"
      selectedValues={exampleToUse?.id}
    >
      <DropdownPanel.Trigger>
        {trigger(exampleToUse?.id, indexOfExampleToUse + 1)}
      </DropdownPanel.Trigger>
      <DropdownPanel.Content>
        {dropdownOptions.map((option) => {
          return (
            <DropdownPanel.Option key={option.id} value={option.id}>
              {option.name}
            </DropdownPanel.Option>
          );
        })}
      </DropdownPanel.Content>
    </DropdownPanel>
  );
};
