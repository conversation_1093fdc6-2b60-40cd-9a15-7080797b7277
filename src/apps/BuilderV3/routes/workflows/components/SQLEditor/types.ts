import { FloLakeStoredQueryBindMapping } from "@floqastinc/transform-v3";

export interface DropdownOptions {
  id: string;
  name: string;
  bindMapping?: FloLakeStoredQueryBindMapping[];
}

export interface InputValue {
  name: string;
  description: string;
  kind: string;
  value: string | number;
  isGenerated: boolean;
}

export interface AddInputResponse {
  data: {
    taskId: string;
    taskInputId: string;
  };
}

export interface AddInputVariables {
  name: string;
  description: string;
  type: string;
  value: string | number;
}

export interface SQLEditorProps {
  selectedQuery: DropdownOptions;
  setSelectedQuery: (query: DropdownOptions) => void;
  setInputValues: React.Dispatch<React.SetStateAction<InputValue[]>>;
  inputValues: InputValue[];
  currentQuery: string;
  setCurrentQuery: (query: string) => void;
  setFileIsLoading: (isLoading: boolean) => void;
  setSelectedView: (view: string) => void;
}
