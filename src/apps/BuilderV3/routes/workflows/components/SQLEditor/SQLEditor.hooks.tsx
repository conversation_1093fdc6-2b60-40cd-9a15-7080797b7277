import { NewWorkflowInput } from "@floqastinc/transform-v3";
import { useMutation } from "@tanstack/react-query";
import { t } from "@/utils/i18n";
import { useParams } from "react-router-dom";
import { queryClient } from "@/components/queryClient";
import v3, { ApiError } from "@/services/v3";
import { queryKeys } from "@BuilderV3/api/query-keys";
type WorkflowInputData = NewWorkflowInput & {
  value: any;
};

export const useInputQueries = () => {
  const { workflowId, taskId, exampleSetId } = useParams();
  if (!workflowId || !taskId || !exampleSetId) {
    console.error(t("components.SQLEditor.Errors.noIDsprovided"));
    throw new Error(t("components.SQLEditor.Errors.noIDsprovided"));
  }
  const addInputMutation = useMutation({
    mutationKey: [
      "addInput",
      {
        workflowId,
        taskId,
        exampleSetId,
      },
    ],
    mutationFn: async (input: WorkflowInputData) => {
      const workflowInputRes = await v3.workflowInputs.createWorkflowInput({
        workflowId,
        input: {
          name: input.name,
          type: input.type,
        },
      });
      if (workflowInputRes.errors?.length) {
        throw new ApiError(workflowInputRes.errors);
      }
      if (!workflowInputRes.data) {
        throw new Error(t("components.SQLEditor.Errors.noDataReturnedCWI"));
      }

      const { data: workflowInput } = workflowInputRes;

      const taskInputRes = await v3.taskInputs.createTaskInput({
        workflowId,
        taskId,
        input: {
          name: input.name,
          type: input.type,
          description: input.description,
          source: {
            workflowInputId: workflowInput.id,
          },
        },
      });
      if (taskInputRes.errors?.length) {
        throw new ApiError(taskInputRes.errors);
      }
      if (!taskInputRes.data) {
        throw new Error(t("components.SQLEditor.Errors.noDataReturnedCTI"));
      }

      const exampleInputResponse = await v3.exampleInputs.createExampleInput({
        workflowId,
        taskId,
        exampleSetId,
        input: {
          taskInputId: taskInputRes.data?.id,
        },
      });
      if (exampleInputResponse.errors?.length) {
        throw new ApiError(exampleInputResponse.errors);
      }
      if (!exampleInputResponse.data) {
        throw new Error(t("components.SQLEditor.Errors.noDataReturnedCEI"));
      }
      const exampleInput = exampleInputResponse.data;

      if (input.type === "TEXT") {
        const setTextInputRes = await v3.exampleInputs.setExampleInputText({
          workflowId,
          taskId,
          exampleSetId,
          exampleInputId: exampleInput.id,
          value: {
            kind: "TEXT",
            value: input.value,
          },
        });
        if (setTextInputRes.errors?.length) {
          throw new ApiError(setTextInputRes.errors);
        }

        return setTextInputRes;
      }

      if (input.type === "NUMBER") {
        const setNumberInputRes = await v3.exampleInputs.setExampleInputNumber({
          workflowId,
          taskId,
          exampleSetId,
          exampleInputId: exampleInput.id,
          value: {
            kind: "NUMBER",
            value: input.value,
          },
        });
        if (setNumberInputRes.errors?.length) {
          throw new ApiError(setNumberInputRes.errors);
        }

        return setNumberInputRes;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.workflowInputs.byWorkflow(workflowId),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.taskInputs.byTask({ workflowId, taskId }),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.exampleInputs.getExampleInputs({
          workflowId,
          taskId,
          exampleSetId,
        }),
      });
    },
    onError: (error) => {
      console.error("error", error);
    },
  });
  return {
    addInputMutation,
  };
};
