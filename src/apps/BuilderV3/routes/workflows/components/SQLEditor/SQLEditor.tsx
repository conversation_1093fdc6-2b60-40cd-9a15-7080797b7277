import { sql } from "@codemirror/lang-sql";
import { useMutation, UseMutationResult, useQuery } from "@tanstack/react-query";
import { t } from "@/utils/i18n";
import { basicSetup, EditorView } from "codemirror";
import { Compartment, StateEffect } from "@codemirror/state";
import { Parser } from "node-sql-parser";
import { useEffect, useMemo, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router";
import { Button, Flex, Spinner, useToast, Toggle, TextArea } from "@floqastinc/flow-ui_core";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { styled } from "styled-components";
import { QueryDropdown } from "../QueryDropdown/QueryDropdown";
import { SourceTreeView } from "./SourceTreeView";
import { InputValue, AddInputResponse, AddInputVariables, SQLEditorProps } from "./types";
import { useInputQueries } from "./SQLEditor.hooks";
import { getExampleQuery } from "@BuilderV3/api/examples";
import { queryKeys } from "@BuilderV3/api/query-keys";
import { getExampleOutputsQuery } from "@BuilderV3/api/example-outputs";
import { useUpdateInputValues } from "@BuilderV3/app-components/Inputs/useUpdatedInputValues";
import { createSchemaReferenceExtensions } from "@BuilderV3/utils/schemaReferenceExtensions";
import { convertSchemaToToken, convertTokenToSchema } from "@BuilderV3/utils/conversionFunctions";
import { ConnectionsProvider } from "@Transform/pages/connections/ConnectionsProvider";
import {
  hasTooltipWarning,
  tooltipErrorMessage,
  scanQueryForWarnings,
} from "@BuilderV3/utils/tooltip";
import { ResizeHandle } from "@BuilderV3/routes/workflows/components/ResizeHandle/ResizeHandle";
import { getWorkflowTaskQuery } from "@BuilderV3/api/tasks";
import { useFlolakeData } from "@/api/shared/connections-query";
import { queryClient } from "@/components";
import v3 from "@/services/v3";

const SQLEditorWrapper = styled.div`
  width: 100%;
  height: 40vh;
  max-height: 40vh;
  position: relative;
  overflow: auto;
  .cm-editor {
    height: 100%;
    border: 1px solid #ddd;
    &.cm-focused {
      outline: none;
    }
  }
  &.loading-schema::before {
    content: "Loading data...";
    position: absolute;
    bottom: 8px;
    right: 8px;
    color: var(--flo-base-color-neutral-500);
    font-family: var(--flo-sem-font-family-body);
    opacity: 0.7;
    padding: 4px 8px;
  }
`;

const customEditorTheme = EditorView.theme({
  "&": {
    fontSize: "14px",
  },
  ".cm-content": {
    fontFamily: "monospace",
    lineHeight: "1.6",
    backgroundColor: "var(--flo-sem-color-surface-neutral-base)",
  },
});

export const SQLEditor = ({
  selectedQuery,
  setSelectedQuery,
  setInputValues,
  inputValues,
  currentQuery,
  setCurrentQuery,
  setFileIsLoading,
  setSelectedView,
}: SQLEditorProps) => {
  const { workflowId = "", taskId = "", exampleSetId = "" } = useParams();
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isQueryConverting, setIsQueryConverting] = useState<boolean>(true);
  const [isEditorMode, setIsEditorMode] = useState<boolean>(false);
  const [generationPrompt, setGenerationPrompt] = useState<string>("");
  const [isGeneratingSql, setIsGeneratingSql] = useState<boolean>(false);

  if (!workflowId || !taskId || !exampleSetId) {
    console.warn(t("components.SQLEditor.Errors.noIDsprovided"));
    navigate("/builder");
  }

  const { data: allFlolakeData, isLoading: isFlolakeDataLoading } = useFlolakeData();

  const connections = useMemo(() => {
    // Filter connections: must be active and have valid table structure
    const filteredConnections =
      allFlolakeData?.data.connections.filter(
        (connection) =>
          // Check for active connection status
          (connection.connectionInfo?.status === "active" || !connection.connectionInfo?.status) &&
          // Verify table structure exists
          connection.tableData &&
          connection.tableData.length > 0 &&
          // Ensure tables have column definitions
          connection.tableData.every((table) => table.columns && table.columns.length > 0),
      ) || [];

    return filteredConnections;
  }, [allFlolakeData]);

  useUpdateInputValues({
    setInputValues,
  });

  const { addInputMutation } = useInputQueries();
  const { showToast, Toast } = useToast();
  const sqlParser = useMemo(() => new Parser(), []);
  const taskQuery = useQuery(getWorkflowTaskQuery({ workflowId, taskId }));
  const currentTask = taskQuery.data;
  const taskStrategy = currentTask?.strategy;

  const exampleOutputsQuery = useQuery(
    getExampleOutputsQuery({ workflowId, taskId, exampleSetId }),
  );

  const exampleSetQuery = useQuery({
    ...getExampleQuery({
      workflowId,
      taskId,
      exampleSetId,
    }),
  });

  const exampleStrategyData = exampleSetQuery.data;
  const exampleStrategy = exampleSetQuery.data?.strategy;
  const isDraft = exampleSetQuery.data?.status === "DRAFT";

  const schemaExtensionsRef = useRef(new Compartment());
  const containerRef = useRef<HTMLDivElement | null>(null);
  const editorRef = useRef<EditorView | null>(null);
  const createExtensions = (isEditable: boolean) => [
    basicSetup,
    EditorView.lineWrapping,
    customEditorTheme,
    EditorView.updateListener.of((update) => {
      if (update.docChanged) {
        const query = update.state.doc.toString();
        setCurrentQuery(query);
      }
    }),
    sql({ upperCaseKeywords: true }),
    schemaExtensionsRef.current.of([]),
    EditorView.editable.of(isEditable),
  ];

  useEffect(() => {
    if (!containerRef.current || editorRef.current) return;

    const view = new EditorView({
      doc: "",
      extensions: createExtensions(false),
      parent: containerRef.current,
    });

    editorRef.current = view;

    return () => {
      view.destroy();
      editorRef.current = null;
    };
  }, [!!containerRef.current]);

  useEffect(() => {
    const editorInstance = editorRef.current;
    if (!editorInstance) return;
    const isEditable = isDraft && !isQueryConverting;

    editorInstance.dispatch({
      effects: StateEffect.reconfigure.of(createExtensions(isEditable)),
    });
  }, [isDraft, isQueryConverting]);

  useEffect(() => {
    setGenerationPrompt("");
  }, [isDraft, exampleSetId]);

  useEffect(() => {
    const editorInstance = editorRef.current;

    if (
      connections &&
      editorInstance &&
      exampleStrategy &&
      taskStrategy &&
      exampleStrategy?.kind === "FLOLAKE"
    ) {
      editorInstance.dispatch({
        effects: schemaExtensionsRef.current.reconfigure(
          createSchemaReferenceExtensions(connections),
        ),
      });

      const statement =
        exampleStrategy.statement ||
        (taskStrategy?.kind === "FLOLAKE" ? taskStrategy.statement : "");

      const hasValidConnections = connections && connections.length > 0;

      let tokenizedQuery: string;

      if (hasValidConnections) {
        tokenizedQuery = convertSchemaToToken(statement, connections);
      } else if (!isFlolakeDataLoading) {
        tokenizedQuery = statement;
      } else {
        return;
      }

      const wasEditable = editorInstance.state.facet(EditorView.editable);
      if (!wasEditable) {
        // Temporarily enable editing
        editorInstance.dispatch({
          effects: StateEffect.reconfigure.of(createExtensions(true)),
        });
      }
      editorInstance.dispatch({
        changes: {
          from: 0,
          to: editorInstance.state.doc.length,
          insert: tokenizedQuery,
        },
      });

      if (!wasEditable) {
        editorInstance.dispatch({
          effects: StateEffect.reconfigure.of(createExtensions(false)),
        });
      }

      setIsQueryConverting(false);
    }
  }, [connections, exampleStrategy, taskStrategy, isFlolakeDataLoading]);

  const handleApply = () => {
    const editorInstance = editorRef.current;
    if (!editorInstance || !selectedQuery?.name) return;

    editorInstance.dispatch({
      changes: {
        from: 0,
        to: editorInstance.state.doc.length,
        insert: selectedQuery.name,
      },
    });

    editorInstance.focus();
  };

  const validateSQL = (sql: string): boolean => {
    try {
      // TODO: dc - this is a hack to replace the bindmapping with dummy values so that the sql parser can parse it
      const sqlWithDummyValues = sql.replace(/:\d+/g, "1");
      sqlParser.astify(sqlWithDummyValues, {
        database: "snowflake",
      });
      return true;
    } catch {
      return false;
    }
  };

  const extractSourcesFromQuery = (sqlQuery: string): string[] => {
    // Look for TLC format with underscores and extract the source name
    // Example: TLC_66CA67A8F9B2F84CB05E2791_5T_67C1F3BE664390427180208A_ERP_COUPA.ACCOUNT_TYPE
    // TODO: change this
    const sourceRegex = /TLC_[A-Z0-9]+_[A-Z0-9]+_[A-Z0-9]+_ERP_([A-Z]+)\./gi;
    const sources = new Set<string>();

    let match;
    while ((match = sourceRegex.exec(sqlQuery)) !== null) {
      const sourceName = match[1].toUpperCase();
      sources.add(sourceName);
    }

    return Array.from(sources);
  };

  const updateExampleMutation = useMutation({
    mutationFn: async ({ query, bindMapping }: { query: string; bindMapping: string[] }) => {
      if (!exampleStrategyData) {
        throw new Error(t("components.SQLEditor.Errors.taskNotFound"));
      }

      const sources = extractSourcesFromQuery(query);

      const updatedExample = {
        name: exampleStrategyData.name,
        status: exampleStrategyData.status,
        strategy: {
          kind: "FLOLAKE" as const,
          statement: query,
          bindMapping: bindMapping,
          sources: sources as any,
        },
      };

      return v3.examples.updateExample({
        workflowId,
        taskId,
        exampleSetId,
        example: updatedExample,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.workflows.byId(workflowId),
      });
    },
    onError: (data) => {
      console.error(t("components.SQLEditor.Errors.errorUpdatingTask"), data);
    },
  });

  const executeFlolakeSQL = useMutation({
    mutationFn: async ({ exampleOutputId }: { exampleOutputId: string }) => {
      const response = await v3.flolakeExecuteService.executeFlolakeSQL({
        workflowId,
        taskId,
        exampleSetId,
        exampleOutputId: exampleOutputId,
      });

      if (response?.errors?.length) {
        console.error(response.errors);
        throw new Error(t("components.SQLEditor.Errors.badNetworkResponse"));
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.exampleOutputs.getExampleOutputs({
          workflowId,
          taskId,
          exampleSetId,
        }),
      });
      queryClient.invalidateQueries({
        queryKey: [
          {
            resource: "exampleOutputs",
            params: { workflowId, taskId, exampleSetId },
          },
        ],
      });
    },
    onError: (error) => {
      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.SQLEditor.taskRunFailed")}</Toast.Title>
          <Toast.Message>{t("components.SQLEditor.pleaseCheckQuery")}</Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
      console.error(t("components.SQLEditor.Errors.errorExecutingWorkflow"), error);
    },
  });

  // UPDATE
  const handleInputs = async (
    inputValues: InputValue[],
    workflowId: string,
    taskId: string,
    addInputMutation: UseMutationResult<AddInputResponse, AddInputVariables, unknown>,
  ): Promise<{ body: any; bindMapping: string[] } | undefined> => {
    let bindMapping: string[] = [];
    const updatedVals: {
      workflowInputId?: string;
      name: string;
      description: string;
      kind: string;
      value: string | number;
      isGenerated: boolean;
    }[] = [];

    try {
      // Step 1: Add new inputs to the workflow
      for (const input of inputValues) {
        if (!input.isGenerated) {
          // this needs to be updated for examples
          const res = await addInputMutation.mutateAsync({
            name: input.name,
            description: input.description,
            type: input.kind,
            value: input.kind === "NUMBER" ? Number(input.value) : input.value,
          });

          const { data: taskInput } = await v3.taskInputs.getTaskInput({
            workflowId,
            taskId: res.data.taskId,
            taskInputId: res.data.taskInputId,
          });

          const updatedInput = {
            ...input,
            workflowInputId: taskInput.source.workflowInputId,
          };
          updatedVals.push(updatedInput);
        } else {
          updatedVals.push(input);
        }
      }

      // Step 2: Update the body with the new inputs
      const body = {
        inputs: updatedVals.map((input) => ({
          workflowInputId: input.workflowInputId,
          name: input.name,
          description: input.description,
          type: input.kind,
          value: {
            value: input.kind === "NUMBER" ? Number(input.value) : input.value,
            kind: input.kind,
          },
        })),
      };

      // Step 3: Get the bind mapping for the inputs
      const { data: taskInputs } = await v3.taskInputs.getTaskInputs({
        workflowId,
        taskId,
      });

      const validTypes = ["TEXT", "NUMBER"];
      bindMapping = taskInputs
        .filter((input) => validTypes.includes(input.type))
        .map((input) => input.id);

      return { body, bindMapping };
    } catch (error) {
      console.error("Error handling inputs:", error);
    }
  };

  const validateInputs = (query: string, inputValues: InputValue[]): boolean => {
    if (!query) {
      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.SQLEditor.noSQLQuery")}</Toast.Title>
          <Toast.Message>{t("components.SQLEditor.enterSQLQuery")}</Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
      return false;
    }

    if (!validateSQL(query)) {
      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.SQLEditor.invalidSQLQuery")}</Toast.Title>
          <Toast.Message>{t("components.SQLEditor.checkSQLSyntax")}</Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
      return false;
    }

    for (const input of inputValues) {
      if (
        !input.name ||
        !input.description ||
        !input.kind ||
        input.value === undefined ||
        input.value === ""
      ) {
        showToast(
          <Toast type="error">
            <Toast.Title>{t("components.SQLEditor.incompleteInput")}</Toast.Title>
            <Toast.Message>{t("components.SQLEditor.ensureInputsFilled")}</Toast.Message>
          </Toast>,
          { position: "top-right" },
        );
        return false;
      }
    }

    return true;
  };

  const handleExecute = async (query: string, generated?: boolean) => {
    try {
      setIsLoading(true);
      setFileIsLoading(true);

      scanQueryForWarnings(query, connections);

      if (hasTooltipWarning) {
        showToast(
          <Toast type="error">
            <Toast.Title>{t("components.SQLEditor.Errors.schemaError")}</Toast.Title>
            <Toast.Message>{tooltipErrorMessage}</Toast.Message>
          </Toast>,
          { position: "top-right" },
        );
        setIsLoading(false);
        setFileIsLoading(false);
        return;
      }

      // what will happen if the query doesnt need conversion?
      let convertedQuery = query;
      if (!generated) {
        convertedQuery = convertTokenToSchema(query, connections);
      }

      const isValid = validateInputs(convertedQuery, inputValues);
      if (!isValid) {
        setIsLoading(false);
        setFileIsLoading(false);
        return;
      }

      setSelectedView("File");

      const result = await handleInputs(inputValues, workflowId, taskId, addInputMutation);

      if (!result) {
        throw new Error(t("components.SQLEditor.Errors.errorHandlingInputs"));
      }

      const { body, bindMapping } = result;

      await updateExampleMutation.mutateAsync({ query: convertedQuery, bindMapping });

      const exampleOutputId = exampleOutputsQuery.data?.[0]?.id;

      if (!exampleOutputId) {
        throw new Error(t("components.SQLEditor.Errors.noExampleOutputID"));
      }

      await executeFlolakeSQL.mutateAsync({ exampleOutputId });

      showToast(
        <Toast type="success">
          <Toast.Title>{t("components.SQLEditor.taskRunSuccess")}</Toast.Title>
          <Toast.Message>{t("components.SQLEditor.taskBeenRunSuccessfully")}</Toast.Message>
        </Toast>,
        { position: "top-right" },
      );

      setIsLoading(false);
      setFileIsLoading(false);
    } catch (error) {
      console.error(t("components.SQLEditor.Errors.errorExecutingFlolake"), error);
      setIsLoading(false);
      setFileIsLoading(false);
    }
  };

  const handleToggleChange = (checked: boolean) => {
    setIsEditorMode(checked);
  };

  const generateSQLMutation = useMutation({
    mutationFn: async (prompt: string) => {
      const response = await v3.nlToSqlService.nlSql({
        nlsql: {
          naturalLanguageQuery: prompt,
        },
      });

      if (!response || response.errors?.length) {
        throw new Error("Failed to generate SQL");
      }

      return response.data?.sql;
    },
    onSuccess: () => {
      showToast(
        <Toast type="success">
          <Toast.Title>{t("components.SQLEditor.sqlGenerated")}</Toast.Title>
          <Toast.Message>{t("components.SQLEditor.sqlGeneratedSuccess")}</Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
    },
    onError: (error) => {
      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.SQLEditor.Errors.generationFailed")}</Toast.Title>
          <Toast.Message>
            {error.message || t("components.SQLEditor.Errors.generationFailedMessage")}
          </Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
    },
  });

  const checkPrompt = (prompt: string) => {
    if (!prompt.trim()) {
      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.SQLEditor.Errors.emptyPrompt")}</Toast.Title>
          <Toast.Message>{t("components.SQLEditor.Errors.enterSQLDescription")}</Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
      return false;
    }
    return true;
  };

  const handleGenerateSQL = async () => {
    setIsGeneratingSql(true);
    try {
      if (!checkPrompt(generationPrompt)) {
        return;
      }
      const newQuery = await generateSQLMutation.mutateAsync(generationPrompt);
      if (!newQuery) {
        return;
      }

      const editorInstance = editorRef.current;
      if (!editorInstance) {
        return;
      }

      editorInstance.dispatch({
        changes: {
          from: 0,
          to: editorInstance.state.doc.length,
          insert: newQuery,
        },
      });

      setCurrentQuery(newQuery);
    } finally {
      setIsGeneratingSql(false);
    }
  };

  const handleAutomatedExecute = async () => {
    if (!checkPrompt(generationPrompt)) return;

    setIsGeneratingSql(true);

    try {
      const generatedSQL = await generateSQLMutation.mutateAsync(generationPrompt);

      if (generatedSQL) {
        const editorInstance = editorRef.current;
        if (!editorInstance) {
          return;
        }
        editorInstance.dispatch({
          changes: {
            from: 0,
            to: editorInstance.state.doc.length,
            insert: generatedSQL,
          },
        });
        await handleExecute(generatedSQL, true);
      }
    } catch (error) {
      console.error("Error in automated execute flow:", error);
    } finally {
      setIsGeneratingSql(false);
    }
  };

  return (
    <ConnectionsProvider>
      <Flex
        style={{
          borderTop: "solid var(--flo-base-color-neutral-300)",
        }}
      >
        <PanelGroup direction="horizontal">
          <Panel defaultSize={33} minSize={15} maxSize={33} collapsible={true} collapsedSize={0}>
            <SourceTreeView
              connections={connections}
              isFlolakeDataLoading={isFlolakeDataLoading}
              editorRef={editorRef}
              isDraft={isDraft}
            />
          </Panel>
          <PanelResizeHandle
            style={{
              width: "2px",
              background: "var(--flo-base-color-neutral-300)",
            }}
          >
            <ResizeHandle />
          </PanelResizeHandle>
          <Panel>
            <Flex
              direction="column"
              align="flex-start"
              gap={24}
              style={{
                padding: "24px",
                overflow: "scroll",
              }}
            >
              <Flex
                direction="row"
                justify="space-between"
                align="flex-start"
                style={{ height: "60px", width: "100%", paddingTop: "16px" }}
              >
                <Toggle
                  checked={isEditorMode}
                  onChange={handleToggleChange}
                  label={t("components.SQLEditor.sqlEditorToggleLabel")}
                />

                {!isEditorMode && (
                  <Flex direction="row" justify="space-between" gap={24}>
                    <QueryDropdown
                      currentQuery={currentQuery}
                      selectedQuery={selectedQuery}
                      setSelectedQuery={setSelectedQuery}
                    />
                    <Button disabled={!isDraft} onClick={handleApply}>
                      {t("components.SQLEditor.select")}
                    </Button>
                  </Flex>
                )}
              </Flex>

              <TextArea
                placeholder={t("components.SQLEditor.sqlEditorPrompt")}
                value={generationPrompt}
                label={t("components.SQLEditor.sqlEditorPromptLabel")}
                onChange={(val: string) => setGenerationPrompt(val)}
                disabled={generateSQLMutation.isPending || !isDraft || (isLoading && isEditorMode)}
                withFooter
                customSendButton={
                  <Button
                    disabled={isGeneratingSql || !isDraft || (isLoading && isEditorMode)}
                    onClick={!isEditorMode ? handleGenerateSQL : handleAutomatedExecute}
                  >
                    {generateSQLMutation.isPending || (isLoading && isEditorMode) ? (
                      <Spinner />
                    ) : (
                      t("components.SQLEditor.generate")
                    )}
                  </Button>
                }
                styleOverrides={{
                  inputWrapper: { width: "100%", outline: "none" },
                  textarea: {
                    width: "100%",
                    outline: "none",
                    height: !isEditorMode ? "100%" : "65vh",
                  },
                  footer: {
                    width: "100%",
                    outline: "none",
                    backgroundColor:
                      isGeneratingSql || !isDraft || (isLoading && isEditorMode)
                        ? "var(--flo-sem-color-surface-neutral-weakest)"
                        : "var(--flo-sem-color-surface-neutral-base)",
                  },
                  root: {
                    width: "100%",
                    outline: "none",
                  },
                }}
              />

              <Flex
                direction="column"
                style={{
                  height: "55vh",
                  width: "100%",
                  display: isEditorMode ? "none" : "block",
                }}
              >
                <SQLEditorWrapper id="editor" ref={containerRef} />
                <Flex justify="flex-end" align="center" style={{ height: "10vh" }}>
                  <Button
                    disabled={isLoading || !isDraft}
                    onClick={() => handleExecute(currentQuery)}
                  >
                    {isLoading ? <Spinner /> : t("components.SQLEditor.execute")}
                  </Button>
                </Flex>
              </Flex>
            </Flex>
          </Panel>
        </PanelGroup>
      </Flex>
    </ConnectionsProvider>
  );
};
