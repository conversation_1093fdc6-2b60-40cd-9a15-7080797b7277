import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { SideDrawer, useToast, Spinner, <PERSON>Button, Button } from "@floqastinc/flow-ui_core";
import { t } from "@/utils/i18n";
import { TaskDescription, TaskDescriptionDescription } from "@floqastinc/transform-v3";
import { useEffect, useState } from "react";
import cloneDeep from "lodash/cloneDeep";
import DetailsCard from "./DetailsCard";
import v3 from "@/services/v3";

export const DetailsSlideout = ({
  workflowId,
  taskId,
  exampleSetId,
  isOpen,
  setIsOpen,
}: {
  workflowId: string;
  taskId: string;
  exampleSetId: string;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}) => {
  const [showOverlay, setShowOverlay] = useState(false);
  const [loading, setLoading] = useState(false);
  const [descriptionValue, setDescriptionValue] = useState<TaskDescriptionDescription[]>();

  const { showToast, Toast } = useToast();

  const queryClient = useQueryClient();

  const onCancel = () => {
    setShowOverlay(false);
    setIsOpen(false);
  };

  const openOverlay = () => {
    setShowOverlay(true);
  };

  const fetchDescription = async (): Promise<TaskDescription | null> => {
    setLoading(true);
    let response = await v3.taskDescriptions.getTaskDescriptionByCompositeKey({
      workflowId,
      taskId,
      exampleSetId,
    });
    if (!response?.data?.id) {
      response = await v3.taskDescriptions.createTaskDescription({
        workflowId,
        taskId,
        exampleSetId,
        description: {
          generate: true,
        },
      });
    }
    if (!response?.data?.id) {
      console.error(t("components.DetailsSlideout.Errors.workflowDescNotFound"), response);
      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.DetailsSlideout.Errors.somethingWentWrong")}</Toast.Title>
          <Toast.Message>
            {t("components.DetailsSlideout.Errors.unableToGetDescription")} (
            {response.errors.map((e) => e.detail).join("; ")})
          </Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
    }
    setLoading(false);
    return response.data || null;
  };

  const { data } = useQuery({
    queryKey: ["description"],
    queryFn: fetchDescription,
  });

  useEffect(() => {
    setDescriptionValue(cloneDeep(data?.description));
  }, [data?.description]);

  const updateDescription = async ({
    description,
    generate,
  }: {
    description?: TaskDescriptionDescription[];
    generate: boolean;
  }): Promise<TaskDescription | null> => {
    if (!data || !data?.id) return null;
    setLoading(true);
    const response = await v3.taskDescriptions.updateTaskDescription({
      taskDescriptionId: data?.id,
      workflowId,
      taskId,
      exampleSetId,
      description: {
        description: description || [],
        generate: generate,
      },
    });
    return response.data || null;
  };

  const updateDescriptionMutation = useMutation({
    mutationFn: ({
      description,
      generate,
    }: {
      description?: TaskDescriptionDescription[];
      generate: boolean;
    }) => updateDescription({ description, generate }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["description"] });
      setLoading(false);
      setShowOverlay(false);
      showToast(
        <Toast type="success">
          <Toast.Title>{t("components.DetailsSlideout.success")}</Toast.Title>
          <Toast.Message>{t("components.DetailsSlideout.updatedTaskDescription")}</Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
    },
    onError: (error) => {
      setLoading(false);
      console.error(error);
      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.DetailsSlideout.error")}</Toast.Title>
          <Toast.Message>{t("components.DetailsSlideout.failedToFetchDesc")}</Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
    },
  });

  return (
    <SideDrawer
      show={isOpen}
      onCancel={onCancel}
      renderOverlay={showOverlay}
      aria-label={t("components.DetailsSlideout.taskDescription")}
    >
      <SideDrawer.Overlay>
        <SideDrawer.Header>
          <SideDrawer.Title>{t("components.DetailsSlideout.editTaskDescription")}</SideDrawer.Title>
          <SideDrawer.TopRight>
            <CloseButton donClick={onCancel} aria-label={t("components.DetailsSlideout.close")} />
          </SideDrawer.TopRight>
        </SideDrawer.Header>
        <SideDrawer.Body>
          {loading ? (
            <Spinner color="success" size={48} />
          ) : (
            <DetailsCard
              taskDescription={descriptionValue || []}
              editable={true}
              onChange={setDescriptionValue}
            />
          )}
        </SideDrawer.Body>
        <SideDrawer.Footer>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <Button
              size="lg"
              color="dark"
              variant="ghost"
              onClick={() => {
                setDescriptionValue(cloneDeep(data?.description));
                setShowOverlay(false);
              }}
              disabled={loading}
            >
              {t("components.DetailsSlideout.cancel")}
            </Button>
            <Button
              disabled={loading}
              onClick={() => {
                updateDescriptionMutation.mutate({
                  description: descriptionValue || [],
                  generate: false,
                });
              }}
            >
              {t("components.DetailsSlideout.save")}
            </Button>
          </div>
        </SideDrawer.Footer>
      </SideDrawer.Overlay>

      <SideDrawer.Header>
        <SideDrawer.Title>{t("components.DetailsSlideout.taskDescription")}</SideDrawer.Title>
        <SideDrawer.TopRight>
          <CloseButton onClick={onCancel} aria-label={t("components.DetailsSlideout.close")} />
        </SideDrawer.TopRight>
      </SideDrawer.Header>

      <SideDrawer.Body>
        {loading ? (
          <Spinner color="success" size={48} />
        ) : (
          <DetailsCard taskDescription={descriptionValue || []} onChange={setDescriptionValue} />
        )}
      </SideDrawer.Body>
      <SideDrawer.Footer>
        <div style={{ display: "flex", gap: "16px" }}>
          <Button
            disabled={loading}
            onClick={() =>
              updateDescriptionMutation.mutate({
                generate: true,
              })
            }
          >
            {t("components.DetailsSlideout.regenerate")}
          </Button>
          <Button disabled={loading} onClick={openOverlay}>
            {t("components.DetailsSlideout.edit")}
          </Button>
        </div>
      </SideDrawer.Footer>
    </SideDrawer>
  );
};
