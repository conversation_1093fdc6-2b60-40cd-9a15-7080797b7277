import React, { use<PERSON>allback, useMemo, useRef, useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { t } from "@/utils/i18n";
import type * as types from "@floqastinc/transform-v3";
import styled from "styled-components";
import { Button, CalendarSelectBox, DropdownButton, Flex } from "@floqastinc/flow-ui_core";
import DateRange from "@floqastinc/flow-ui_icons/material/DateRange";
import Download from "@floqastinc/flow-ui_icons/material/Download";
import { getExampleInputFileUriQuery } from "@BuilderV3/api/example-inputs";

import { Input, NumericInput } from "@/components/BootlegInputs";
import v3 from "@/services/v3";
import { queryClient } from "@/components/queryClient";
import { queryKeys } from "@BuilderV3/api/query-keys";

const ButtonText = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: var(--flo-base-font-family-1);
  font-size: var(--flo-base-font-size-3);
`;

export const InputArgumentValue: React.FC<
  {
    type: types.DataType;
  } & InputArgumentValueProps
> = ({ type, ...props }) => {
  switch (type) {
    case "DATETIME":
      return <DatetimeExample {...props} />;
    case "FILE":
      return <FileExample {...props} />;
    case "TEXT":
      return <TextExample {...props} />;
    case "NUMBER":
      return <NumberExample {...props} />;
    default:
      return <div>{t("components.InputArgumentValue.Errors.unsupportedInputType")}</div>;
  }
};

export type InputArgumentValueProps = {
  mode: "input";
  workflowId: string;
  taskId: string;
  exampleSetId: string;
  exampleInputId: string;
  argumentValue: types.ArgumentValue | undefined;
};

const stopPropagation = (e: React.MouseEvent) => {
  e.stopPropagation();
};

const NumberExample: React.FC<InputArgumentValueProps> = ({
  workflowId,
  argumentValue: example,
  ...props
}) => {
  const [value, setValue] = useState(example?.kind === "NUMBER" ? `${example.value}` : "");

  const saveContent = useMutation({
    mutationKey: ["setExampleInput", { workflowId }],
    mutationFn: (content: number) =>
      v3.exampleInputs.setExampleInputNumber({
        workflowId,
        taskId: props.taskId,
        exampleInputId: props.exampleInputId,
        exampleSetId: props.exampleSetId,
        value: { kind: "NUMBER", value: content },
      }),
  });

  const debouncedSaveContent = useDebounce(saveContent.mutate, 1000);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    const n = parseFloat(newValue);
    if (!isNaN(n)) {
      setValue(newValue);
      debouncedSaveContent(n);
    } else {
      setValue((prev) => prev);
    }
  };

  return (
    <>
      <NumericInput
        value={value}
        onChange={handleChange}
        onMouseDownCapture={stopPropagation}
        aria-label={t("components.InputArgumentValue.textInput")}
        placeholder={t("components.InputArgumentValue.addInput")}
      />
    </>
  );
};

const TextExample: React.FC<InputArgumentValueProps> = ({
  workflowId,
  argumentValue: example,
  ...props
}) => {
  const [value, setValue] = useState(example?.kind === "TEXT" ? example.value : "");

  const saveContent = useMutation({
    mutationKey: ["setExampleInput", { workflowId }],
    mutationFn: (content: string) =>
      v3.exampleInputs.setExampleInputText({
        workflowId,
        taskId: props.taskId,
        exampleInputId: props.exampleInputId,
        exampleSetId: props.exampleSetId,
        value: { kind: "TEXT", value: content },
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.exampleInputs.getExampleInputs({
          workflowId,
          taskId: props.taskId,
        }),
      });
    },
  });

  const debouncedSaveContent = useDebounce(saveContent.mutate, 1000);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
    debouncedSaveContent(newValue);
  };

  return (
    <Input
      aria-label={t("components.InputArgumentValue.textInput")}
      placeholder={t("components.InputArgumentValue.addInput")}
      value={value}
      onChange={handleChange}
      onMouseDownCapture={stopPropagation}
    />
  );
};

const DatetimeExample: React.FC<InputArgumentValueProps> = ({
  argumentValue: example,
  workflowId,
  ...props
}) => {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  const today = new Date();

  const [valueStr, setValueStr] = useState(
    asDayString(example?.kind === "DATETIME" ? asDate(example.value) : today),
  );
  const [activePeriod, setActivePeriod] = useState({
    month: today.getMonth() + 1,
    year: today.getFullYear(),
  });

  const saveDate = useMutation({
    mutationKey: ["setExampleInput", { workflowId }],
    mutationFn: (date: Date) =>
      v3.exampleInputs.setExampleInputDatetime({
        workflowId,
        taskId: props.taskId,
        exampleInputId: props.exampleInputId,
        exampleSetId: props.exampleSetId,
        value: {
          kind: "DATETIME",
          value: date,
        },
      }),
  });

  const handleChange = useCallback(
    async (val: string) => {
      if (props.mode === "input") {
        setValueStr(val);
        setIsCalendarOpen(false);

        const date = new Date(val);
        saveDate.mutate(date);
      }
    },
    [workflowId, props],
  );

  return (
    <CalendarSelectBox
      value={valueStr}
      activePeriod={activePeriod}
      onChange={handleChange}
      onMonthChange={setActivePeriod}
      onOpenChange={setIsCalendarOpen}
      open={isCalendarOpen}
      data-tracking-id="builder-message-datetime-picker-input"
      trigger={
        <DropdownButton open={open} onClick={() => {}}>
          <ButtonText>
            <DateRange
              color="var(--flo-sem-color-icon-primary)"
              style={{ opacity: 0.8 }}
              size={20}
            />
            {valueStr || t("components.InputArgumentValue.selectDate")}
          </ButtonText>
        </DropdownButton>
      }
      styleOverrides={{
        content: {
          zIndex: "9999",
        },
      }}
    />
  );
};

const FileExample: React.FC<InputArgumentValueProps> = ({
  argumentValue: example,
  workflowId,
  ...props
}) => {
  const exampleInputFileUri = useQuery({
    ...getExampleInputFileUriQuery({
      workflowId,
      taskId: props.taskId,
      exampleInputId: props.exampleInputId,
      exampleSetId: props.exampleSetId,
    }),
  });

  const handleDownload = useCallback(() => {
    if (exampleInputFileUri.data?.data?.url) {
      const {
        data: {
          data: { url },
        },
      } = exampleInputFileUri;
      if (!url) return;

      const a = document.createElement("a");
      a.href = url;
      a.download = (example as types.FileArgument).name;
      a.click();
      a.remove();
    }
  }, [exampleInputFileUri]);

  return (
    <Flex direction={"row"} align="center" gap={12}>
      <strong>{(example as types.FileArgument).name}</strong>
      <Button onClick={handleDownload} size="md" variant="ghost" color="dark" padding={false}>
        <Download color="currentColor" />
      </Button>
    </Flex>
  );
};

function asDayString(date: Date) {
  return date.toISOString().split("T")[0];
}

function asDate(value: string | Date) {
  return typeof value === "string" ? new Date(value) : value;
}

function useDebounce<T extends (...args: any[]) => void>(
  handler: T,
  timeout: number,
): (...args: Parameters<T>) => void {
  const timeoutRef = useRef<ReturnType<typeof setTimeout>>(null);

  const debouncedFunction = useMemo(() => {
    return (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => {
        handler(...args);
      }, timeout);
    };
  }, [handler, timeout]);

  return debouncedFunction;
}
