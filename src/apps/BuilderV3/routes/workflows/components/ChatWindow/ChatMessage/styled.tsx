import styled from "styled-components";

type MessageContainerProps = {
  $chatRole: "assistant" | "user" | (string & {});
};
export const MessageContainer = styled.div<MessageContainerProps>`
  display: flex;
  flex-direction: column;
  width: fit-content;
  max-width: 75%;
  margin-right: 16px;
  gap: 4px;
  align-items: ${(props) => (props.$chatRole === "assistant" ? "start" : "end")};
  align-self: ${(props) => (props.$chatRole === "assistant" ? "start" : "end")};
`;

export const Message = styled.div`
  display: flex;
  height: fit-content;
  gap: 16px;
  font-family: Inter;
  font-size: 14px;
`;

export const ChatMessageActions = styled.div`
  display: flex;
  gap: 10px;
  width: fit-content;
  align-self: start;
`;

export const MessageContent = styled.div<{ $isEditing?: boolean }>`
  padding: 12px 8px;
  border: ${(props) => (props.$isEditing ? "none" : "1px solid var(--flo-base-color-neutral-300)")};
  border-radius: 8px;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: bold;
    font-size: revert;
  }

  & ul,
  & ol {
    list-style: disc inside;
  }

  & p {
    margin: 20px 0;
  }
  *:first-child {
    margin-top: 0;
  }
  *:last-child {
    margin-bottom: 0;
  }
`;

export const InputContent = styled.p`
  font-family: Inter;
  font-size: 14px;
  padding: 12px 8px;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: bold;
    font-size: revert;
  }

  * {
    margin: 20px 0;
  }
  *:first-child {
    margin-top: 0;
  }
  *:last-child {
    margin-bottom: 0;
  }
`;

export const PreserveFormatting = styled.span`
  white-space: pre-wrap;
`;

export const DatetimeButton = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

export const TextInput = styled.div<{ $disabled?: boolean }>`
  border: 1px solid var(--flo-base-color-neutral-300);
  border-radius: 8px;
  padding: 12px 8px;

  &:hover {
    ${(props) => (props.$disabled ? "" : "background-color: var(--flo-base-color-neutral-100)")};
    ${(props) => (props.$disabled ? "" : "cursor: pointer")};
  }
`;
