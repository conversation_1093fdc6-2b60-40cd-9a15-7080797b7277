import { Button, Dialog } from "@floqastinc/flow-ui_core";
import { t } from "@/utils/i18n";
import { useNavigate, useParams } from "react-router";
import { useAtom } from "jotai";
import { confirmMessageActionDialogOpenAtom } from "./store";

type ConfirmMessageActionDialogProps = {
  onConfirm: () => void;
  onCancel: () => void;
  messageActionText: string | null;
};
export const ConfirmMessageActionDialog = ({
  onCancel,
  onConfirm,
  messageActionText,
}: ConfirmMessageActionDialogProps) => {
  const { workflowId, taskId, exampleSetId } = useParams();
  if (!workflowId || !taskId || !exampleSetId) {
    console.error(t("components.ConfirmMessageActionDialogue.Errors.noMessageID"));
    throw new Error(t("components.ConfirmMessageActionDialogue.Errors.noMessageID"));
  }

  const [isConfirmActionDialogue, setisConfirmActionDialogue] = useAtom(
    confirmMessageActionDialogOpenAtom,
  );

  return (
    <Dialog
      type="warning"
      open={isConfirmActionDialogue}
      onOpenChange={(isOpen: boolean) => {
        setisConfirmActionDialogue(isOpen);
      }}
    >
      <Dialog.Trigger>
        <Button style={{ display: "none" }}>{t("components.Chat.no")}</Button>
      </Dialog.Trigger>
      <Dialog.Header>{t("components.ConfirmMessageActionDialogue.confirmAction")}</Dialog.Header>
      <Dialog.Body>
        {t("components.ConfirmMessageActionDialogue.aboutTo")} {`${messageActionText}`}{" "}
        {t("components.ConfirmMessageActionDialogue.aboutTo2")}
      </Dialog.Body>
      <Dialog.Footer>
        <Dialog.FooterCancelBtn
          onClick={() => {
            setisConfirmActionDialogue(false);
            onCancel();
          }}
        >
          {t("components.ConfirmMessageActionDialogue.cancel")}
        </Dialog.FooterCancelBtn>
        <Dialog.FooterActionBtn onClick={() => onConfirm()}>
          {t("components.ConfirmMessageActionDialogue.save")}
        </Dialog.FooterActionBtn>
      </Dialog.Footer>
    </Dialog>
  );
};
