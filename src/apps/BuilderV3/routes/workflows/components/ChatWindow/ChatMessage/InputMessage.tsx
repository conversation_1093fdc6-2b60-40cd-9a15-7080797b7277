import PropTypes from "prop-types";
import * as Styled from "./styled";
// @ts-ignore
import { Avatar } from "@floqastinc/flow-ui_core";
import { InputArgumentValue } from "./InputArgumentValue";
import { MergedExampleInput } from "@BuilderV3/routes/flocharts/utils";
import { useState } from "react";

const UserAvatar = () => <Avatar size="sm" />;

const NullAvatar = () => <div style={{ width: "28px" }} />;

export const InputMessage = ({
  input,
  includeAvatar,
  style,
  children,
}: {
  input: MergedExampleInput;
  includeAvatar?: boolean;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}) => {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  return (
    <Styled.MessageContainer $chatRole="user">
      <Styled.Message style={{ ...style, alignItems: "center" }}>
        {<NullAvatar />}
        <InputArgumentValue
          mode="input"
          type={input.value?.kind!}
          argumentValue={input.value}
          workflowId={input.workflowId}
          taskId={input.taskId}
          exampleInputId={input.id}
          exampleSetId={input.exampleSetId}
        />
        {/* TODO: Discuss implications of deleting inputs */}
        {/* <DeleteMessageInputDialog
        isOpen={isDeleteDialogOpen}
        setIsOpen={setIsDeleteDialogOpen}
        input={input}
      /> */}
        {children}
        {includeAvatar ? <UserAvatar /> : <NullAvatar />}
      </Styled.Message>
    </Styled.MessageContainer>
  );
};
