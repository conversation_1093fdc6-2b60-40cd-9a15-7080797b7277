import React, { useState } from "react";
import { t } from "@/utils/i18n";
import styled from "styled-components";
import { useMutation } from "@tanstack/react-query";
// @ts-ignore
import { IconButton, Dialog } from "@floqastinc/flow-ui_core";
// @ts-ignore
import DeleteOutlined from "@floqastinc/flow-ui_icons/material/DeleteOutlined";

import { MergedExampleInput } from "@BuilderV3/routes/flocharts/utils";
import { queryClient } from "@/components/queryClient";
import v3, { ApiError } from "@/services/v3";
import { queryKeys } from "@BuilderV3/api/query-keys";

const DeleteButton = styled(IconButton)`
  &:hover {
    background-color: #d23340;
  }
`;

const DeleteMessageInputDialog = ({
  isOpen,
  setIsOpen,
  input,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  input: MergedExampleInput;
}) => {
  const [isDeleting, setIsDeleting] = useState(false);

  const deleteInputMutation = useMutation({
    mutationFn: async () => {
      try {
        if (input.exampleSetId) {
          const responses = await Promise.allSettled([
            input.meta?.workflowInputId
              ? v3.workflowInputs.deleteWorkflowInput({
                  workflowId: input.workflowId,
                  workflowInputId: input.meta?.workflowInputId!,
                })
              : Promise.resolve(),
            v3.taskInputs.deleteTaskInput({
              workflowId: input.workflowId,
              taskId: input.taskId,
              taskInputId: input.taskInputId,
            }),
            v3.exampleInputs.deleteExampleInput({
              workflowId: input.workflowId,
              taskId: input.taskId,
              exampleInputId: input.id,
              exampleSetId: input.exampleSetId,
            }),
          ]);

          const rejectedResponses = responses.filter((res) => res.status === "rejected");
          if (rejectedResponses.length > 0) {
            const reasons = rejectedResponses.map((res) => res.reason);
            throw new ApiError(reasons);
          }

          queryClient.invalidateQueries({
            queryKey: queryKeys.taskInputs.byTask({
              workflowId: input.workflowId,
              taskId: input.taskId,
            }),
          });
          queryClient.invalidateQueries({
            queryKey: [
              {
                resource: queryKeys.exampleInputs.resource,
                params: { workflowId: input.workflowId, taskId: input.taskId },
              },
            ],
          });
        }
      } catch (error) {
        console.error(t("components.DeleteInputMessageDialog.Errors.issueDeletingInput"), error);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["deepWorkflows", { id: input.workflowId }],
      });
    },
    onError: (error) => {
      console.error("error", error);
    },
  });

  const handleConfirmDeleteClick = async () => {
    setIsDeleting(true);
    deleteInputMutation.mutate();
    setIsDeleting(false);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(isOpen: boolean) => setIsOpen(isOpen)} type="danger">
      <Dialog.Trigger color="danger">
        <DeleteButton
          aria-label={t("components.DeleteInputMessageDialog.deleteInput")}
          onClick={() => {}}
        >
          <DeleteOutlined />
        </DeleteButton>
      </Dialog.Trigger>
      <Dialog.Header>
        {t("components.DeleteInputMessageDialog.permanentlyDeleteInput")}
      </Dialog.Header>
      <Dialog.Body>
        <p>
          {t("components.DeleteInputMessageDialog.permanentlyDelete1")}
          <strong>"{input.meta?.name}"</strong>
          {t("components.DeleteInputMessageDialog.permanentlyDelete2")}
        </p>
      </Dialog.Body>
      <Dialog.Footer>
        <Dialog.FooterCancelBtn onClick={() => setIsOpen(false)} disabled={isDeleting}>
          {t("components.DeleteInputMessageDialog.cancel")}
        </Dialog.FooterCancelBtn>
        <Dialog.FooterActionBtn
          color="danger"
          disabled={isDeleting}
          onClick={handleConfirmDeleteClick}
        >
          {isDeleting ? "Deleting..." : "Permanently delete!"}
        </Dialog.FooterActionBtn>
      </Dialog.Footer>
    </Dialog>
  );
};

export default DeleteMessageInputDialog;
