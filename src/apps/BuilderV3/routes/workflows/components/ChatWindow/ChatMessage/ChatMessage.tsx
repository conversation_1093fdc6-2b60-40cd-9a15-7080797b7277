import { TypeAnimation } from "react-type-animation";
import { t } from "@/utils/i18n";
import {
  Avatar,
  Button,
  IconButton,
  TextArea,
  Tooltip,
  Dialog,
  useToast,
} from "@floqastinc/flow-ui_core";
import { DeleteOutlined, EditOutlined, SyncOutlined } from "@floqastinc/flow-ui_icons";
import MagicAiStar from "@floqastinc/flow-ui_icons/fq/MagicAIStar";
import Markdown from "react-markdown";
import React, { useState } from "react";
import { useAtom } from "jotai";
import { useParams } from "react-router-dom";
import { useMutation, useQuery } from "@tanstack/react-query";
import { match } from "ts-pattern";
import type { ChatMessage as ChatMessageType } from "../../ChatWindow/types";
import { messagesAtom } from "../store";
import { isEditingAtom } from "./store";
import * as Styled from "./styled";
import { editMessage, regenerateMessage } from "@BuilderV3/api/messages";
import v3 from "@/services/v3";
import { getExampleQuery } from "@BuilderV3/api/examples";
import { FeedbackButtons, FeedbackType } from "@BuilderV3/app-components/Feedback/FeedbackButtons";

/**
 * Workaround for the issue of `p` tags in <li /> tags having `block` display
 * and causing a newline break within the li tag.
 *
 * This unwraps the `p` children and discards the `p` tag.
 *
 * NOTE: This cannot be done in the Markdown component via `components={{ p: (...) => ... }}`
 *  since other line breaks in the formatting relies on the `p` tag. It can only be done by
 *  modifying the `li` tag to look for nested `p` tags.
 */
const Li = ({
  children,
  ...rest
}: React.DetailedHTMLProps<React.LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>) => {
  type ParagraphProps = {
    children: React.ReactNode;
  };
  const children_: React.ReactNode[] = [];
  React.Children.forEach(children, (child) => {
    if (React.isValidElement(child) && child.type === "p") {
      children_.push((child as React.ReactElement<ParagraphProps>).props.children);
    } else {
      children_.push(child);
    }
  });

  return <li {...rest}>{children_}</li>;
};

export const cleanMessage = (message: string) => {
  return message.replace(/\*\*INPUTS\*\*:[\s\S]*?\*\*MESSAGE\*\*:/, "").trim();
};

const AiAvatar = () => <Avatar icon={<MagicAiStar />} />;
const UserAvatar = () => <Avatar size="sm" />;

type ChatMessageProps = {
  message: ChatMessageType;
  assistantMessageIsPending?: boolean;
  children?: React.ReactNode[];
  style?: React.CSSProperties;
  showFeedback?: boolean;
};

export const ChatMessage = ({
  message,
  assistantMessageIsPending,
  children,
  style,
  showFeedback,
}: ChatMessageProps) => {
  const { workflowId, taskId, exampleSetId } = useParams();
  if (!workflowId || !taskId || !exampleSetId) {
    throw new Error(t("components.ChatMessage.Errors.noIDsprovided"));
  }

  const { showToast, Toast } = useToast();
  type ToastType = "error" | "success";
  const renderToast = (type: ToastType, title: string, message: string) => {
    showToast(
      <Toast type={type}>
        <Toast.Title>{title}</Toast.Title>
        <Toast.Message>{message}</Toast.Message>
      </Toast>,
      {
        duration: 5000,
        position: "bottom-right",
      },
    );
  };

  const [draftMessage, setDraftMessage] = useState(message.content);

  const [messages, setMessages] = useAtom(messagesAtom);
  const removeMessagesFrom = (messageId: string) => {
    const messagesToKeepIndex = messages.findIndex((msg) => msg.id === messageId);
    if (messagesToKeepIndex === -1) {
      return messages;
    }
    return messages.slice(0, messagesToKeepIndex);
  };
  const removeMessagesAfter = (messageId: string) => {
    const messagesToKeepIndex = messages.findIndex((msg) => msg.id === messageId);
    if (messagesToKeepIndex === -1) {
      return messages;
    }
    return messages.slice(0, messagesToKeepIndex + 1);
  };

  const [isEditing, setIsEditing] = useAtom(isEditingAtom);

  const exampleQuery = useQuery(
    getExampleQuery({
      workflowId,
      taskId,
      exampleSetId,
    }),
  );

  const editMessageMutation = useMutation({
    mutationKey: [
      "editMessage",
      {
        workflowId,
        taskId,
        exampleSetId,
        messageId: message.id,
      },
    ],
    mutationFn: async (message: { content: string; id: string }) => {
      setMessages([
        ...removeMessagesFrom(message.id),
        {
          id: undefined,
          role: "user",
          content: message.content,
        },
      ]);
      // TODO: Add some validation here
      return editMessage({
        workflowId,
        taskId: taskId,
        exampleSetId,
        message: message,
      });
    },
    onError: (error) => {
      console.error("error", error);
    },
  });

  const deleteMessageMutation = useMutation({
    mutationKey: [
      "deleteMessage",
      {
        workflowId,
        taskId,
        exampleSetId,
        messageId: message.id,
      },
    ],
    mutationFn: async (messageId: string) => {
      return v3.messages.deleteMessage({
        workflowId,
        taskId,
        exampleSetId,
        messageId,
      });
    },
    onSuccess: async () => {
      renderToast("success", "Success", t("components.ChatMessage.messageDeleted"));
    },
    onError: (error) => {
      console.error("error", error);
    },
  });

  const regenerateMessageMutation = useMutation({
    mutationKey: [
      "regenerateMessage",
      {
        workflowId,
        taskId,
        exampleSetId,
        messageId: message.id,
      },
    ],
    mutationFn: async (messageId: string) => {
      setMessages(removeMessagesAfter(messageId));
      return regenerateMessage({
        workflowId,
        taskId: taskId,
        exampleSetId,
        messageId,
      });
    },
    onSuccess: async () => {
      renderToast("success", "Success", t("components.ChatMessage.messageRegenerated"));
    },
    onError: (error) => {
      console.error("error", error);
    },
  });

  const isDraft = exampleQuery.data?.status === "DRAFT";
  const [confirmationDialogView, setConfirmationDialogView] = useState<
    "edit" | "delete" | "regenerate" | null
  >(null);
  const confirmationDialogContent = match(confirmationDialogView)
    .with("edit", () => ({
      header: t("components.ChatMessage.edit") + t("components.Chat.message"),
      body: t("components.ChatMessage.editMessage"),
      action: () => {
        if (!message.id) {
          throw new Error(t("components.ChatMessage.Errors.noMessageID"));
        }
        editMessageMutation.mutate({
          content: draftMessage,
          id: message.id,
        });
      },
    }))
    .with("delete", () => ({
      header: t("components.Chat.delete") + t("components.Chat.message"),
      body: t("components.ChatMessage.deleteMessage"),
      action: () => {
        if (!message.id) {
          throw new Error(t("components.ChatMessage.Errors.noMessageID"));
        }
        deleteMessageMutation.mutate(message.id);
      },
    }))
    .with("regenerate", () => ({
      header: t("components.ChatMessage.regenerateAssistantResponse"),
      body: t("components.ChatMessage.areYouSureRegenerateResponse"),
      action: () => {
        if (!message.id) {
          throw new Error(t("components.ChatMessage.Errors.noMessageID"));
        }
        regenerateMessageMutation.mutate(message.id);
      },
    }))
    .with(null, () => ({
      header: "",
      body: "",
      action: () => {},
    }))
    .exhaustive();

  // Editing actions
  const handleCancelEditClick = () => {
    setIsEditing(false);
    setDraftMessage(message.content);
  };

  const submitFeedback = async (type: FeedbackType) => {
    const metricName = type === "upVote" ? "thumbsUp" : "thumbsDown";
    const value = { kind: "NUMBER", numberValue: type === "upVote" ? 1 : 0 };
    try {
      await v3.llmMetrics.createLlmMetric({
        metric: {
          metricName,
          value,
          entityType: "TASK",
          workflowId,
          taskId,
          exampleSetId,
        },
        workflowId,
      });
      showToast(
        <Toast type="success">
          <Toast.Title>{t("components.Chat.thankYouForYourFeedback")}</Toast.Title>
          <Toast.Message>{t("components.Chat.yourFeedbackHelpsUsImprove")}</Toast.Message>
        </Toast>,
        { position: "bottom-right" },
      );
    } catch (error) {
      console.error("Error capturing thumbsUp/thumbsDown feedback:", error);
    }
  };

  return (
    <>
      {confirmationDialogView ? (
        <Dialog
          type="warning"
          open={confirmationDialogView !== null}
          onOpenChange={(isOpen: boolean) => {
            if (!isOpen) {
              setConfirmationDialogView(null);
            }
          }}
        >
          <Dialog.Header>{confirmationDialogContent.header}</Dialog.Header>
          <Dialog.Body>{confirmationDialogContent.body}</Dialog.Body>
          <Dialog.Footer>
            <Dialog.FooterCancelBtn
              onClick={() => setConfirmationDialogView(null)}
              data-tracking-id="builder-message-dialog-confirmation-cancel-button"
            >
              {t("components.Chat.cancel")}
            </Dialog.FooterCancelBtn>
            <Dialog.FooterActionBtn
              onClick={() => {
                confirmationDialogContent.action();
                setIsEditing(false);
                setConfirmationDialogView(null);
              }}
              data-tracking-id="builder-message-dialog-confirmation-confirm-button"
            >
              {t("components.Chat.confirm")}
            </Dialog.FooterActionBtn>
          </Dialog.Footer>
        </Dialog>
      ) : null}
      <Styled.MessageContainer $chatRole={message.role}>
        <Styled.Message style={style}>
          {message.role === "assistant" ? <AiAvatar /> : null}

          {/* Message Content Area */}
          {isEditing ? (
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "end",
                gap: "8px",
              }}
            >
              {/* <TextArea type="text" value={message} onChange={onChange} /> */}
              <TextArea
                label={t("components.ChatMessage.draftMessage")}
                type="text"
                value={draftMessage}
                onChange={setDraftMessage}
              />
              <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <Button
                  variant="outlined"
                  style={{ color: "#6B7280" }}
                  onClick={handleCancelEditClick}
                  data-tracking-id="builder-message-edit-message-cancel-button"
                >
                  {t("components.Chat.cancel")}
                </Button>
                <Button
                  onClick={() => setConfirmationDialogView("edit")}
                  data-tracking-id="builder-message-edit-message-save-button"
                >
                  {t("components.Chat.save")}
                </Button>
              </div>
            </div>
          ) : (
            <Styled.MessageContent>
              {message.role === "assistant" ? (
                <Markdown
                  components={{
                    li({ node, ...rest }) {
                      return <Li {...rest} />;
                    },
                  }}
                >
                  {message.content}
                </Markdown>
              ) : (
                <Styled.PreserveFormatting>{message.content}</Styled.PreserveFormatting>
              )}
            </Styled.MessageContent>
          )}
          {children}
          {message.role === "user" ? <UserAvatar /> : null}
        </Styled.Message>
        {/* Action Buttons for User's Messages */}
        {message.role === "user" && !isEditing && isDraft && !exampleQuery.isPending && (
          <Styled.ChatMessageActions>
            {/* Edit Button */}
            <Tooltip title="Edit message">
              <Tooltip.Trigger>
                <IconButton
                  onClick={() => {
                    setIsEditing(true);
                  }}
                  data-tracking-id="builder-chat-message-edit-button"
                  size="sm"
                  disabled={assistantMessageIsPending}
                >
                  <EditOutlined />
                </IconButton>
              </Tooltip.Trigger>
              <Tooltip.Content>
                {t("components.ChatMessage.edit") + t("components.Chat.message")}
              </Tooltip.Content>
            </Tooltip>

            {/* Regenerate Button */}
            <Tooltip title="Regenerate message">
              <Tooltip.Trigger>
                <IconButton
                  size="sm"
                  onClick={() => {
                    setConfirmationDialogView("regenerate");
                  }}
                  data-tracking-id="builder-chat-message-regenerate-button"
                  disabled={assistantMessageIsPending}
                >
                  <SyncOutlined />
                </IconButton>
              </Tooltip.Trigger>
              <Tooltip.Content>
                {t("components.Chat.regenerate") + t("components.Chat.message")}
              </Tooltip.Content>
            </Tooltip>

            {/* Delete Button */}
            <Tooltip title="Delete message">
              <Tooltip.Trigger>
                <IconButton
                  size="sm"
                  onClick={() => {
                    setConfirmationDialogView("delete");
                  }}
                  disabled={assistantMessageIsPending}
                  data-tracking-id="builder-chat-message-delete-button"
                >
                  <DeleteOutlined />
                </IconButton>
              </Tooltip.Trigger>
              <Tooltip.Content>
                {t("components.Chat.delete") + t("components.Chat.message")}
              </Tooltip.Content>
            </Tooltip>
          </Styled.ChatMessageActions>
        )}
        {message.role === "assistant" && showFeedback && message.id && (
          <Styled.ChatMessageActions style={{ marginLeft: "48px" }}>
            <FeedbackButtons messageId={message.id} onFeedback={submitFeedback} />
          </Styled.ChatMessageActions>
        )}
      </Styled.MessageContainer>
    </>
  );
};

export const AssistantMessagePending = () => {
  return (
    <Styled.MessageContainer $chatRole="assistant">
      <Styled.Message>
        <AiAvatar />
        <Styled.MessageContent>
          <TypeAnimation
            sequence={[".  ", 750, ".. ", 750, "...", 750]}
            cursor={false}
            omitDeletionAnimation
            wrapper="span"
            speed={50}
            repeat={Infinity}
          />
        </Styled.MessageContent>
      </Styled.Message>
    </Styled.MessageContainer>
  );
};
