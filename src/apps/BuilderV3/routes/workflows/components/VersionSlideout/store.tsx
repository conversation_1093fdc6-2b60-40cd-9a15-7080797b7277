import { ExampleSetStatus } from "@floqastinc/transform-v3";
import { atom } from "jotai";

// NOTE: There is NO <ScopeProvider> used for the statusChangeAtom
// This is because the Dialog for the status change
// is rendered outside the tree and does not get captured in the context tree.
// Thus, we let it fall to the default global provider.
export const statusChangeAtom = atom<{
  fromStatus: ExampleSetStatus | null;
  toStatus: ExampleSetStatus | null;
  exampleSetId: string | null;
}>({
  fromStatus: null,
  toStatus: null,
  exampleSetId: null,
});
