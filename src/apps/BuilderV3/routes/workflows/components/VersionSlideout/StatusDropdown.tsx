import { capitalize } from "@/utils/string";
import { DropdownPanel } from "@floqastinc/flow-ui_core";
import KeyboardArrowDown from "@floqastinc/flow-ui_icons/material/KeyboardArrowDown";
import KeyboardArrowUp from "@floqastinc/flow-ui_icons/material/KeyboardArrowUp";
import { ExampleSetStatus } from "@floqastinc/transform-v3";
import * as Styled from "./styled";

type StatusDropdownProps = {
  isOpen: boolean;
  status: ExampleSetStatus;
  onChange: (status: ExampleSetStatus) => void;
  onOpenChange: (open: boolean) => void;
};

export const getStatus = (status: ExampleSetStatus) => {
  switch (status) {
    case "ACTIVE":
      return "DEFAULT";
    default:
      return status;
  }
};

// General rules:
// 1. The current status should not be an option in the dropdown
// 2. Don't allow publishing of draft/archived from the dropdown
// - This is because we need to ensure version has output before publishing
const getDropdownOptions = (status: ExampleSetStatus): ExampleSetStatus[] => {
  if (status === "ACTIVE") {
    return ["PUBLISHED", "DRAFT", "ARCHIVED"];
  } else if (status === "PUBLISHED") {
    return ["ACTIVE", "DRAFT", "ARCHIVED"];
  } else if (status === "DRAFT") {
    return ["ARCHIVED"];
  } else if (status === "ARCHIVED") {
    return ["DRAFT"];
  } else {
    return [];
  }
};

export const StatusDropdown = ({ isOpen, status, onChange, onOpenChange }: StatusDropdownProps) => {
  return (
    <DropdownPanel
      isOpen={isOpen}
      onChange={onChange}
      onOpenChange={onOpenChange}
      disableClear
      disableFilter
      selectionMode="single"
      selectedValues={status}
    >
      <DropdownPanel.Trigger>
        <Styled.PillButton $status={status}>
          {capitalize(getStatus(status))}
          <Styled.KeyboardArrowDownIcon $status={status}>
            {isOpen ? (
              <KeyboardArrowUp height={16} width={16} />
            ) : (
              <KeyboardArrowDown height={16} width={16} />
            )}
          </Styled.KeyboardArrowDownIcon>
        </Styled.PillButton>
      </DropdownPanel.Trigger>
      <DropdownPanel.Content
        style={{
          // There's a random z-index of 100 on the SideDrawer component.
          // There _should_ be a defined one -> var(--fq-z-index-slideout)
          // but stuff happens, so we just need to manually define a matching one
          zIndex: 100,
        }}
      >
        {getDropdownOptions(status).map((option) => (
          <DropdownPanel.Option value={option} key={option}>
            {capitalize(getStatus(option))}
          </DropdownPanel.Option>
        ))}
      </DropdownPanel.Content>
    </DropdownPanel>
  );
};
