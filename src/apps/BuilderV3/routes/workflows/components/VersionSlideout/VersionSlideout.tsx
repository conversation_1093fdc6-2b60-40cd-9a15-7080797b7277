import { useQuery } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { t } from "@/utils/i18n";
import { partition } from "lodash";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Heading, SideDrawer, TabGroup, Tab, IconButton, Tooltip } from "@floqastinc/flow-ui_core";
import Close from "@floqastinc/flow-ui_icons/material/Close";
import Warning from "@floqastinc/flow-ui_icons/material/Warning";
import { match, P } from "ts-pattern";
import { ExampleSet } from "@floqastinc/transform-v3";
import { VersionOptionsDropdown } from "../VersionOptionsDropdown/VersionOptionsDropdown";
import { StatusChangeDialog } from "./StatusChangeDialog";
import { StatusDropdown } from "./StatusDropdown";
import { statusChangeAtom } from "./store";
import * as Styled from "./styled";
import { getWorkflowTaskQuery } from "@BuilderV3/api/tasks";
import { getExamplesQuery } from "@BuilderV3/api/examples";

type VersionSlideoutProps = {
  show: boolean;
  onClose: () => void;
};
type VersionTab = "active" | "draft" | "archived";
export const VersionSlideout = ({ show, onClose }: VersionSlideoutProps) => {
  const { workflowId, taskId, exampleSetId } = useParams();
  const navigate = useNavigate();
  if (!workflowId || !taskId || !exampleSetId) {
    console.warn(t("components.VersionSlideout.Errors.noIDsprovided"));
    navigate("/builder");
    return null;
  }

  const [currentlyOpenStatusDropdown, setCurrentlyOpenStatusDropdown] = useState<string | null>(
    null,
  );

  const [tab, setTab] = useState<VersionTab>("active");

  const taskQuery = useQuery(
    getWorkflowTaskQuery({
      workflowId,
      taskId,
    }),
  );

  const examplesQuery = useQuery({
    ...getExamplesQuery({
      workflowId,
      taskId,
    }),
    select: (data) => {
      const sortedData = data.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
      const examples = sortedData.reduce(
        (accExamples, curExample) => {
          if (curExample.status === "ACTIVE" || curExample.status === "PUBLISHED") {
            accExamples.activeAndPublishedExamples.push({
              ...curExample,
              name:
                curExample.name ??
                `Version ${curExample.createdAt.toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                  hour: "numeric",
                  minute: "2-digit",
                  second: "2-digit",
                })}`,
            });
          } else if (curExample.status === "DRAFT") {
            accExamples.draftExamples.push(curExample);
          } else if (curExample.status === "ARCHIVED") {
            accExamples.archivedExamples.push(curExample);
          }

          accExamples.allExamples.push(curExample);

          return accExamples;
        },
        {
          activeAndPublishedExamples: [] as (ExampleSet & { name: string })[],
          draftExamples: [] as (ExampleSet & { name: string })[],
          archivedExamples: [] as (ExampleSet & { name: string })[],
          allExamples: [] as ExampleSet[],
        },
      );
      return examples;
    },
    throwOnError: true,
  });

  useEffect(() => {
    if (examplesQuery.data) {
      const currentExample = examplesQuery.data.allExamples.find(
        (example) => example.id === exampleSetId,
      );
      match(currentExample?.status)
        .with(P.union("ACTIVE", "PUBLISHED"), () => setTab("active"))
        .with("DRAFT", () => setTab("draft"))
        .with("ARCHIVED", () => setTab("archived"));
    }
  }, [examplesQuery.data]);

  const examples =
    match(tab)
      .with("active", () => examplesQuery.data?.activeAndPublishedExamples)
      .with("draft", () => examplesQuery.data?.draftExamples)
      .with("archived", () => examplesQuery.data?.archivedExamples)
      .otherwise(() => []) ?? [];

  const hasAnActiveVersion = examplesQuery.data?.activeAndPublishedExamples.some(
    (example) => example.status === "ACTIVE",
  );

  const [statusChange, setStatusChange] = useAtom(statusChangeAtom);
  const isStatusChangeDialogOpen = Boolean(
    statusChange.fromStatus && statusChange.toStatus && statusChange.exampleSetId,
  );

  // NOTE: There is NO <ScopeProvider> here for the statusChangeAtom
  // This is because Dialog is rendered outside the tree and does
  // not get captured in the context tree. Thus, we let it
  // fall to the default global provider.
  return (
    <>
      <StatusChangeDialog
        open={isStatusChangeDialogOpen}
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            setStatusChange({
              fromStatus: null,
              toStatus: null,
              exampleSetId: null,
            });
          }
        }}
        currentTask={taskQuery.data}
      />
      {show && (
        <SideDrawer
          show={show}
          onCancel={() => {
            setStatusChange({
              fromStatus: null,
              toStatus: null,
              exampleSetId: null,
            });
            onClose();
          }}
        >
          <SideDrawer.Body>
            <Styled.HeaderArea>
              <Styled.Header>
                <Heading variant="h5" weight="semibold">
                  {taskQuery.data?.name
                    ? t("components.VersionSlideout.versionOfStep") + taskQuery.data?.name
                    : t("components.VersionSlideout.versions")}
                </Heading>
              </Styled.Header>
              <Styled.VersionsDescription>
                <div>{t("components.VersionSlideout.versionsCanBeOTFS")}</div>
                <ul style={{ listStyleType: "circle", paddingLeft: 20 }}>
                  <li>
                    <strong>{t("components.VersionSlideout.default")}</strong>
                    {t("components.VersionSlideout.versionExecutedWorkflow")}
                  </li>
                  <li>
                    <strong>{t("components.VersionSlideout.published")}</strong>
                    {t("components.VersionSlideout.finalizedVersionDefault")}
                  </li>
                  <li>
                    <strong>{t("components.VersionSlideout.draft")}:</strong>
                    {t("components.VersionSlideout.developmentVer")}
                  </li>
                  <li>
                    <strong>{t("components.VersionSlideout.archived")}:</strong>{" "}
                    {t("components.VersionSlideout.notInUse")}
                  </li>
                </ul>
                <div>{t("components.VersionSlideout.forWorkflowToRun")}</div>
              </Styled.VersionsDescription>
              <Styled.CloseIconButton>
                <IconButton onClick={onClose}>
                  <Close />
                </IconButton>
              </Styled.CloseIconButton>
            </Styled.HeaderArea>
            <Styled.Versions>
              <Styled.Tabs>
                <TabGroup
                  defaultValue={tab}
                  value={tab}
                  onValueChange={(tab: VersionTab) => {
                    setTab(tab);
                  }}
                  data-tracking-id="builder-version-slideout-tab-group"
                >
                  <Tab tabId="active" title="Published" />
                  {!hasAnActiveVersion ? (
                    <Styled.WarningIcon>
                      <Tooltip>
                        <Tooltip.Trigger>
                          <Warning height={12} width={12} color="#DB7712" />
                        </Tooltip.Trigger>
                        <Tooltip.Content hasArrow style={{ zIndex: 100, width: "250px" }}>
                          <p>{t("components.VersionSlideout.noActiveVersions")}</p>
                          <p>{t("components.VersionSlideout.noWorkflowRunsCan")}</p>
                        </Tooltip.Content>
                      </Tooltip>
                    </Styled.WarningIcon>
                  ) : null}
                  <Tab tabId="draft" title={t("components.VersionSlideout.draft")} />
                  <Tab tabId="archived" title={t("components.VersionSlideout.archived")} />
                </TabGroup>
              </Styled.Tabs>
              <Styled.Hr />
              <Styled.VersionList>
                {examples.map((example) => {
                  return (
                    <Styled.VersionItem key={example.id}>
                      <Styled.VersionItemLeft>
                        <Styled.SelectButton
                          variant="outlined"
                          color="dark"
                          data-tracking-id="builder-version-slideout-select-version-button"
                          onClick={() => {
                            navigate(
                              `/builder/v3/agents/${workflowId}/steps/${taskId}/examples/${example.id}`,
                            );
                          }}
                          isActive={example.id === exampleSetId}
                        >
                          {example.id === exampleSetId
                            ? t("components.VersionSlideout.selected")
                            : t("components.VersionSlideout.select")}
                        </Styled.SelectButton>
                        <Styled.VersionItemTitle>{example.name}</Styled.VersionItemTitle>
                      </Styled.VersionItemLeft>
                      <VersionOptionsDropdown
                        example={example}
                        data-tracking-id="builder-version-slideout-rename-dropdown"
                        disableDelete={
                          // @ts-ignore
                          examplesQuery.data?.allExamples.length <= 1
                        }
                      />
                      <StatusDropdown
                        status={example.status}
                        isOpen={currentlyOpenStatusDropdown === example.id}
                        data-tracking-id="builder-version-slideout-version-dropdown"
                        onChange={(newStatus) => {
                          setStatusChange({
                            fromStatus: example.status,
                            toStatus: newStatus,
                            exampleSetId: example.id,
                          });
                          setCurrentlyOpenStatusDropdown(null);
                        }}
                        onOpenChange={(isOpen) => {
                          setCurrentlyOpenStatusDropdown(isOpen ? example.id : null);
                        }}
                      />
                    </Styled.VersionItem>
                  );
                })}
              </Styled.VersionList>
            </Styled.Versions>
          </SideDrawer.Body>
        </SideDrawer>
      )}
    </>
  );
};
