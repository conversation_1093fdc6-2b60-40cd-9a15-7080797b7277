import styled from "styled-components";
import { t } from "@/utils/i18n";
import {
  Button,
  Flex,
  LinkButton,
  Table,
  THead,
  TBody,
  TH,
  TR,
  TD,
  TableStatusBadge,
  ZeroItemsEmptyState,
  DropdownPanel,
  IconButton,
  // @ts-ignore
} from "@floqastinc/flow-ui_core";
// @ts-ignore
import MoreVert from "@floqastinc/flow-ui_icons/material/MoreVert";
// @ts-ignore
import DeleteOutline from "@floqastinc/flow-ui_icons/material/DeleteOutline";
// @ts-ignore
import HistoryOutlined from "@floqastinc/flow-ui_icons/material/HistoryOutlined";
// @ts-ignore
import EditOutlined from "@floqastinc/flow-ui_icons/material/EditOutlined";
// @ts-ignore
import MagicAiStar from "@floqastinc/flow-ui_icons/fq/MagicAIStar";
// @ts-ignores
import PlayCircleOutlined from "@floqastinc/flow-ui_icons/material/PlayCircleOutlined";

import _ from "lodash";
import { RunStatus, UpdateWorkflowRunParams, WorkflowRun } from "@floqastinc/transform-v2";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { Principal } from "@floqastinc/transform-v0";
import { Audit } from "./Audit";
import { useModal } from "@/components/ModalContext";
import { Time } from "@/components/Time";
import v3 from "@/services/v3";
import { AGENT, BUILDER, RUNNER, RUNS, V3, AGENTS } from "@/constants";

const IconWrap = styled.div`
  float: right;
  display: flex;
`;

const DropdownPanelText = styled.span`
  margin-top: 3px;
`;

export type WorkflowRunTableProps = {
  activeWorkflowId: string | undefined;
  workflowRuns: WorkflowRun[];
  principals: Principal[];
  onReload: () => void;
  onRunClick: (workflowId: string) => void;
  onHistoryClick: (workflowId: string) => void;
};

export const WorkflowRunTable = ({
  activeWorkflowId,
  workflowRuns,
  principals,
  onReload,
  onRunClick,
  onHistoryClick,
}: WorkflowRunTableProps) => {
  const { openModal } = useModal();
  const navigate = useNavigate();
  const [isMoreOptionsOpenForRunId, setIsMoreOptionsOpenForRunId] = useState<string>();

  const editWorkflowRun = useMutation({
    mutationFn: (params: UpdateWorkflowRunParams) => {
      return v3.runs.updateWorkflowRun(params);
    },
    onSuccess: () => {
      onReload();
    },
  });

  const deleteWorkflowRun = useMutation({
    mutationFn: (id: string) => {
      return v3.runs.deleteWorkflowRun({ workflowRunId: id });
    },
    onSuccess: () => {
      onReload();
    },
  });

  if (workflowRuns.length === 0) {
    return (
      <Flex justify="center" style={{ marginTop: "4em" }}>
        <ZeroItemsEmptyState
          title={
            t("components.WorkflowRunTable.this") +
            `${AGENT.toLocaleLowerCase()}` +
            t("components.WorkflowRunTable.notYetRun")
          }
          subtitle={t("components.WorkflowRunTable.workFlowRunTableSubtitle")}
          primaryButton={
            !!activeWorkflowId && (
              <Button onClick={() => onRunClick(activeWorkflowId)}>
                {`Run ${AGENT.toLocaleLowerCase()}`}
              </Button>
            )
          }
        />
      </Flex>
    );
  }

  return (
    <Table topBorder middleBorders outerRoundedBorder>
      <THead>
        <TR>
          <TH>{t("components.WorkflowRunTable.status")}</TH>
          <TH>{t("components.WorkflowRunTable.name")}</TH>
          <TH>{t("components.WorkflowRunTable.startedAt")}</TH>
          <TH>{t("components.WorkflowRunTable.runBy")}</TH>
          <TH></TH>
        </TR>
      </THead>
      <TBody>
        {workflowRuns?.map((run) => (
          <TR key={run.id}>
            <TD>
              <Badge status={run.status} />
            </TD>
            <TD>
              <Flex direction="column" gap="8">
                <LinkButton
                  onClick={() =>
                    navigate(`/${RUNNER}/${V3}/${AGENTS}/${run.workflowId}/${RUNS}/${run.id}`)
                  }
                >
                  {run.name}
                </LinkButton>
                {!!run.notes && <p>{run.notes}</p>}
              </Flex>
            </TD>
            <TD>
              <Time value={run.createdAt} relative />
            </TD>
            <TD>
              <Audit principalId={run.createdBy} principals={principals} />
            </TD>
            <TD>
              <DropdownPanel
                disableFilter
                disableClear
                isOpen={isMoreOptionsOpenForRunId === run.id}
                onOpenChange={(isOpen: boolean) => {
                  if (isOpen) {
                    setIsMoreOptionsOpenForRunId(run.id);
                  } else {
                    setIsMoreOptionsOpenForRunId(undefined);
                  }
                }}
                onChange={(value: string) => {
                  if (value === "edit-notes") {
                    openModal("EditWorkflowRunNotes", {
                      name: run.name,
                      notes: run.notes,
                      onSubmit: (editedNotes: string) =>
                        editWorkflowRun.mutate({
                          workflowRunId: run.id,
                          run: { notes: editedNotes },
                        }),
                    });
                  }
                  if (value === "view-history") {
                    onHistoryClick(run.workflowId);
                  }
                  if (value === "run-again") {
                    onRunClick(run.workflowId);
                  }
                  if (value === "view-workflow") {
                    navigate(`/${BUILDER}/${V3}/${AGENTS}/${run.workflowId}`);
                  }
                  if (value === "delete-run") {
                    openModal("DeleteWorkflowRun", {
                      onSubmit: () => deleteWorkflowRun.mutate(run.id),
                    });
                  }
                  setIsMoreOptionsOpenForRunId(undefined);
                }}
              >
                <DropdownPanel.Trigger>
                  <IconButton
                    size="md"
                    onClick={() => {
                      setIsMoreOptionsOpenForRunId(run.id);
                    }}
                  >
                    <MoreVert color="#1D2433" />
                  </IconButton>
                </DropdownPanel.Trigger>
                <DropdownPanel.Content>
                  <DropdownPanel.Group>
                    <DropdownPanel.Option value="edit-notes" key="edit-notes">
                      <IconWrap>
                        <EditOutlined color="action" />
                      </IconWrap>
                      <DropdownPanelText>
                        {t("components.WorkflowRunTable.editNotes")}
                      </DropdownPanelText>
                    </DropdownPanel.Option>
                  </DropdownPanel.Group>
                  <DropdownPanel.Group>
                    <DropdownPanel.Option value="run-again" key="run-again">
                      <IconWrap>
                        <PlayCircleOutlined color="action" />
                      </IconWrap>
                      <DropdownPanelText>
                        {t("components.WorkflowRunTable.runAgain")}
                      </DropdownPanelText>
                    </DropdownPanel.Option>
                    <DropdownPanel.Option value="view-history" key="view-history">
                      <IconWrap>
                        <HistoryOutlined color="action" />
                      </IconWrap>
                      <DropdownPanelText>
                        {t("components.WorkflowRunTable.viewRunHistory")}
                      </DropdownPanelText>
                    </DropdownPanel.Option>
                    <DropdownPanel.Option value="view-workflow" key="view-workflow">
                      <IconWrap>
                        <MagicAiStar color="action" />
                      </IconWrap>
                      <DropdownPanelText>
                        {t("components.WorkflowRunTable.view")} {AGENT.toLocaleLowerCase()}
                      </DropdownPanelText>
                    </DropdownPanel.Option>
                  </DropdownPanel.Group>
                  <DropdownPanel.Group>
                    <DropdownPanel.Option value="delete-run" key="delete-run">
                      <IconWrap>
                        <DeleteOutline color="#d23340" />
                      </IconWrap>
                      <DropdownPanelText color="#d23340">
                        {t("components.WorkflowRunTable.deleteRun")}
                      </DropdownPanelText>
                    </DropdownPanel.Option>
                  </DropdownPanel.Group>
                </DropdownPanel.Content>
              </DropdownPanel>
            </TD>
          </TR>
        ))}
      </TBody>
    </Table>
  );
};

const Badge: React.FC<{ status: RunStatus }> = ({ status }) => {
  let color = "info";
  switch (status) {
    case "CANCELED":
    case "FAILED":
      color = "danger";
      break;
    case "COMPLETED":
      color = "success";
      break;
    case "BLOCKED":
    case "PENDING":
      color = "warning";
      break;
  }

  return (
    <TableStatusBadge color={color} hasIcon={false}>
      {status}
    </TableStatusBadge>
  );
};
