import { Principal } from "@floqastinc/transform-v0";
import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { t } from "@/utils/i18n";
import v0, { ApiError } from "@/services/v0";
import { getPrincipalDisplayName } from "@/utils/getPrincipalDisplayName";

export interface Auditable {
  createdById?: string;
  updatedById?: string;
  createdBy?: string;
  updatedBy?: string;
}

const usePrincipalIds = (auditables: Auditable[]) => {
  return useMemo(() => {
    const ids = new Set<string>();
    for (const auditable of auditables) {
      if (auditable.createdById) ids.add(auditable.createdById);
      if (auditable.updatedById) ids.add(auditable.updatedById);
      if (auditable.createdBy) ids.add(auditable.createdBy);
      if (auditable.updatedBy) ids.add(auditable.updatedBy);
    }
    return Array.from(ids);
  }, [auditables]);
};

export const getPrincipalsQuery = (principalIds: string[]) => {
  return {
    queryKey: ["/v0/principals", { ids: principalIds }],
    queryFn: async () => {
      const { data, errors } = await v0.principals.getPrincipals({
        ids: principalIds,
      });
      if (errors.length) {
        throw new ApiError(errors);
      }
      if (!data) {
        throw new Error(t("components.Audit.Errors.failedPrincipals"));
      }
      return data;
    },
  };
};

export const usePrincipals = (auditables: Auditable[]) => {
  const principalIds = usePrincipalIds(auditables);
  return useQuery(getPrincipalsQuery(principalIds));
};

export const useSuspensePrincipals = (auditables: Auditable[]) => {
  const principalIds = usePrincipalIds(auditables);
  return useSuspenseQuery(getPrincipalsQuery(principalIds));
};

export interface AuditProps {
  principalId: string;
  principals: Principal[];
}

export const Audit: React.FC<{
  principalId: string;
  principals: Principal[];
}> = ({ principalId, principals }) => {
  if (!principalId) return null;
  const principal = principals.find((p) => p.id === principalId);
  if (!principal) return null;

  return <>{getPrincipalDisplayName(principal)}</>;
};
