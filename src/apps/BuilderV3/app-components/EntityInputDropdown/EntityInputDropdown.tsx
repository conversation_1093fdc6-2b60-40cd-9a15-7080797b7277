import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { t } from "@/utils/i18n";
import { DropdownPanel, DropdownButton } from "@floqastinc/flow-ui_core";
import { Entity } from "@floqastinc/transform-v3";
import v3, { ApiError } from "@/services/v3";

export const EntityInputDropdown = ({
  entityId,
  setEntity,
}: {
  entityId: string;
  setEntity: (entity: Entity) => void;
}) => {
  const [selectedValue, setSelectedValue] = useState<string | undefined>();
  const [isOpen, setIsOpen] = useState(false);

  const { data } = useQuery({
    queryKey: ["entities", "list"],
    queryFn: async () => {
      const response = await v3.entities.getEntities();
      if (response.errors.length) {
        throw new ApiError(response.errors);
      }
      return response.data;
    },
    retry: false,
  });

  const handleOnChange = (value: string | undefined) => {
    setSelectedValue(value);
    const selectedEntity = getSelectedEntity(value);
    if (selectedEntity) {
      setEntity(selectedEntity);
    }
    setIsOpen(false);
  };

  const getSelectedEntity = (value?: string | undefined): Entity | undefined => {
    const searchValue = value || selectedValue || entityId;
    return data?.find((entity) => searchValue && entity.id === searchValue);
  };

  return (
    <div>
      <DropdownPanel
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        onChange={handleOnChange}
        selectedValues={selectedValue}
        selectionMode="single"
      >
        <DropdownPanel.Trigger>
          <DropdownButton open={isOpen} isRequired label="Entity" variant="default">
            {getSelectedEntity()?.name || t("components.EntityInputDropdown.selectOption")}
          </DropdownButton>
        </DropdownPanel.Trigger>
        <DropdownPanel.Content style={{ maxHeight: "300px", overflowY: "auto", zIndex: 9000 }}>
          {data?.map((entity: Entity) => (
            <DropdownPanel.Option key={entity.id} value={entity.id} name={entity.name}>
              {entity.name}
            </DropdownPanel.Option>
          ))}
        </DropdownPanel.Content>
      </DropdownPanel>
    </div>
  );
};
