import { ThumbsDown } from "@/svg/ThumbsDown";
import { ThumbsUp } from "@/svg/ThumbsUp";
import { Button } from "@floqastinc/flow-ui_core";
import { useEffect, useState } from "react";

export type FeedbackType = "upVote" | "downVote";

type FeedbackButtonsProps = {
  messageId?: string;
  onFeedback?: (type: FeedbackType, messageId?: string) => void;
  disabled?: boolean;
};

const SELECTED_COLOR = "#1FAC76";
const DEFAULT_COLOR = "#6B7280";

export const FeedbackButtons: React.FC<FeedbackButtonsProps> = ({
  messageId,
  onFeedback,
  disabled = false,
}) => {
  const storageKey = `sentiment-${messageId}`;
  const [selected, setSelected] = useState<FeedbackType | null>(() => {
    if (!storageKey) return null;
    const stored = localStorage.getItem(storageKey);
    return stored === "upVote" || stored === "downVote" ? stored : null;
  });

  useEffect(() => {
    if (selected && storageKey) {
      localStorage.setItem(storageKey, selected);
    }
  }, [selected, storageKey]);

  const handleClick = (type: FeedbackType) => {
    if (isVoted) return;
    setSelected(type);
    if (onFeedback) onFeedback(type);
  };

  const isVoted = selected !== null || disabled;

  return (
    <div style={{ display: "flex", gap: 4, marginTop: 4 }}>
      <Button
        size="sm"
        variant={selected === "upVote" ? "solid" : "ghost"}
        aria-label="Thumbs Up"
        onClick={() => handleClick("upVote")}
        disabled={isVoted}
        style={{ border: "none", padding: 0 }}
        aria-selected={selected === "upVote"}
      >
        <ThumbsUp color={selected === "upVote" ? SELECTED_COLOR : DEFAULT_COLOR} />
      </Button>
      <Button
        size="sm"
        variant={selected === "downVote" ? "solid" : "ghost"}
        aria-label="Thumbs Down"
        onClick={() => handleClick("downVote")}
        disabled={isVoted}
        style={{ border: "none", padding: 0 }}
        aria-selected={selected === "downVote"}
      >
        <ThumbsDown color={selected === "downVote" ? SELECTED_COLOR : DEFAULT_COLOR} />
      </Button>
    </div>
  );
};
