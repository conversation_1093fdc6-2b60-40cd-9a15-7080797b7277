import { t } from "@/utils/i18n";
import { Icon<PERSON><PERSON>on, Tooltip } from "@floqastinc/flow-ui_core";
import Add from "@floqastinc/flow-ui_icons/material/Add";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ExampleSet, Task } from "@floqastinc/transform-v3";
import { useParams } from "react-router";
import { useAtom } from "jotai";
import { useModal } from "@/components";
import { getExamplesQuery } from "@BuilderV3/api/examples";
import v3 from "@/services/v3";
import { queryClient } from "@/components/queryClient";
import { queryKeys } from "@BuilderV3/api/query-keys";
import { useTaskQueries } from "@BuilderV3/routes/workflows/BuilderPage.hooks";
import { getWorkflowTasksQuery } from "@BuilderV3/api/tasks";
import { shouldRefetchTasksAtom } from "@BuilderV3/routes/workflows/BuilderPage.store";
import { getIsChatBasedStrategy } from "@/utils/strategy";

export const AddStepButton = () => {
  const { workflowId, taskId, exampleSetId } = useParams();

  if (!workflowId || !taskId || !exampleSetId) {
    console.error("No workflowId, taskId, or exampleSetId provided in params for Builder Page");
    throw new Error("No workflowId, taskId, or exampleSetId provided in params for Builder Page");
  }

  const { openModal } = useModal();

  const { createTaskMutation } = useTaskQueries();

  const tasksQuery = useQuery(getWorkflowTasksQuery(workflowId));
  const lastTask = tasksQuery.data?.[tasksQuery.data.length - 1];
  const isLastTask = lastTask?.id === taskId;

  const examplesQuery = useQuery(getExamplesQuery({ workflowId, taskId }));

  const [_, setRefetchTasksOverride] = useAtom(shouldRefetchTasksAtom);
  const updateExampleMutation = useMutation({
    mutationFn: (example: Partial<ExampleSet>) => {
      return v3.examples.updateExample({
        workflowId,
        taskId,
        exampleSetId,
        example,
      });
    },
    onSuccess: (exampleResponse) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.examples.getExample({
          workflowId,
          taskId,
          exampleSetId,
        }),
      });
      if (
        getIsChatBasedStrategy(exampleResponse.data?.strategy) &&
        exampleResponse.data?.status === "ACTIVE"
      ) {
        setRefetchTasksOverride(true);
        queryClient.invalidateQueries({
          queryKey: queryKeys.tasks.byWorkflow(workflowId),
        });
      }
    },
  });

  const isPublished = examplesQuery.data?.some((example) => example.status === "ACTIVE");

  const shouldDisableAddStepIcon = !(isLastTask && isPublished);

  return (
    <Tooltip>
      <Tooltip.Trigger>
        <IconButton
          data-tracking-id="builder-steps-view-add-step-button"
          disabled={shouldDisableAddStepIcon}
          onClick={() => {
            openModal("AddTask", {
              onSave: (task: Task) => {
                const noActiveExamples =
                  examplesQuery.data?.filter((exSet) => exSet.status === "ACTIVE").length === 0;
                if (noActiveExamples) {
                  updateExampleMutation.mutate({
                    status: "ACTIVE",
                  });
                }
                return createTaskMutation.mutate(task);
              },
            });
          }}
        >
          <Add height={24} width={24} />
        </IconButton>
      </Tooltip.Trigger>
      <Tooltip.Content hasArrow>
        {shouldDisableAddStepIcon
          ? t("components.AddStepButton.unableCreateNewStep")
          : t("components.AddStepButton.addNewStep")}
      </Tooltip.Content>
    </Tooltip>
  );
};
