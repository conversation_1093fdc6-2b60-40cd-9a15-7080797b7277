import { <PERSON><PERSON><PERSON><PERSON>, ArcherE<PERSON> } from "react-archer";
import { useNavigate, useParams } from "react-router";
import { t } from "@/utils/i18n";
import { IconButton, Heading, Button, DropdownPanel } from "@floqastinc/flow-ui_core";
import MoreVert from "@floqastinc/flow-ui_icons/material/MoreVert";
import Delete from "@floqastinc/flow-ui_icons/material/Delete";
import KeyboardArrowDown from "@floqastinc/flow-ui_icons/material/KeyboardArrowDown";
import { Task } from "@floqastinc/transform-v3";
import { useState } from "react";
import { ScopeProvider } from "jotai-scope";
import { ConfirmStepDeletionDialog } from "./ConfirmStepDeletionDialog";
import * as Styled from "./styled";
import { confirmDeleteDialogOpenAtom } from "./store";
import { AddStepButton } from "./AddStepButton";
import { useTaskQueries } from "@BuilderV3/routes/workflows/BuilderPage.hooks";
import { StyledLinkButton } from "@/components";
import { VersionDropdown } from "@BuilderV3/routes/workflows/components/VersionDropdown/VersionDropdown";

const Step = ({ task, stepNumber }: { task: Task; stepNumber: number }) => {
  const { workflowId, taskId } = useParams();
  if (!workflowId || !taskId) {
    console.error(t("components.Steps.Errors.noIDsprovided"));
    throw new Error(t("components.Steps.Errors.noIDsprovided"));
  }

  const navigate = useNavigate();

  const [isOptionsOpen, setIsOptionsOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedExampleId, setSelectedExampleId] = useState<string | undefined>();

  const { tasksQuery } = useTaskQueries();
  const tasks = tasksQuery.data ?? [];
  const isOnlyStep = tasks.length === 1;

  // NOTE: The <Styled.Step> styled component is placed _outside_ of this
  // component in order for react-archer to properly find the div.
  // Something about having it inside this component causes the svg
  // line to fail to exist.
  return (
    <>
      <div style={{ position: "absolute" }}>
        <ConfirmStepDeletionDialog
          isOpen={isDeleteDialogOpen}
          task={task}
          deleteType="delete-step"
          onConfirm={() => {
            setIsOptionsOpen(false);
            setIsDeleteDialogOpen(false);
          }}
          onOpenChange={setIsDeleteDialogOpen}
        />
      </div>
      <Styled.TaskInfo>
        <Styled.StepHeading>
          <Heading variant="body-base" weight="semibold">
            Step {stepNumber}:
          </Heading>
          <VersionDropdown
            taskId={task.id}
            selectedExampleId={selectedExampleId}
            onChange={(selectedExampleId) => {
              // TODO: This should have some sort of server interaction here instead of
              // just client-side.
              // What do we want to have happen when a user sets a task in the step?
              // TODO: This is also dependent on usability testing (i.e. too difficult, etc.)
              setSelectedExampleId(selectedExampleId);
              if (taskId === task.id) {
                navigate(
                  `/builder/v3/agents/${workflowId}/steps/${task.id}/examples/${selectedExampleId}`,
                );
              }
            }}
            trigger={(selectedValue) => (
              <Styled.VersionDropdownButton>
                <StyledLinkButton>{selectedValue ?? ""}</StyledLinkButton>
                <KeyboardArrowDown color="var(--flo-base-color-neutral-500)" size={16} />
              </Styled.VersionDropdownButton>
            )}
          />
        </Styled.StepHeading>
        <Styled.TaskDescription>{task.name}</Styled.TaskDescription>
      </Styled.TaskInfo>
      <Styled.Controls>
        <Button
          variant="outlined"
          color="dark"
          onClick={() => {
            navigate(`/builder/v3/agents/${workflowId}/steps/${task.id}`);
          }}
        >
          {t("components.Steps.view")}
        </Button>
        {/* Show delete option if it's not the only step */}
        {!isOnlyStep ? (
          <DropdownPanel
            isOpen={isOptionsOpen}
            onOpenChange={setIsOptionsOpen}
            disableClear
            disableFilter
            onChange={(value: string) => {
              if (value === "delete") {
                setIsDeleteDialogOpen(true);
              }
            }}
          >
            <DropdownPanel.Trigger>
              <IconButton>
                <MoreVert />
              </IconButton>
            </DropdownPanel.Trigger>
            <DropdownPanel.Content>
              <DropdownPanel.Option value="delete">
                <Styled.DeleteOption>
                  <Delete color="#D24747" />
                  <Styled.DeleteText>{t("components.Steps.delete")}</Styled.DeleteText>
                </Styled.DeleteOption>
              </DropdownPanel.Option>
            </DropdownPanel.Content>
          </DropdownPanel>
        ) : (
          <div style={{ width: 36 }} />
        )}
      </Styled.Controls>
    </>
  );
};

export const Steps = () => {
  const { workflowId, taskId, exampleSetId } = useParams();

  if (!workflowId || !taskId || !exampleSetId) {
    console.error(t("components.Steps.Errors.noIDsprovided"));
    throw new Error(t("components.Steps.Errors.noIDsprovided"));
  }

  const { tasksQuery } = useTaskQueries();
  const tasks = tasksQuery.data ?? [];

  return (
    <ScopeProvider atoms={[confirmDeleteDialogOpenAtom]}>
      <ArcherContainer endMarker={false}>
        <Styled.StepContainer>
          {tasks.map((task, i) => {
            if (i === tasks.length - 1)
              return (
                <ArcherElement
                  id={task.id}
                  key={task.id}
                  relations={[
                    {
                      targetId: "add-step",
                      targetAnchor: "top",
                      sourceAnchor: "bottom",
                      style: { strokeColor: "#E1E6EF" },
                    },
                  ]}
                >
                  <Styled.Step active={task.id === taskId} tabIndex={1}>
                    <Step task={task} stepNumber={i + 1} />
                  </Styled.Step>
                </ArcherElement>
              );
            return (
              <ArcherElement
                id={task.id}
                key={task.id}
                relations={[
                  {
                    targetId: tasks[i + 1].id,
                    targetAnchor: "top",
                    sourceAnchor: "bottom",
                    style: { strokeColor: "#E1E6EF" },
                  },
                ]}
              >
                <Styled.Step active={task.id === taskId} tabIndex={1}>
                  <Step task={task} stepNumber={i + 1} />
                </Styled.Step>
              </ArcherElement>
            );
          })}
          <ArcherElement id="add-step" relations={[]}>
            <Styled.AddStep tabIndex={1}>
              <AddStepButton />
            </Styled.AddStep>
          </ArcherElement>
        </Styled.StepContainer>
      </ArcherContainer>
    </ScopeProvider>
  );
};
