// @ts-ignore
import { <PERSON><PERSON>, <PERSON><PERSON>, But<PERSON> } from "@floqastinc/flow-ui_core";
import { t } from "@/utils/i18n";
import { Overlay, ModalWrapper, ModalHeader, Form } from "./styled";
import { useModal } from "@/components";
import { AGENT } from "@/constants";

export interface DeleteWorkflowRunModalProps {
  onSubmit: () => void;
}
export const DeleteWorkflowRunModal: React.FC<DeleteWorkflowRunModalProps> = ({ onSubmit }) => {
  const { closeModal } = useModal();

  const handleSubmit: React.FormEventHandler<HTMLFormElement> = (event) => {
    event.preventDefault();
    onSubmit();
    closeModal();
  };

  return (
    <Overlay onClick={closeModal}>
      <ModalWrapper onClick={(e) => e.stopPropagation()}>
        <ModalHeader>
          {t("components.DeleteWorkflowRunModal.confirm")} {AGENT}{" "}
          {t("components.DeleteWorkflowRunModal.runDeletion")}
        </ModalHeader>
        <Heading variant="body-base" weight="medium">
          <Flex style={{ marginTop: "1em" }} direction="column" gap="8">
            <p>
              {t("components.DeleteWorkflowRunModal.areYouSure")} {AGENT.toLocaleLowerCase()} run?
            </p>
            <p>
              {t("components.DeleteWorkflowRunModal.runRemoved1")} {AGENT.toLocaleLowerCase()}
              {t("components.DeleteWorkflowRunModal.allFilesUnchanged")}
            </p>
            <p>{t("components.DeleteWorkflowRunModal.cannotUndo")}</p>
          </Flex>
        </Heading>
        <Form onSubmit={handleSubmit}>
          <Button type="submit" color="danger">
            {t("components.DeleteWorkflowRunModal.confirm")}
          </Button>
          <Button type="button" onClick={closeModal} color="dark" variant="outlined">
            {t("components.DeleteWorkflowRunModal.cancel")}
          </Button>
        </Form>
      </ModalWrapper>
    </Overlay>
  );
};
