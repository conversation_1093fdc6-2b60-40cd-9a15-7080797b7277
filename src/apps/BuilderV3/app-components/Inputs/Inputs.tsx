import { ButtonGroup, <PERSON><PERSON>, IconButton, Tooltip } from "@floqastinc/flow-ui_core";
import { t } from "@/utils/i18n";
import React from "react";
import { useParams } from "react-router";
import Add from "@floqastinc/flow-ui_icons/material/Add";
import Clear from "@floqastinc/flow-ui_icons/material/Clear";
import { ArcherContainer, ArcherElement } from "react-archer";
import { InputValue, DropdownOptions } from "../../routes/workflows/components/SQLEditor/types";
import { AddStep, Form, StepContainer, TextInput } from "./styled";
import { useUpdateInputValues } from "./useUpdatedInputValues";

interface InputsProps {
  inputValues: InputValue[];
  setInputValues: React.Dispatch<React.SetStateAction<InputValue[]>>;
  isDraft: boolean;
}

export const Inputs: React.FC<InputsProps> = ({ inputValues, setInputValues, isDraft }) => {
  const { workflowId, taskId, exampleSetId } = useParams();

  if (!workflowId || !taskId || !exampleSetId) {
    console.error(t("components.Inputs.Errors.noIDsprovided"));
    throw new Error(t("components.Inputs.Errors.noIDsprovided"));
  }

  useUpdateInputValues({
    setInputValues,
  });

  const handleAddAdditionalInput = () => {
    addInput({
      name: "",
      description: "",
      kind: "TEXT",
      value: "",
      isGenerated: false,
    });
  };

  const handleInputChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setInputValues((prevValues) => {
      const updatedValues = [...prevValues];
      updatedValues[index] = {
        ...updatedValues[index],
        [name]: value,
      };
      return updatedValues;
    });
  };

  const addInput = (input: InputValue) => {
    setInputValues((prevValues) => [...prevValues, { ...input, isGenerated: false }]);
  };

  const handleKindChange = (index: number, kind: "TEXT" | "NUMBER") => {
    setInputValues((prevValues) => {
      const updatedValues = [...prevValues];
      updatedValues[index] = {
        ...updatedValues[index],
        kind,
      };
      return updatedValues;
    });
  };

  const deleteInput = (index: number) => {
    setInputValues((prev) => prev.filter((_, i) => i !== index));
  };

  const renderInputForm = (input: InputValue, index: number) => (
    <Form key={index} onSubmit={(e) => e.preventDefault()}>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Heading variant="body-base" weight="semibold">
          Input for :{index + 1}
        </Heading>
        {!input.isGenerated && (
          <IconButton disabled={!isDraft} onClick={() => deleteInput(index)}>
            <Clear height={16} width={16} />
          </IconButton>
        )}
      </div>
      <TextInput
        disabled={!isDraft || input.isGenerated}
        required
        name="name"
        onChange={(e) => handleInputChange(index, e)}
        placeholder={"Input Name"}
        value={input.name}
        aria-label={`Input Name`}
      />
      <TextInput
        disabled={!isDraft || input.isGenerated}
        required
        name="description"
        onChange={(e) => handleInputChange(index, e)}
        placeholder={"Input Description"}
        value={input.description}
        aria-label={`Input Description`}
      />
      <ButtonGroup>
        <ButtonGroup.Button
          disabled={!isDraft || input.isGenerated}
          onClick={() => handleKindChange(index, "TEXT")}
          isActive={input.kind === "TEXT"}
        >
          Text
        </ButtonGroup.Button>
        <ButtonGroup.Button
          disabled={!isDraft || input.isGenerated}
          onClick={() => handleKindChange(index, "NUMBER")}
          isActive={input.kind === "NUMBER"}
        >
          Number
        </ButtonGroup.Button>
      </ButtonGroup>
      {input.kind === "TEXT" || input.kind === "NUMBER" ? (
        <TextInput
          disabled={!isDraft || input.isGenerated}
          type={input.kind === "NUMBER" ? "number" : "text"}
          aria-label={`${input.kind.toLowerCase()}-input`}
          placeholder={input.kind === "NUMBER" ? "Add Numerical Value" : "Add Value"}
          value={input.value}
          name="value"
          onChange={(e) => handleInputChange(index, e)}
        />
      ) : null}
    </Form>
  );

  return (
    <ArcherContainer endMarker={false}>
      <Heading
        variant="body-base"
        weight="semibold"
        style={{ paddingLeft: "24px", paddingTop: "24px" }}
      >
        Current Inputs
      </Heading>
      <StepContainer>
        {inputValues.map((input, index) => renderInputForm(input, index))}
        <ArcherElement id="add-step" relations={[]}>
          <AddStep tabIndex={1}>
            <Tooltip>
              <Tooltip.Trigger>
                <IconButton disabled={!isDraft} onClick={handleAddAdditionalInput}>
                  <Add height={24} width={24} />
                </IconButton>
              </Tooltip.Trigger>
              <Tooltip.Content hasArrow>
                {isDraft
                  ? "Add Additional Input"
                  : "Cannot add additional inputs to a published workflow"}
              </Tooltip.Content>
            </Tooltip>
          </AddStep>
        </ArcherElement>
      </StepContainer>
    </ArcherContainer>
  );
};
