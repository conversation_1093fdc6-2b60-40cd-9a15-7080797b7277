import { styled } from "styled-components";

export const TextInput = styled.input`
  padding: 8px 12px;
  border: 1px solid var(--flo-base-color-neutral-300);
  border-radius: 6px;
  font-weight: var(--flo-base-font-weight-4);
  font-size: var(--flo-base-font-size-3);
  font-family: var(--flo-sem-font-family-body);
  width: 100%;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05)
  background-color: var(--flo-sem-color-white);
`;

export const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px;
  padding-top: 0px;
  width: 100%;
`;

export const AddStep = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  width: 48px;
  border: 1px solid #e1e6ef;
  border-radius: 6px;
  &:focus {
    border: 1px solid #3d7bf7;
  }
`;

export const StepContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 18px;
`;
