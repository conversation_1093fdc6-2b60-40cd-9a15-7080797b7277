import { getTaskInputsQuery } from "@BuilderV3/api/task-inputs";
import { useExampleQueries } from "@BuilderV3/routes/workflows/BuilderPage.hooks";
import { InputValue } from "@BuilderV3/routes/workflows/components/SQLEditor/types";
import { useQuery } from "@tanstack/react-query";
import { t } from "@/utils/i18n";
import { useEffect } from "react";
import { useParams } from "react-router";

interface UseUpdateInputValuesProps {
  setInputValues: React.Dispatch<React.SetStateAction<InputValue[]>>;
}

export const useUpdateInputValues = ({ setInputValues }: UseUpdateInputValuesProps) => {
  const { workflowId, taskId, exampleSetId } = useParams();

  if (!workflowId || !taskId || !exampleSetId) {
    console.error(t("components.Inputs.Errors.noIDsprovided"));
    throw new Error(t("components.Inputs.Errors.noIDsprovided"));
  }

  const { exampleInputsQuery } = useExampleQueries();
  const { data: taskInputs } = useQuery(getTaskInputsQuery({ workflowId, taskId }));

  useEffect(() => {
    const updatedValues: InputValue[] = [];

    if (
      exampleInputsQuery.data &&
      exampleInputsQuery.data.length > 0 &&
      taskInputs &&
      taskInputs.length > 0
    ) {
      const exampleInputMap = new Map(
        exampleInputsQuery.data.map((input) => [input.taskInputId, input]),
      );
      taskInputs.forEach((input, i) => {
        const exampleInput = exampleInputMap.get(input.id);

        updatedValues[i] = {
          ...updatedValues[i],
          name: input.name,
          description: input.description,
          kind: input.type,
          value: exampleInput?.value?.value || "",
          isGenerated: true,
          workflowInputId: input.source.workflowInputId,
        };
      });
    }

    setInputValues(updatedValues);
  }, [exampleInputsQuery.data, taskInputs]);
};
