import * as apiSdk from "@floqastinc/transform-v3";
import { Task, TaskInput } from "@floqastinc/transform-v3";
import { queryOptions } from "@tanstack/react-query";
import { queryKeys } from "./query-keys";
import type { QueryContext, TaskId, WorkflowId } from "./types";
import { t } from "@/utils/i18n";
import v3, { ApiError } from "@/services/v3";
import { featureFlags } from "@/components/FeatureFlag";

const getWorkflowTasks = async ({
  queryKey: [
    {
      filter: { workflowId },
    },
  ],
}: QueryContext<(typeof queryKeys.tasks)["byWorkflow"]>) => {
  const { data, errors } = await v3.tasks.getTasks({ workflowId });
  if (errors.length) {
    throw new ApiError(errors);
  }
  if (!data) {
    throw new Error(t("components.BuilderV3API.Errors.failedGetTask"));
  }
  return data;
};

export const getWorkflowTasksQuery = (workflowId: string) => {
  return queryOptions({
    queryKey: queryKeys.tasks.byWorkflow(workflowId),
    queryFn: getWorkflowTasks,
  });
};

type RevertTaskArgs = {
  workflowId: string;
  taskId: string;
  tasks: apiSdk.Task[];
};
export const revertTaskMutation = async ({ workflowId, taskId, tasks }: RevertTaskArgs) => {
  const taskToRevertToIndex = tasks.findIndex((task) => task.id === taskId);

  if (taskToRevertToIndex === -1) {
    throw new Error(t("components.BuilderV3API.Errors.noTaskRevert"));
  }

  const tasksToRemove = tasks.slice(taskToRevertToIndex + 1).map((task) => task.id);
  await Promise.all(tasksToRemove.map((taskId) => v3.tasks.deleteTask({ workflowId, taskId })));

  // point workflow output source to last task after reverting
  const revertedTasks = tasks.slice(0, taskToRevertToIndex + 1);
  const lastTask = revertedTasks[revertedTasks.length - 1];
  const { data: taskOutputsRes } = await v3.taskOutputs.getTaskOutputs({
    taskId: lastTask.id,
    workflowId,
  });
  const { data: workflowOutputsRes } = await v3.workflowOutputs.getWorkflowOutputs({ workflowId });
  await v3.workflowOutputs.updateWorkflowOutput({
    workflowId,
    workflowOutputId: workflowOutputsRes[0].id,
    output: {
      source: { taskId: lastTask.id, taskOutputId: taskOutputsRes[0].id },
    },
  });
};

const getWorkflowTask = async ({
  queryKey: [
    {
      filter: { workflowId },
    },
    {
      filter: { taskId },
    },
  ],
}: QueryContext<(typeof queryKeys.tasks)["byId"]>) => {
  const { data, errors } = await v3.tasks.getTask({ workflowId, taskId });
  if (errors.length) {
    throw new ApiError(errors);
  }
  if (!data) {
    throw new Error(t("components.BuilderV3API.Errors.failedGetTask"));
  }
  return data;
};

export const getWorkflowTaskQuery = ({ workflowId, taskId }: WorkflowId & TaskId) => {
  return queryOptions({
    queryKey: queryKeys.tasks.byId({ workflowId, taskId }),
    queryFn: getWorkflowTask,
  });
};

type DeleteTaskArgs = {
  workflowId: string;
  taskId: string;
  tasks: apiSdk.Task[];
  deleteAfter?: boolean;
};

const deleteTaskAndTasksAfter = async ({
  workflowId,
  taskToDeleteIndex,
  tasks,
}: {
  workflowId: string;
  taskToDeleteIndex: number;
  tasks: apiSdk.Task[];
}) => {
  const prevTask = tasks[taskToDeleteIndex - 1];
  const { data: taskOutputsRes } = await v3.taskOutputs.getTaskOutputs({
    taskId: prevTask.id,
    workflowId,
  });

  const tasksToRemove = tasks.slice(taskToDeleteIndex).map((task) => task.id);

  await Promise.all(tasksToRemove.map((taskId) => v3.tasks.deleteTask({ workflowId, taskId })));

  const { data: workflowOutputsRes } = await v3.workflowOutputs.getWorkflowOutputs({
    workflowId,
  });

  await v3.workflowOutputs.updateWorkflowOutput({
    workflowId,
    workflowOutputId: workflowOutputsRes[0]?.id,
    output: {
      source: { taskId: prevTask.id, taskOutputId: taskOutputsRes[0]?.id },
    },
  });

  return prevTask;
};

export const deleteTaskMutation = async ({
  workflowId,
  taskId,
  tasks,
  deleteAfter = false,
}: DeleteTaskArgs) => {
  const taskToDeleteIndex = tasks.findIndex((task) => task.id === taskId);

  if (taskToDeleteIndex === -1) {
    throw new Error(t("components.BuilderV3API.Errors.noTaskDelete"));
  }

  const isASMEnabled = featureFlags.get("transform-advanced-step-manager");

  if (isASMEnabled) {
    if (!deleteAfter) {
      if (taskToDeleteIndex === 0) {
        if (taskToDeleteIndex + 1 >= tasks.length) {
          throw new Error("Cannot delete first task when it's the only task");
        }

        const nextTask = tasks[taskToDeleteIndex + 1];

        const { data: nextTaskInputsRes = [] } = await v3.taskInputs.getTaskInputs({
          taskId: nextTask.id,
          workflowId,
        });

        const { data: workflowInputsRes = [] } = await v3.workflowInputs.getWorkflowInputs({
          workflowId,
        });

        await v3.workflowInputs.updateWorkflowInput({
          workflowId,
          workflowInputId: workflowInputsRes[0]?.id,
          input: {
            name: nextTaskInputsRes[0]?.name,
          },
        });

        await v3.taskInputs.updateTaskInput({
          workflowId,
          taskId: nextTask.id,
          taskInputId: nextTaskInputsRes[0]?.id,
          input: {
            source: {
              workflowInputId: workflowInputsRes[0]?.id,
            },
          },
        });

        await v3.tasks.deleteTask({ workflowId, taskId });

        return nextTask;
      } else if (taskToDeleteIndex === tasks.length - 1) {
        const prevTask = tasks[taskToDeleteIndex - 1];

        const { data: taskOutputsRes } = await v3.taskOutputs.getTaskOutputs({
          taskId: prevTask.id,
          workflowId,
        });

        await v3.tasks.deleteTask({ workflowId, taskId });

        const { data: workflowOutputsRes } = await v3.workflowOutputs.getWorkflowOutputs({
          workflowId,
        });
        await v3.workflowOutputs.updateWorkflowOutput({
          workflowId,
          workflowOutputId: workflowOutputsRes[0]?.id,
          output: {
            source: { taskId: prevTask.id, taskOutputId: taskOutputsRes[0]?.id },
          },
        });

        return prevTask;
      } else {
        const prevTask = tasks[taskToDeleteIndex - 1];
        const { data: taskOutputsRes } = await v3.taskOutputs.getTaskOutputs({
          taskId: prevTask.id,
          workflowId,
        });

        const nextTask = tasks[taskToDeleteIndex + 1];

        const { data: taskInputsRes } = await v3.taskInputs.getTaskInputs({
          taskId: nextTask.id,
          workflowId,
        });

        await v3.tasks.deleteTask({ workflowId, taskId });

        await v3.taskInputs.updateTaskInput({
          input: {
            source: { taskId: prevTask.id, taskOutputId: taskOutputsRes[0]?.id },
          },
          workflowId,
          taskId: nextTask.id,
          taskInputId: taskInputsRes[0]?.id,
        });

        return prevTask;
      }
    } else {
      return deleteTaskAndTasksAfter({ workflowId, taskToDeleteIndex, tasks });
    }
  } else {
    return deleteTaskAndTasksAfter({ workflowId, taskToDeleteIndex, tasks });
  }
};

const createTaskInputs = async ({
  workflowId,
  task,
  previousTaskId,
  prevTaskOutputs,
}: {
  workflowId: string;
  task: Task;
  previousTaskId: string;
  prevTaskOutputs: apiSdk.TaskOutput[];
}) => {
  if (task.strategy.kind === "FLOLAKE" || task.strategy.kind === "JEM_TEMPLATE_FETCH") {
    return [];
  }
  const newTaskInputs = [];
  for (const prevTaskOutput of prevTaskOutputs) {
    const source: apiSdk.Source = {
      taskId: previousTaskId,
      taskOutputId: prevTaskOutput.id,
    };
    const createTaskInputQuery = await v3.taskInputs.createTaskInput({
      workflowId,
      taskId: task.id,
      input: {
        name: prevTaskOutput.name,
        type: prevTaskOutput.type,
        source,
      },
    });
    if (createTaskInputQuery.errors.length) {
      throw new ApiError(createTaskInputQuery.errors);
    }
    if (!createTaskInputQuery.data) {
      throw new Error(t("components.BuilderV3API.Errors.failedCreateTaskInput"));
    }

    newTaskInputs.push(createTaskInputQuery.data);
  }
  return newTaskInputs;
};

const createTaskOutputs = async ({ workflowId, task }: { workflowId: string; task: Task }) => {
  let type: apiSdk.DataType = "FILE";
  switch (task.strategy.kind) {
    case "FLOLAKE":
    case "JEM_TEMPLATE_FETCH":
    case "LLM_THREAD":
    case "SCRIPT":
    case "REVIEW":
      type = "FILE";
      break;
    case "JEM_EXPORT":
      type = "TEXT";
      break;
    default:
      throw new Error(t("components.BuilderV3API.Errors.noStrategyType"));
  }
  const { data: taskOutput, errors: createTaskErrors } = await v3.taskOutputs.createTaskOutput({
    workflowId,
    taskId: task.id,
    output: {
      name: `${task.name} Output`,
      type,
    },
  });

  if (createTaskErrors.length) {
    throw new ApiError(createTaskErrors);
  }
  if (!taskOutput) {
    throw new Error(t("components.BuilderV3API.Errors.failedCreateTaskOutput"));
  }
  return taskOutput;
};

type CreateTaskParams = {
  workflowId: string;
  task: apiSdk.NewTask;
  previousTaskId?: string;
};
export const createTask = async ({
  workflowId,
  task,
  previousTaskId,
}: CreateTaskParams): Promise<{
  task: apiSdk.Task;
  taskInputs: apiSdk.TaskInput[] | undefined;
  taskOutput: apiSdk.TaskOutput;
}> => {
  const { data: createdTask, errors } = await v3.tasks.createTask({
    workflowId,
    task,
  });

  if (errors.length) {
    throw new ApiError(errors);
  }
  if (!createdTask) {
    throw new Error(t("components.BuilderV3API.Errors.failedCreateTask"));
  }

  // create new task input using previous task output
  let taskInputs: TaskInput[] | undefined;
  if (previousTaskId) {
    const { data: taskOutputs, errors } = await v3.taskOutputs.getTaskOutputs({
      workflowId,
      taskId: previousTaskId,
    });
    if (errors.length) {
      throw new ApiError(errors);
    }
    if (!taskOutputs) {
      throw new Error(t("components.BuilderV3API.Errors.noPrevTaskOutputs"));
    }

    taskInputs = await createTaskInputs({
      workflowId,
      task: createdTask,
      previousTaskId,
      prevTaskOutputs: taskOutputs,
    });
  }

  const taskOutput = await createTaskOutputs({ workflowId, task: createdTask });

  // TODO: Example sets inputs/outputs

  // point workflow output source to new task output
  const { data: workflowOutputsRes } = await v3.workflowOutputs.getWorkflowOutputs({ workflowId });
  await v3.workflowOutputs.updateWorkflowOutput({
    workflowId,
    workflowOutputId: workflowOutputsRes[0].id,
    output: {
      source: { taskId: createdTask.id, taskOutputId: taskOutput.id },
    },
  });

  return { task: createdTask, taskInputs, taskOutput };
};

type UpdateTaskParams = {
  workflowId: string;
  taskId: string;
  task: apiSdk.TaskUpdate;
};
export const updateTask = async ({
  workflowId,
  taskId,
  task,
}: UpdateTaskParams): Promise<apiSdk.Task> => {
  const { data: updatedTask, errors } = await v3.tasks.updateTask({
    workflowId,
    taskId,
    task,
  });

  if (errors.length) {
    throw new ApiError(errors);
  }
  if (!updatedTask) {
    throw new Error(t("components.BuilderV3API.Errors.failedUpdateTask"));
  }

  return updatedTask;
};
