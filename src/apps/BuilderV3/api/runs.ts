import {
  GetWorkflowRunInputParams,
  GetWorkflowRunInputValueUriParams,
  GetWorkflowRunInputsParams,
  TaskRun,
} from "@floqastinc/transform-v3";
import { queryOptions } from "@tanstack/react-query";
import { queryKeys } from "./query-keys";
import { queryClient } from "@/components";
import { v3, ApiError } from "@/services/v3";
import { createQueryFunction } from "@/utils/query";
import { t } from "@/utils/i18n";

export const getRunStatusQuery = (runId: string) => {
  return queryOptions({
    // TODO: change query key to be similar to others with key typesafety
    //  Not changing as of time of comment since it will cause cascading changes
    //  for query invalidations to continue working.
    queryKey: ["runStatus", runId],
    queryFn: async () => {
      const workflowRun = await v3.runs.getWorkflowRun({
        workflowRunId: runId,
      });

      if (workflowRun.errors.length) {
        throw new Error(t("components.BuilderV3API.Errors.badNetworkResponse"));
      }
      if (!workflowRun.data) {
        throw new Error(t("components.BuilderV3API.Errors.workflowDataUndefined"));
      }

      const taskRuns = await v3.runs.getTaskRuns({
        workflowRunId: runId,
      });
      if (taskRuns.errors.length) {
        throw new Error(t("components.BuilderV3API.Errors.badNetworkResponse"));
      }
      if (!taskRuns.data) {
        throw new Error(t("components.BuilderV3API.Errors.taskDataUndefined"));
      }

      const activeTaskIndex = taskRuns.data.findIndex(
        (taskRun: TaskRun) => taskRun.status === "RUNNING",
      );

      return {
        taskRuns: taskRuns.data,
        activeTaskIndex: activeTaskIndex === -1 ? null : activeTaskIndex,
        activeTask: activeTaskIndex === -1 ? null : taskRuns.data[activeTaskIndex],
        workflowRun: workflowRun.data,
      };
    },
    refetchInterval: (query) => {
      if (query.state.error) {
        return undefined;
      }

      const status = query.state.data?.workflowRun.status;
      const anyTaskPendingReadyOrRunning = query.state.data?.taskRuns.some(
        (taskRun: TaskRun) =>
          taskRun.status === "PENDING" ||
          taskRun.status === "READY" ||
          taskRun.status === "RUNNING",
      );
      if (status === "PENDING" || status === "RUNNING" || anyTaskPendingReadyOrRunning) {
        return 750;
      }

      // Invalidate for run list
      queryClient.invalidateQueries({ queryKey: queryKeys.workflowRuns.all() });

      return undefined;
    },
  });
};

export const getRunInputs = (params: GetWorkflowRunInputsParams) => {
  const getWorkflowRunInputs = createQueryFunction(
    queryKeys.workflowRuns.getWorkflowRunInputs,
    async (params) => {
      if (!params.workflowRunId) {
        throw new Error(t("components.BuilderV3API.Errors.noRunID"));
      }
      const result = await v3.runs.getWorkflowRunInputs(params as GetWorkflowRunInputsParams);
      if (result.errors.length) {
        throw new ApiError(result.errors);
      }
      if (!result.data) {
        throw new Error(t("components.BuilderV3API.Errors.failedWorkflowInputs"));
      }
      return result;
    },
  );

  return queryOptions({
    queryKey: queryKeys.workflowRuns.getWorkflowRunInputs(params),
    queryFn: getWorkflowRunInputs,
  });
};

export const getRunInputValueUri = (params: GetWorkflowRunInputParams) => {
  const getWorkflowRunInputValueUri = createQueryFunction(
    queryKeys.workflowRuns.getWorkflowRunInputValueUri,
    async (params) => {
      if (!params.workflowRunId) {
        throw new Error(t("components.BuilderV3API.Errors.noRunID"));
      }
      const result = await v3.runs.getWorkflowRunInputValueUri(
        params as GetWorkflowRunInputValueUriParams,
      );
      if (result.errors.length) {
        throw new ApiError(result.errors);
      }
      if (!result.data) {
        throw new Error(t("components.BuilderV3API.Errors.workflowInputUri"));
      }
      return result;
    },
  );

  return queryOptions({
    queryKey: queryKeys.workflowRuns.getWorkflowRunInputValueUri(params),
    queryFn: getWorkflowRunInputValueUri,
  });
};
