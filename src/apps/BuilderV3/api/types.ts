import { QueryFunctionContext } from "@tanstack/react-query";

export type QueryContext<T extends (...args: any) => any> = QueryFunctionContext<ReturnType<T>>;

export type InfiniteQueryContext<T extends (...args: any) => any> = QueryFunctionContext<
  ReturnType<T>,
  string | undefined
>;

export type WorkflowId = { workflowId: string };
export type TaskId = { taskId: string };
export type WorkflowInputId = { workflowInputId: string };
export type TaskInputId = { taskInputId: string };
export type FileOptions = { fileOptions: Partial<File> };
export type RunId = { runId: string };

// export  { TaskAssertion } from '@floqastinc/transform-v2';
export type TaskAssertion = { id: string; tests: any[] };
