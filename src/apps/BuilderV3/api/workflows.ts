import { CreateWorkflowParams, GetWorkflowsParams } from "@floqastinc/transform-v3";
import { infiniteQueryOptions, queryOptions } from "@tanstack/react-query";
import { queryKeys } from "./query-keys";
import type { InfiniteQueryContext, QueryContext } from "./types";
import { t } from "@/utils/i18n";

import v3, { ApiError } from "@/services/v3";

export const getWorkflowQuery = (workflowId: string) => {
  return queryOptions({
    queryKey: queryKeys.workflows.byId(workflowId),
    queryFn: getWorkflow,
  });
};

const getWorkflow = async ({
  queryKey: [
    {
      filter: { workflowId },
    },
  ],
}: QueryContext<(typeof queryKeys.workflows)["byId"]>) => {
  const { data: workflow, errors } = await v3.workflows.getWorkflow({
    workflowId,
  });
  if (errors.length) {
    throw new ApiError(errors);
  }
  if (!workflow) {
    throw new Error(t("components.BuilderV3API.Errors.failedToGetWorkflow"));
  }
  return workflow;
};

const getWorkflows = async ({
  queryKey: [{ page }],
  pageParam,
}: InfiniteQueryContext<(typeof queryKeys.workflows)["all"]>) => {
  const workflowsResult = await v3.workflows.getWorkflows({
    ...page,
    after: pageParam,
  });
  if (workflowsResult.errors.length) {
    throw new ApiError(workflowsResult.errors);
  }
  if (!workflowsResult.data) {
    throw new Error(t("components.BuilderV3API.Errors.failedToGetWorkflow"));
  }
  return workflowsResult;
};

/**
 * Returns a query for fetching workflows.
 *
 * Returned data is sorted by name by default, Otherwise sorts by the defaultSorting parameter
 * @param params
 * @param defaultSorting
 */
export const getWorkflowsQuery = (params: GetWorkflowsParams) => {
  return infiniteQueryOptions({
    queryKey: queryKeys.workflows.all(params),
    queryFn: getWorkflows,
    initialPageParam: undefined as string | undefined,
    getNextPageParam: (lastPage, _pages) =>
      lastPage.pageInfo.hasNextPage ? lastPage.pageInfo.endCursor : undefined,
    select: (data) => {
      return {
        pages: data.pages,
        pageParams: data.pages.map((page) => page.pageInfo.endCursor),
        workflows: data.pages.flatMap((page) => page.data),
        hasNextPage: data.pages[data.pages.length - 1].pageInfo.hasNextPage,
      };
    },
  });
};

export const createWorkflow = async ({
  workflow,
  experimentAssignments,
}: CreateWorkflowParams & {
  experimentAssignments?: Record<string, string>;
}) => {
  const { data, errors } = await v3.workflows.createWorkflow({ workflow });

  if (errors.length) {
    throw new ApiError(errors);
  }
  if (!data) {
    throw new Error(t("components.BuilderV3API.Errors.failedCreateWorkflow"));
  }

  await v3.workflowOutputs.createWorkflowOutput({
    workflowId: data.id,
    output: {
      name: "default",
      type: "FILE",
    },
  });

  // Conditionally assign experiment variants if provided and at least one variantId is present
  // in the experimentAssignments object.
  // This is to ensure that we only assign experiments if there are valid assignments.
  if (
    experimentAssignments &&
    Object.entries(experimentAssignments).some(([_, variantId]) => !!variantId)
  ) {
    // Grab each experimentName and variantId from the experimentAssignments object
    // and map them to an experimentAssignments promise for each selected experiment variant.
    // Filter out any entries where the variantId is falsy.
    const assignmentPromises = Object.entries(experimentAssignments)
      .filter(([_, variantId]) => !!variantId)
      .map(([experimentName, variantId]) =>
        v3.experimentAssignments.assignWorkflowToExperiment({
          workflowId: data.id,
          assignment: {
            experimentName,
            variantId,
          },
        }),
      );

    // Run v3.experimentAssignments.assignWorkflowToExperiment() for each selected experiement variant.
    await Promise.all(assignmentPromises);
  }

  return { workflow: data };
};
