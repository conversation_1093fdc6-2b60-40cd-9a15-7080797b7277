import axios from "axios";

export const getFileFromUri = async (uri: string, fileOptions?: Partial<File>) => {
  const fileResponse = await axios.get(uri, { responseType: "blob" });
  const regex = /\.(\w+)(?=\?|$)/;
  const match = uri.match(regex);
  const fileType = match ? match[1] : "xlsx";
  const formattedDate = new Date().toLocaleDateString();
  const fileName = fileOptions?.name
    ? `${fileOptions.name}.${fileType}`
    : `file_${formattedDate}.${fileType}`;
  const newFile = new File([fileResponse.data], fileName);
  return newFile;
};
