import {
  CreateExampleParams,
  DeleteExampleParams,
  GetExampleParams,
  GetExamplesParams,
} from "@floqastinc/transform-v3";
import { queryOptions } from "@tanstack/react-query";
import { queryKeys } from "./query-keys";
import v3, { ApiError } from "@/services/v3";
import { createQueryFunction } from "@/utils/query";
import { t } from "@/utils/i18n";

export const getExamplesQuery = ({ workflowId, taskId }: GetExamplesParams) => {
  return queryOptions({
    queryKey: queryKeys.examples.getExamples({ workflowId, taskId }),
    queryFn: getExamples,
  });
};

const getExamples = createQueryFunction(queryKeys.examples.getExamples, async (params) => {
  const result = await v3.examples.getExamples(params as GetExamplesParams);
  if (result.errors.length) {
    throw new ApiError(result.errors);
  }
  if (!result.data) {
    throw new Error(t("components.BuilderV3API.Errors.failedGetExample"));
  }
  return result.data;
});

const getExample = createQueryFunction(queryKeys.examples.getExample, async (params) => {
  const result = await v3.examples.getExample(params as GetExampleParams);
  if (result.errors.length) {
    throw new ApiError(result.errors);
  }
  if (!result.data) {
    throw new Error(t("components.BuilderV3API.Errors.failedGetExample"));
  }
  return result.data;
});

export const getExampleQuery = ({ workflowId, taskId, exampleSetId }: GetExampleParams) => {
  return queryOptions({
    queryKey: queryKeys.examples.getExample({
      workflowId,
      taskId,
      exampleSetId,
    }),
    queryFn: getExample,
  });
};

export const createExample = async (params: CreateExampleParams) => {
  // TODO: Add in previous exampleSetId for propagation
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const result = await v3.examples.createExample({
    ...params,
    example: {
      ...params.example,
      timezone,
    },
  });
  if (result.errors.length) {
    throw new ApiError(result.errors);
  }
  if (result.errors.length) {
    throw new ApiError(result.errors);
  }
  if (!result.data) {
    throw new Error(t("components.BuilderV3API.Errors.failedCreateExample"));
  }

  return result.data;
};

export const deleteExample = async (params: DeleteExampleParams) => {
  const result = await v3.examples.deleteExample(params);
  if (result.errors.length) {
    throw new ApiError(result.errors);
  }
  if (!result.data) {
    throw new Error(t("components.BuilderV3API.Errors.failedDeleteExample"));
  }
  return result.data;
};
