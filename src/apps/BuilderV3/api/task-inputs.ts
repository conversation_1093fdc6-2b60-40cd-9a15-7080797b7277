import { queryOptions } from "@tanstack/react-query";
import { queryKeys } from "./query-keys";
import type { QueryContext, TaskId, WorkflowId } from "./types";
import { t } from "@/utils/i18n";
import v3, { ApiError } from "@/services/v3";

const getTaskInputs = async ({
  queryKey: [
    {
      filter: { workflowId },
    },
    {
      filter: { taskId },
    },
  ],
}: QueryContext<(typeof queryKeys.taskInputs)["byTask"]>) => {
  const { data, errors } = await v3.taskInputs.getTaskInputs({
    workflowId,
    taskId,
  });
  if (errors.length) {
    throw new ApiError(errors);
  }
  if (!data) {
    throw new Error(t("components.BuilderV3API.Errors.failedWorkflowIn"));
  }
  return data;
};

export const getTaskInputsQuery = ({ workflowId, taskId }: WorkflowId & TaskId) => {
  return queryOptions({
    queryKey: queryKeys.taskInputs.byTask({ workflowId, taskId }),
    queryFn: getTaskInputs,
  });
};
