import type {
  DeleteMessageParams,
  ExampleLlmScriptStrategy,
  ExampleLlmThreadStrategy,
  ExampleScriptStrategy,
  ExampleStrategy,
  GetMessagesParams,
  TextContent,
} from "@floqastinc/transform-v3";
import { queryOptions } from "@tanstack/react-query";
import { queryKeys } from "./query-keys";
import v3, { ApiError } from "@/services/v3";
import { poll } from "@/utils/api";
import { createQueryFunction } from "@/utils/query";
import { t } from "@/utils/i18n";

type ConversationBasedStrategy =
  | ExampleLlmThreadStrategy
  | ExampleLlmScriptStrategy
  | ExampleScriptStrategy;
export const isConversationBasedStrategy = (
  strategy: ExampleStrategy,
): strategy is ConversationBasedStrategy => {
  const kinds = ["LLM_THREAD", "LLM_SCRIPT", "SCRIPT"] as const;
  return kinds.includes(strategy?.kind);
};

type SendMessageArgs = {
  workflowId: string;
  taskId: string;
  message: string;
  exampleSetId: string;
};

type EditMessageArgs = {
  workflowId: string;
  taskId: string;
  message: { content: string; id: string };
  exampleSetId: string;
};

type RegenerateMessageArgs = {
  workflowId: string;
  taskId: string;
  messageId: string;
  exampleSetId: string;
};

export const createMessage = async ({
  workflowId,
  taskId,
  message,
  exampleSetId,
}: SendMessageArgs) => {
  const result = await v3.messages.createMessage({
    workflowId,
    taskId,
    message: {
      content: message,
    },
    exampleSetId,
  });

  if (result.errors.length) {
    throw new ApiError(result.errors);
  }
  const pollResult = await poll(
    () =>
      // TODO: likely need to check status on message/example set
      v3.examples.getExample({
        workflowId,
        taskId,
        exampleSetId,
      }),
    {
      succeed: (result) => {
        const strategy = result.data?.strategy;
        if (!strategy) return false;

        if (isConversationBasedStrategy(strategy)) {
          return strategy?.conversationStatus === "READY";
        }

        return false;
      },
      fail: (result) => {
        const strategy = result.data?.strategy;

        if (!strategy) return true;

        if (isConversationBasedStrategy(strategy)) {
          return strategy?.conversationStatus === "FAILED";
        }

        return false;
      },
    },
  );

  // TODO: propagation needed?
  const exampleSet = await v3.examples.getExample({
    workflowId,
    taskId,
    exampleSetId,
  });
  if (exampleSet.errors.length > 0) {
    throw new ApiError(exampleSet.errors);
  }
  if (!exampleSet.data) {
    throw new Error(t("components.BuilderV3API.Errors.noDataExample"));
  }

  return pollResult;
};

export const editMessage = async ({
  workflowId,
  taskId,
  exampleSetId,
  message,
}: EditMessageArgs) => {
  const { content, id } = message;
  try {
    const result = await v3.messages.editMessage({
      workflowId,
      taskId,
      exampleSetId,
      messageId: id,
      message: {
        content,
      },
    });
    if (result.errors.length) {
      throw new ApiError(result.errors);
    }
  } catch (e: any) {
    throw new ApiError([e]);
  }

  return await poll(
    () =>
      // TODO: likely need to check status on message/example set
      v3.examples.getExample({
        workflowId,
        taskId,
        exampleSetId,
      }),
    {
      succeed: (result) => {
        const strategy = result.data?.strategy;
        if (!strategy) return false;

        if (isConversationBasedStrategy(strategy)) {
          return strategy?.conversationStatus === "READY";
        }

        return false;
      },
      fail: (result) => {
        const strategy = result.data?.strategy;

        if (!strategy) return true;

        if (isConversationBasedStrategy(strategy)) {
          return strategy?.conversationStatus === "FAILED";
        }

        return false;
      },
    },
  );
};

export const regenerateMessage = async ({
  workflowId,
  taskId,
  exampleSetId,
  messageId,
}: RegenerateMessageArgs) => {
  try {
    const result = await v3.messages.regenerateMessage({
      workflowId,
      taskId,
      exampleSetId,
      messageId,
    });
    if (result.errors.length) {
      throw new ApiError(result.errors);
    }
  } catch (e: any) {
    throw new ApiError([e]);
  }

  return await poll(
    () =>
      // TODO: likely need to check status on message/example set
      v3.examples.getExample({
        workflowId,
        taskId,
        exampleSetId,
      }),
    {
      succeed: (result) => {
        const strategy = result.data?.strategy;
        if (!strategy) return false;

        if (isConversationBasedStrategy(strategy)) {
          return strategy?.conversationStatus === "READY";
        }

        return false;
      },
      fail: (result) => {
        const strategy = result.data?.strategy;

        if (!strategy) return true;

        if (isConversationBasedStrategy(strategy)) {
          return strategy?.conversationStatus === "FAILED";
        }

        return false;
      },
    },
  );
};

// User messages will be displayed as:
/*
**INPUTS**:
{Inputs}

**MESSAGE**:
${Message}
*/
// We just want to display the message to the user
export const cleanMessage = (message: string) => {
  return message.replace(/\*\*INPUTS\*\*:[\s\S]*?\*\*MESSAGE\*\*:/, "").trim();
};

export const isTextMessage = (message: any): message is TextContent =>
  message.content?.[0].type === "text";

export const getMessages = createQueryFunction(
  queryKeys.messages.getMessages,
  async ({ workflowId, taskId, exampleSetId }) => {
    const { data, errors } = await v3.messages.getMessages({
      workflowId: workflowId || "",
      taskId: taskId || "",
      exampleSetId: exampleSetId || "",
    });

    if (errors.length) {
      throw new ApiError(errors);
    }

    if (!data) {
      throw new Error(t("components.BuilderV3API.Errors.noDataMessage"));
    }

    return data.filter(isTextMessage).map((message) => {
      return {
        id: message.id,
        // @ts-expect-ignore TypeScript can't narrow this type for some reason
        content: message.content?.[0].text.value,
        role: message.role,
        metadata: message.metadata,
      };
    });
  },
);

export const getMessagesQuery = (params: GetMessagesParams) => {
  return queryOptions({
    queryKey: queryKeys.messages.getMessages(params),
    queryFn: getMessages,
  });
};

export const deleteMessage = async ({
  workflowId,
  taskId,
  exampleSetId,
  messageId,
}: DeleteMessageParams) => {
  const result = await v3.messages.deleteMessage({
    workflowId,
    taskId,
    exampleSetId,
    messageId,
  });

  if (result.errors.length) {
    throw new ApiError(result.errors);
  }
  if (!result.data) {
    throw new Error(t("components.BuilderV3API.Errors.noDataMessage"));
  }

  return result;
};
