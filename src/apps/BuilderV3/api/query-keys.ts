import v3 from "@/services/v3";
import { createQueryKeys } from "@/utils/query";
import { GetAllWorkflowRunsParams, GetWorkflowsParams } from "@floqastinc/transform-v3";
import { FileOptions, RunId, TaskId, TaskInputId, WorkflowId, WorkflowInputId } from "./types";

const workflowQueryKeys = {
  all: (page: GetWorkflowsParams = {}) => [{ scope: "workflows", page }] as const,
  byId: (workflowId: string) => [{ scope: "workflows", filter: { workflowId } }] as const,
};

const workflowRunQueryKeys = {
  all: (page: GetAllWorkflowRunsParams = {}) => [{ scope: "workflowRuns", page }] as const,
};

const workflowInputQueryKeys = {
  byWorkflow: (workflowId: string) =>
    [...workflowQueryKeys.byId(workflowId), { scope: "inputs" }] as const,
  byId: ({ workflowId, workflowInputId }: WorkflowId & WorkflowInputId) =>
    [
      ...workflowQueryKeys.byId(workflowId),
      { scope: "inputs", filter: { workflowInputId } },
    ] as const,
  uriById: ({
    workflowId,
    workflowInputId,
    fileOptions,
  }: WorkflowId & WorkflowInputId & { fileOptions: Partial<File> }) =>
    [
      ...workflowQueryKeys.byId(workflowId),
      { scope: "inputs", filter: { workflowInputId } },
      { scope: "uri", options: { fileOptions } },
    ] as const,
};

const taskInputQueryKeys = {
  byTask: ({ workflowId, taskId }: WorkflowId & TaskId) =>
    [...taskQueryKeys.byId({ workflowId, taskId }), { scope: "inputs" }] as const,
  byId: ({ workflowId, taskId, taskInputId }: WorkflowId & TaskId & TaskInputId) =>
    [
      ...taskQueryKeys.byId({ workflowId, taskId }),
      { scope: "inputs", filter: { taskInputId } },
    ] as const,
};

const taskOutputQueryKeys = {
  byTask: ({
    workflowId,
    taskId,
    options,
  }: WorkflowId & TaskId & { options?: Partial<FileOptions> }) =>
    [...taskQueryKeys.byId({ workflowId, taskId }), { scope: "outputs", options }] as const,
};

const taskQueryKeys = {
  byWorkflow: (workflowId: string) =>
    [...workflowQueryKeys.byId(workflowId), { scope: "tasks" }] as const,
  byId: ({ workflowId, taskId }: WorkflowId & TaskId) =>
    [...workflowQueryKeys.byId(workflowId), { scope: "tasks", filter: { taskId } }] as const,
  currentOutputFile: ({
    workflowId,
    taskId,
    fileOptions,
  }: WorkflowId & TaskId & { fileOptions?: Partial<File> }) => [
    ...workflowQueryKeys.byId(workflowId),
    { scope: "tasks", filter: { taskId } },
    { scope: "currentOutputFile", options: { fileOptions } },
  ],
  pollStatus: ({ workflowId, taskId, runId }: WorkflowId & TaskId & RunId) =>
    [
      ...workflowQueryKeys.byId(workflowId),
      { scope: "tasks", filter: { taskId } },
      { scope: "run", filter: { runId } },
    ] as const,
};

const workflowOutputQueryKeys = createQueryKeys("workflowOutputs", v3.workflowOutputs);

/**
 * Best practice is to rely on the api to generate query keys provided by the api (e.g. createQueryKeys('messages', v3.messages)),
 * sometimes legacy query keys need to be maintained for backwards compatibility
 * (e.g. {...createQueryKeys('workflowRuns', v3.runs), ...workflowRunQueryKeys})
 */
export const queryKeys = {
  workflows: workflowQueryKeys,
  workflowInputs: workflowInputQueryKeys,
  workflowOutputs: workflowOutputQueryKeys,
  taskInputs: taskInputQueryKeys,
  taskOutputs: taskOutputQueryKeys,
  tasks: taskQueryKeys,
  examples: createQueryKeys("examples", v3.examples),
  exampleInputs: createQueryKeys("exampleInputs", v3.exampleInputs),
  exampleOutputs: createQueryKeys("exampleOutputs", v3.exampleOutputs),
  messages: createQueryKeys("messages", v3.messages),
  workflowRuns: { ...createQueryKeys("workflowRuns", v3.runs), ...workflowRunQueryKeys },
  experiments: createQueryKeys("experiments", v3.experiments),
  experimentAsignments: createQueryKeys("experimentAsignments", v3.experimentAssignments),
};
