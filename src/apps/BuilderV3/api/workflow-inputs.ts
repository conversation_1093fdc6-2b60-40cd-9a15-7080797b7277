import { queryOptions } from "@tanstack/react-query";
import { queryKeys } from "./query-keys";
import type { QueryContext } from "./types";
import { t } from "@/utils/i18n";
import v3, { ApiError } from "@/services/v3";

const getWorkflowInputs = async ({
  queryKey: [
    {
      filter: { workflowId },
    },
  ],
}: QueryContext<(typeof queryKeys.workflowInputs)["byWorkflow"]>) => {
  const { data, errors } = await v3.workflowInputs.getWorkflowInputs({
    workflowId,
  });
  if (errors.length) {
    throw new ApiError(errors);
  }
  if (!data) {
    throw new Error(t("components.BuilderV3API.Errors.failedWorkflowIn"));
  }
  return data;
};

export const getWorkflowInputsQuery = (workflowId: string) => {
  return queryOptions({
    queryKey: queryKeys.workflowInputs.byWorkflow(workflowId),
    queryFn: getWorkflowInputs,
  });
};
