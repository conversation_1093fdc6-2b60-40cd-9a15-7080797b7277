import { Suspense } from "react";
import "react-loading-skeleton/dist/skeleton.css";
import { ScopeProvider } from "jotai-scope";
import { RouteObject } from "react-router-dom";
import { isFileLoadingAtom, fileAtom as newFile<PERSON>tom } from "../Transform/pages/builder/store";
import { BuilderPage } from "./routes/workflows/BuilderPage";
import { BuilderLoadingPage } from "./routes/workflows/BuilderLoadingPage";
import {
  activeSheetAtom,
  collapsedPanelAtom,
  fileAtom,
  selectedFileAtom,
  selectedRangeAtom,
} from "./routes/workflows/BuilderPage.store";
import { Root } from "./Root";
import { BuilderPage as NewBuilderPage } from "@Transform/pages/builder";
import { ModalProvider as BuilderModalProvider } from "@/components/ModalContext";
import { Loading } from "@/components/Loading";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { AGENTS, BUILDER, EXAMPLES, INPUTS_PATH, STEPS, V3 } from "@/constants";
import { AgentListPage } from "@Transform/pages/agent-list/AgentListPage";
import { featureFlags } from "@/components/FeatureFlag";

/** @type {import('react-router-dom').RouteObject[]} */
export const routes: RouteObject[] = [
  {
    path: `/${V3}`,
    element: (
      <BuilderModalProvider>
        <Root />
      </BuilderModalProvider>
    ),
    errorElement: <ErrorBoundary />,
    children: [
      {
        index: true,
        element: (
          <Suspense fallback={<Loading />}>
            <AgentListPage />
          </Suspense>
        ),
        errorElement: <ErrorBoundary />,
      },
    ],
  },
  {
    path: `/${BUILDER}/${V3}`,
    element: (
      <BuilderModalProvider>
        <Root />
      </BuilderModalProvider>
    ),
    errorElement: <ErrorBoundary />,
    children: [
      {
        path: `${AGENTS}/:workflowId`,
        element: featureFlags.get("legacy-builder") ? (
          <Suspense fallback={<Loading />}>
            <BuilderLoadingPage />
          </Suspense>
        ) : (
          <ScopeProvider atoms={[newFileAtom, isFileLoadingAtom]}>
            <NewBuilderPage />
          </ScopeProvider>
        ),
        errorElement: <ErrorBoundary />,
      },
      {
        path: INPUTS_PATH,
        element: featureFlags.get("legacy-builder") ? (
          <Suspense fallback={<Loading />}>
            <BuilderLoadingPage />
          </Suspense>
        ) : (
          <ScopeProvider atoms={[newFileAtom, isFileLoadingAtom]}>
            <NewBuilderPage />
          </ScopeProvider>
        ),
        errorElement: <ErrorBoundary />,
      },
      {
        path: `${AGENTS}/:workflowId/${STEPS}/:taskId`,
        element: featureFlags.get("legacy-builder") ? (
          <Suspense fallback={<Loading />}>
            <BuilderLoadingPage />
          </Suspense>
        ) : (
          <ScopeProvider atoms={[newFileAtom, isFileLoadingAtom]}>
            <NewBuilderPage />
          </ScopeProvider>
        ),
        errorElement: <ErrorBoundary />,
      },
      {
        path: `${AGENTS}/:workflowId/${STEPS}/:taskId/${EXAMPLES}/:exampleSetId`,
        element: featureFlags.get("legacy-builder") ? (
          <Suspense fallback={<Loading />}>
            <ScopeProvider
              atoms={[
                selectedRangeAtom,
                activeSheetAtom,
                collapsedPanelAtom,
                fileAtom,
                selectedFileAtom,
              ]}
            >
              <BuilderPage />
            </ScopeProvider>
          </Suspense>
        ) : (
          <ScopeProvider atoms={[newFileAtom, isFileLoadingAtom]}>
            <NewBuilderPage />
          </ScopeProvider>
        ),
        errorElement: <ErrorBoundary />,
      },
    ],
  },
];

export default routes;
