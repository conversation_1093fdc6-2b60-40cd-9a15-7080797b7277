import { useToast } from "@floqastinc/flow-ui_core";
import { SpinnerButton } from "@/components/SpinnerButton";
import { usePublishWorkflowRunValues } from "@/hooks/usePublishWorkflowRunValues";
import { t } from "@/utils/i18n";

interface PublishValuesButtonProps {
  runId: string;
  agentId: string;
}

const TOAST_DURATION = 5000;
const TOAST_POSITION = "bottom-right";

export const PublishValuesButton = ({ runId, agentId }: PublishValuesButtonProps) => {
  const { showToast, Toast } = useToast();
  const publishWorkflowRunValues = usePublishWorkflowRunValues();

  const handlePublishValues = async () => {
    try {
      await publishWorkflowRunValues.mutateAsync({
        workflowRunId: runId,
        workflowId: agentId,
      });

      showToast(
        <Toast type="success">
          <Toast.Title>
            {t("components.PublishValuesButton.publishValuesSuccessHeader")}
          </Toast.Title>
          <Toast.Message>
            {t("components.PublishValuesButton.publishValuesSuccessMessage")}
          </Toast.Message>
        </Toast>,
        {
          duration: TOAST_DURATION,
          position: TOAST_POSITION,
        },
      );
    } catch (error) {
      console.error("Error publishing workflow run values:", error);

      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.PublishValuesButton.publishValuesErrorHeader")}</Toast.Title>
          <Toast.Message>
            {error instanceof Error
              ? error.message
              : t("components.PublishValuesButton.publishValuesErrorMessage")}
          </Toast.Message>
        </Toast>,
        {
          duration: TOAST_DURATION,
          position: TOAST_POSITION,
        },
      );
    }
  };

  return (
    <SpinnerButton
      color="dark"
      variant="outlined"
      isPending={publishWorkflowRunValues.isPending}
      disabled={false}
      onClick={handlePublishValues}
    >
      {t("components.PublishValuesButton.publishValuesButtonText")}
    </SpinnerButton>
  );
};
