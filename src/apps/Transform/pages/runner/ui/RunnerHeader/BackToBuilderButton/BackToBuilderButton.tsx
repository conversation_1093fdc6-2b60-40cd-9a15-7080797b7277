import { Button } from "@floqastinc/flow-ui_core";
import { useNavigate } from "react-router-dom";
import { TaskRun, WorkflowRun } from "@floqastinc/transform-v3";
import { getBuilderUrlForWorkflow, getBuilderUrlForWorkflowTask } from "@/utils/urls";
import { t } from "@/utils/i18n";
import { useTaskRuns, useWorkflowRun } from "@v3/runs";

const getExampleSetToOpen = (run?: WorkflowRun, tasks?: TaskRun[]) => {
  if (run?.status === "FAILED") {
    const taskRun = tasks?.find((taskRun) => taskRun.status === "FAILED");

    if (taskRun) {
      return taskRun.taskId;
    }
  }

  return tasks?.length ? tasks[0]?.taskId : undefined;
};

const BackToBuilderButton = ({ agentId, runId }: { agentId: string; runId: string }) => {
  const navigate = useNavigate();
  const { data: run } = useWorkflowRun({ workflowRunId: runId });
  const { data: tasks } = useTaskRuns({ workflowRunId: runId });

  const exampleSetToOpen = getExampleSetToOpen(run, tasks);

  const url = exampleSetToOpen
    ? getBuilderUrlForWorkflowTask(agentId, exampleSetToOpen)
    : getBuilderUrlForWorkflow(agentId);

  return (
    <Button color="dark" variant="outlined" onClick={() => navigate(url)}>
      {t("components.BackToBuilderButton.backToBuilder")}
    </Button>
  );
};

export default BackToBuilderButton;
