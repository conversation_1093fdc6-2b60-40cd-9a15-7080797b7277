import { Heading, TableStatusBadge } from "@floqastinc/flow-ui_core";
import { Link as ReactRouterLink } from "react-router-dom";
import { styled } from "styled-components";

export const HeaderWrapper = styled.div`
  background-color: var(--flo-base-color-neutral-0);
  border-bottom: 1px solid #e3e1de;
  padding: 10px 20px 20px 20px;
`;

export const BackLink = styled(ReactRouterLink)`
  text-decoration: none;
  color: inherit;
  display: inline-flex;
  align-items: center;
  margin-bottom: 10px;

  svg {
    margin-right: 5px;
  }

  span {
    font-size: 0.75rem;
    padding-top: 2px;
  }
`;

export const HeaderContent = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const RunInfo = styled.div`
  display: inherit;
  align-items: inherit;
`;

export const RunTitle = styled(Heading)`
  font-size: 1.5rem;
  font-weight: 600;
  margin-right: 10px;
`;

export const RunOptions = styled.div`
  display: inherit;
  align-items: inherit;
  gap: 12px;
`;

export const RunStatusBadge = styled(TableStatusBadge)`
  height: 1rem;
`;
