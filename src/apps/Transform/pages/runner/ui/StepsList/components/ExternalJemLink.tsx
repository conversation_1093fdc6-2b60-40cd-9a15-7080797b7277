import React from "react";
import { Button, Text } from "@floqastinc/flow-ui_core";
import OpenInNew from "@floqastinc/flow-ui_icons/material/OpenInNew";
import * as Styled from "./FileDownload.styled";
import { t } from "@/utils/i18n";

type ExternalJemLinkProps = {
  outputValue: {
    value: string;
    name: string;
    stepNumber: number;
    kind:
      | "JEM_EXPORT"
      | "LLM_PROMPT"
      | "LLM_THREAD"
      | "PDF_TO_XLSX"
      | "SCRIPT"
      | "LLM_SCRIPT"
      | "REVIEW"
      | "JEM_TEMPLATE_FETCH"
      | "NO_OP"
      | "FLOLAKE";
  };
};

export const ExternalJemLink: React.FC<ExternalJemLinkProps> = ({ outputValue }) => {
  const baseUrl = process.env.REACT_APP_CLOSE_API_HOSTNAME;
  const JEM_URL = `${baseUrl}/journal-entry-management/search/je/${outputValue.value}`;
  return (
    <Styled.Container>
      <Styled.TextContainer>
        <Text title={t("components.ExternalJemLink.journalEntries")}>
          {t("components.ExternalJemLink.journalEntries")}
        </Text>
      </Styled.TextContainer>
      <Button
        onClick={() => {
          window.open(JEM_URL, "_blank");
        }}
        size="md"
        variant="ghost"
        color="dark"
        padding={false}
      >
        <OpenInNew color="currentColor" />
      </Button>
    </Styled.Container>
  );
};
