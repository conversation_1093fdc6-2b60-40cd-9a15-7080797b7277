import { describe, test, expect, beforeEach, vi } from "vitest";
import { userEvent } from "@vitest/browser/context";
import { RunStatus } from "@floqastinc/transform-v3";
import { ReviewStep } from "./ReviewStep";
import { customRender } from "@/utils/testing";

describe(`ReviewStep`, () => {
  const defaultProps = {
    isFinalStep: false,
    isSelected: false,
    name: "Test Step",
    status: "PENDING" as RunStatus,
    onClick: vi.fn(),
    onRejectClick: vi.fn(),
    onApproveClick: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test(`GIVEN a review step component renders,
       THEN the step name is displayed correctly`, async () => {
    const screen = customRender(<ReviewStep {...defaultProps} />);

    await expect.element(screen.getByText("Test Step")).toBeVisible();
  });

  test(`GIVEN a review step component,
       WHEN the card is clicked,
       THEN the card's click function is called`, async () => {
    const screen = customRender(<ReviewStep {...defaultProps} />);

    await userEvent.click(screen.getByText("Test Step"));

    expect(defaultProps.onClick).toHaveBeenCalledTimes(1);
  });

  test(`GIVEN a review step with REVIEW status,
       WHEN the reject button is clicked,
       THEN the button's click function is called and the card's click function is not called`, async () => {
    const screen = customRender(<ReviewStep {...defaultProps} status="REVIEW" />);

    await userEvent.click(screen.getByTestId("reject-run-button"));

    expect(defaultProps.onRejectClick).toHaveBeenCalledTimes(1);
    expect(defaultProps.onClick).not.toHaveBeenCalled();
  });

  test(`GIVEN a review step with REVIEW status,
       WHEN the resume button is clicked,
       THEN the button's click function is called and the card's click function is not called`, async () => {
    const screen = customRender(<ReviewStep {...defaultProps} status="REVIEW" />);
    await userEvent.click(screen.getByTestId("resume-run-button"));

    expect(defaultProps.onApproveClick).toHaveBeenCalledTimes(1);
    expect(defaultProps.onClick).not.toHaveBeenCalled();
  });

  test(`GIVEN a review step with REVIEW status,
       THEN a pause icon is rendered`, async () => {
    const screen = customRender(<ReviewStep {...defaultProps} status="REVIEW" />);

    await expect.element(screen.getByLabelText("Pause Icon")).toBeVisible();
  });

  test(`GIVEN a review step with COMPLETED status,
       THEN a check circle icon is rendered`, async () => {
    const screen = customRender(<ReviewStep {...defaultProps} status="COMPLETED" />);

    await expect.element(screen.getByLabelText("Green Check Icon")).toBeVisible();
  });

  test(`GIVEN a review step with REJECTED status,
       THEN a close icon is rendered`, async () => {
    const screen = customRender(<ReviewStep {...defaultProps} status="REJECTED" />);

    await expect.element(screen.getByLabelText("Red X Icon")).toBeVisible();
  });

  test(`GIVEN a review step with REVIEW status,
       THEN both reject and resume buttons are visible`, async () => {
    const screen = customRender(<ReviewStep {...defaultProps} status="REVIEW" />);

    await expect.element(screen.getByTestId("reject-run-button")).toBeVisible();
    await expect.element(screen.getByTestId("resume-run-button")).toBeVisible();
  });

  test(`GIVEN a review step with COMPLETED status,
       THEN no action buttons are visible`, async () => {
    const screen = customRender(<ReviewStep {...defaultProps} status="COMPLETED" />);

    try {
      screen.getByTestId("reject-run-button");
      // If we get here, the element exists when it shouldn't
      throw new Error("Expected reject button to not be in the document");
    } catch (e) {}

    try {
      screen.getByTestId("resume-run-button");
      throw new Error("Expected resume button to not be in the document");
    } catch (e) {}

    try {
      screen.getByTestId("approve-run-button");
      throw new Error("Expected approve button to not be in the document");
    } catch (e) {}
  });

  test(`GIVEN a review step with REJECTED status,
       THEN no action buttons are visible`, async () => {
    const screen = customRender(<ReviewStep {...defaultProps} status="REJECTED" />);

    try {
      screen.getByTestId("reject-run-button");
      throw new Error("Expected reject button to not be in the document");
    } catch (e) {}

    try {
      screen.getByTestId("resume-run-button");
      throw new Error("Expected resume button to not be in the document");
    } catch (e) {}

    try {
      screen.getByTestId("approve-run-button");
      throw new Error("Expected approve button to not be in the document");
    } catch (e) {}
  });

  test(`GIVEN the step is the final step in the agent run with REVIEW status,
       THEN the approve button appears instead of the resume button`, async () => {
    const screen = customRender(
      <ReviewStep {...defaultProps} isFinalStep={true} status="REVIEW" />,
    );

    await expect.element(screen.getByTestId("approve-run-button")).toBeVisible();

    try {
      screen.getByTestId("resume-run-button");
      throw new Error("Expected resume button to not be in the document");
    } catch (e) {}
  });

  test(`GIVEN the step is not the final step in the agent run with REVIEW status,
       THEN the resume button appears instead of the approve button`, async () => {
    const screen = customRender(
      <ReviewStep {...defaultProps} isFinalStep={false} status="REVIEW" />,
    );

    await expect.element(screen.getByTestId("resume-run-button")).toBeVisible();

    try {
      screen.getByTestId("approve-run-button");
      throw new Error("Expected approve button to not be in the document");
    } catch (e) {}
  });

  test(`GIVEN a review step with REVIEW status,
       THEN it uses ReviewStepCard styling`, async () => {
    const screen = customRender(<ReviewStep {...defaultProps} />);

    await expect
      .element(screen.getByTestId(`${defaultProps.name}-review-step-card`))
      .toBeInTheDocument();
  });

  test(`GIVEN a review step with COMPLETED status,
       THEN it uses StepCard styling`, async () => {
    const screen = customRender(<ReviewStep {...defaultProps} status="COMPLETED" />);

    await expect.element(screen.getByTestId(`${defaultProps.name}-step-card`)).toBeInTheDocument();
  });

  test(`GIVEN a review step with REJECTED status,
       THEN it uses StepCard styling`, async () => {
    const screen = customRender(<ReviewStep {...defaultProps} status="REJECTED" />);

    await expect.element(screen.getByTestId(`${defaultProps.name}-step-card`)).toBeInTheDocument();
  });

  test(`GIVEN a review step is selected,
       THEN it has a thick border and correct aria attributes`, async () => {
    const screen = customRender(<ReviewStep {...defaultProps} isSelected={true} />);

    const card = screen.getByRole("tab");

    await expect(card).toMatchSnapshot("selected-review-step.png");
    await expect.element(card).toHaveAttribute("aria-selected", "true");
  });

  test(`GIVEN a review step is not selected,
       THEN it has thin border and correct aria attributes`, async () => {
    const screen = customRender(<ReviewStep {...defaultProps} isSelected={false} />);

    const card = screen.getByRole("tab");

    await expect(card).toMatchSnapshot("unselected-review-step.png");
    await expect.element(card).toHaveAttribute("aria-selected", "false");
  });
});
