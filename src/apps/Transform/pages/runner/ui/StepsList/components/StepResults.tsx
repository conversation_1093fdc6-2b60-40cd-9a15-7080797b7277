import { Text, TableStatusBadge } from "@floqastinc/flow-ui_core";
import DateRange from "@floqastinc/flow-ui_icons/material/DateRange";
import { RunStatus } from "@floqastinc/transform-v3";
import * as Styled from "./StepResults.styled";
import { StepResultFile } from "./StepResultFile";
import { StepResultJemLink } from "./StepResultJEMLink";
import { t } from "@/utils/i18n";
import { getRunStatusDisplayProperties } from "@Transform/shared/lib/workflow-runs";

type StepResultProps = {
  file: File | null;
  workflowstatus: RunStatus;
  runBy: string | undefined;
  createdAt: Date | undefined;
  outputValues: {
    value: string;
    name: string;
    stepNumber: number;
    kind:
      | "JEM_EXPORT"
      | "LLM_PROMPT"
      | "LLM_THREAD"
      | "PDF_TO_XLSX"
      | "SCRIPT"
      | "LLM_SCRIPT"
      | "REVIEW"
      | "JEM_TEMPLATE_FETCH"
      | "NO_OP"
      | "FLOLAKE";
  }[];
};

export const StepResult: React.FC<StepResultProps> = ({
  file,
  workflowstatus,
  runBy,
  createdAt,
  outputValues,
}: StepResultProps) => {
  const { badgeColor, statusText } = workflowstatus
    ? getRunStatusDisplayProperties(workflowstatus)
    : {};
  return (
    <Styled.StepResultsCard>
      <Styled.NameContainer>
        <Styled.Name data-testid="results">{t("components.StepResults.results")}</Styled.Name>
      </Styled.NameContainer>

      {outputValues.length > 0 &&
        outputValues.map((outputValue, i) => {
          if (i === outputValues.length - 1 && outputValue.kind === "JEM_EXPORT" && file) {
            // Last output should always have the file
            return (
              <div key={`${outputValue.stepNumber}-${outputValue.kind}`}>
                <Text
                  style={{ paddingTop: "4px", paddingBottom: "4px" }}
                  color={"var(--flo-sem-color-text-body-secondary)"}
                >{`Step ${outputValue.stepNumber}: ${outputValue.name}`}</Text>
                <StepResultFile file={file} data-testid="runner-stepList-result-file" />
                <StepResultJemLink
                  outputValue={outputValue}
                  data-testid={`runner-stepList-result-jemLink-${outputValue.value}`}
                />
              </div>
            );
          } else if (outputValue.kind === "JEM_EXPORT") {
            return (
              <div key={`${outputValue.stepNumber}-${outputValue.kind}`}>
                <Text
                  style={{ paddingTop: "4px", paddingBottom: "4px" }}
                  color={"var(--flo-sem-color-text-body-secondary)"}
                >{`Step ${outputValue.stepNumber}: ${outputValue.name}`}</Text>
                <StepResultJemLink
                  outputValue={outputValue}
                  data-testid={`runner-stepList-result-jemLink-${outputValue.value}`}
                />
              </div>
            );
          } else {
            return (
              <div key={`${outputValue.stepNumber}-${outputValue.kind}`}>
                <Text
                  style={{ paddingTop: "4px", paddingBottom: "4px" }}
                  color={"var(--flo-sem-color-text-body-secondary)"}
                >{`Step ${outputValue.stepNumber}: ${outputValue.name}`}</Text>
                <StepResultFile file={file} data-testid="runner-stepList-result-file" />
              </div>
            );
          }
        })}
      <Styled.RunByContainer>
        <Text
          truncate={false}
          style={{ whiteSpace: "nowrap", minWidth: "max-content" }}
          color={"var(--flo-sem-color-text-body-secondary)"}
          data-testid="runBy"
        >
          {t("components.StepResults.runBy")}
        </Text>
        <Text weight={6} style={{ whiteSpace: "nowrap", minWidth: "max-content" }}>
          {runBy}
        </Text>
        <TableStatusBadge color={badgeColor} hasIcon={false} style={{ marginLeft: "2px" }}>
          {statusText}
        </TableStatusBadge>
      </Styled.RunByContainer>
      <Styled.RunDateContainer>
        <DateRange color="var(--flo-sem-color-icon-primary)" style={{ opacity: 0.8 }} size={20} />
        <Text
          truncate={false}
          weight={5}
          style={{
            whiteSpace: "nowrap",
            minWidth: "max-content",
            color: "var(--flo-base-color-neutral-500)",
          }}
        >
          {createdAt?.toLocaleDateString("en-US", {
            month: "short", // "Jan"
            day: "numeric", // "8"
            year: "numeric", // "2025"
          })}
        </Text>
        <Text
          weight={5}
          truncate={false}
          style={{
            whiteSpace: "nowrap",
            minWidth: "max-content",
            padding: "0px 4px 0px 4px",
            color: "var(--flo-base-color-neutral-300)",
          }}
        >
          {"|"}
        </Text>
        <Text
          truncate={false}
          weight={5}
          style={{
            whiteSpace: "nowrap",
            minWidth: "max-content",
            color: "var(--flo-base-color-neutral-500)",
          }}
        >
          {createdAt?.toLocaleTimeString("en-US", {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          })}
        </Text>
      </Styled.RunDateContainer>
    </Styled.StepResultsCard>
  );
};
