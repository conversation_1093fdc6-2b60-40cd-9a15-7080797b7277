import { ReactNode, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { sortBy } from "lodash";
import { match } from "ts-pattern";
import { useParams } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Too<PERSON><PERSON> } from "@floqastinc/flow-ui_core";
import { ProtectedComponent } from "@floqastinc/auth-module-client";
import Coupa from "@floqastinc/flow-ui_icons/fq/Coupa";
import Netsuite from "@floqastinc/flow-ui_icons/fq/Netsuite";
import Intacct from "@floqastinc/flow-ui_icons/fq/Intacct";
import Workday from "@floqastinc/flow-ui_icons/fq/Workday";
import CheckCircle from "@floqastinc/flow-ui_icons/material/CheckCircle";
import TableOutlined from "@floqastinc/flow-ui_icons/material/TableOutlined";
import EditOutlined from "@floqastinc/flow-ui_icons/material/EditOutlined";
import Error from "@floqastinc/flow-ui_icons/material/Error";
import InfoOutlined from "@floqastinc/flow-ui_icons/material/InfoOutlined";
import { RunStatus as TaskRunStatus } from "@floqastinc/transform-v3";
import { StrategyKind } from "../types";
import * as Styled from "./Step.styled";
import { t } from "@/utils/i18n";
import { USER_ACTION_KEYS } from "@/authorization";
import { v3, ApiError } from "@/services/v3";
import { ErrorDialogBox } from "@Transform/components/ErrorDialog/ErrorDialogBox";
import { parseTaskRunError, formatErrorForDisplay } from "@/utils/error-parser";

type StepStatus = "completed" | "error" | "pending" | "running";

const mapTaskStatusToStepStatus = (currentTaskRunStatus: TaskRunStatus): StepStatus => {
  return match(currentTaskRunStatus)
    .with("RUNNING", () => "running" as StepStatus)
    .with("COMPLETED", () => "completed" as StepStatus)
    .with("FAILED", () => "error" as StepStatus)
    .otherwise(() => "pending" as StepStatus);
};

interface StepProps {
  isPreview: boolean;
  integrations: string[];
  isSelected: boolean;
  status?: TaskRunStatus;
  name: string;
  stepRunId?: string;
  strategy: StrategyKind;
  onClick: () => void;
  onEditClick: () => void;
  onInfoClick: () => void;
}

const integrationIcons: Record<string, ReactNode> = {
  COUPA: <Coupa size={20} />,
  EXCEL: <TableOutlined size={20} />,
  NETSUITE: <Netsuite size={20} />,
  INTACCT: <Intacct size={20} />,
  WORKDAY: <Workday size={20} />,
};

const showIcon = (status: StepStatus) => {
  let icon = null;
  if (status === "running") {
    icon = <Spinner color="grey" size={20} />;
  } else if (status === "completed") {
    icon = <CheckCircle color="var(--flo-sem-color-success)" size={24} />;
  } else if (status === "error") {
    icon = <Error color="var(--flo-sem-color-warning)" size={24} />;
  }
  return icon ? <Styled.IconContainer>{icon}</Styled.IconContainer> : null;
};

export const Step = (props: StepProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const {
    isPreview,
    integrations,
    isSelected,
    name,
    stepRunId,
    status,
    strategy,
    onClick,
    onEditClick,
    onInfoClick,
  } = props;

  const stepStatus = mapTaskStatusToStepStatus(status as TaskRunStatus);
  const error = stepStatus === "error";
  const icon = showIcon(stepStatus);
  const showIntegrations = !isHovered && integrations?.length > 0 && isPreview;

  const { runId = "" } = useParams();
  const taskRunLogsQuery = useQuery({
    queryKey: [`taskRunLogs-${stepRunId}`, stepRunId],
    queryFn: async () => {
      // NOTE: This currently assumes that tasks are ran sequentially and stop execution
      //  after the first encountered error.
      //  (i.e. as opposed to async/concurrently)
      // The rendering of the errors in the Well component assumes only the
      //  most recent error is the current error since retrying a step doesn't
      //  create a new run and can create multiple unrelated errors, one per run.
      if (!runId || !stepRunId) return undefined;
      const { data, errors } = await v3.runs.getTaskRunLog({
        workflowRunId: runId,
        taskRunId: stepRunId,
      });

      if (errors.length) {
        throw new ApiError(errors);
      }
      if (!data) {
        throw new Error(t("components.Step.Errors.noDataReturnedFromTaskRun"));
      }
      const sortedErrorLogs = sortBy(
        data.filter((log) => log.severity === "ERROR"),
        "timestamp",
      );

      return sortedErrorLogs;
    },
    // TODO: This works with a regular Task.id only because runId is tied to taskRunId
    enabled: !!runId && !!stepRunId && status === "FAILED",
  });

  const rawErrorMessage = taskRunLogsQuery.data?.[0]?.text;

  // Parse the error message to extract actionable information
  const parsedError = rawErrorMessage ? parseTaskRunError(rawErrorMessage, strategy) : null;
  const formattedError = parsedError ? formatErrorForDisplay(parsedError) : null;

  const errorMessage = formattedError?.message || rawErrorMessage || "No error message available";
  const errorDetails = {
    "Run ID": runId,
    ...(formattedError?.steps && { "Recommended Actions": formattedError.steps }),
    ...(parsedError?.technicalDetails && { "Technical Details": parsedError.technicalDetails }),
  };

  return (
    <Styled.StepCard
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      error={error}
      isSelected={isSelected}
    >
      <Styled.StepDetailsContainer>
        {icon}
        <Styled.NameContainer>
          <Styled.Name isSelected={isSelected} showButton={isHovered} status={status}>
            {name}
          </Styled.Name>
          {showIntegrations && (
            <Styled.IntegrationContainer>
              {integrations.map((integration) => integrationIcons[integration] || null)}
            </Styled.IntegrationContainer>
          )}
          <ProtectedComponent actionKey={USER_ACTION_KEYS.TRANSFORM_WORKFLOW_WRITE}>
            {isHovered && (
              <Tooltip>
                <Tooltip.Trigger>
                  <Styled.EditOrInfoButton
                    data-tracking-id="runner-edit-or-info-run-step-button"
                    onClick={isPreview ? onEditClick : onInfoClick}
                  >
                    {isPreview ? <EditOutlined /> : <InfoOutlined />}
                  </Styled.EditOrInfoButton>
                </Tooltip.Trigger>
                <Styled.TooltipContent side="top">
                  {isPreview
                    ? t("components.StepList.editStep")
                    : t("components.StepList.showStepSummary")}
                </Styled.TooltipContent>
              </Tooltip>
            )}
          </ProtectedComponent>
        </Styled.NameContainer>
      </Styled.StepDetailsContainer>
      {error ? (
        <Styled.ButtonContainer>
          <ErrorDialogBox
            errorMessage={errorMessage ?? "No error message available"}
            errorDetails={errorDetails}
          >
            <LinkButton data-tracking-id="runner-view-error-details-run-step-button">
              {t("components.Step.viewErrorDetails")}
            </LinkButton>
          </ErrorDialogBox>
        </Styled.ButtonContainer>
      ) : null}
    </Styled.StepCard>
  );
};
