import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Text, Tooltip } from "@floqastinc/flow-ui_core";
import { styled } from "styled-components";

// Step.tsx
export const getBorderStyle = (error: boolean, isSelected: boolean, isHovered: boolean) => {
  if (isSelected) {
    return "2px solid var(--flo-base-color-neutral-500)";
  }
  if (error) {
    return "2px solid var(--flo-sem-color-warning)";
  }
  if (isHovered) {
    return "1px solid var(--flo-sem-color-stroke-forms)";
  }
  return "1px solid var(--flo-base-color-neutral-300)";
};

export const StepCard = styled(Card)<{ error: boolean; isSelected: boolean }>`
  background-color: var(--flo-base-color-neutral-0);
  border: ${({ error, isSelected }) => getBorderStyle(error, isSelected, false)};
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: 12px;
  width: 100%;
  &:hover {
    background-color: var(--flo-base-color-neutral-200);
    border: ${({ error, isSelected }) => getBorderStyle(error, isSelected, true)};
  }
`;

export const StepDetailsContainer = styled.div`
  align-items: center;
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
`;

export const IconContainer = styled.div`
  align-items: center;
  display: flex;
  margin-right: 8px;
  hight: 24px;
  width: 24px;
`;

export const NameContainer = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  width: 100%;
`;

export const IntegrationContainer = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-evenly;
  gap: 4px;
`;

export const Name = styled(Text)<{
  isSelected: boolean;
  showButton: boolean;
  status?: string;
}>`
  color: ${({ status }) =>
    status === "REVIEW" ? "var(--flo-base-color-neutral-0)" : "var(--flo-base-color-neutral-800)"};
  font-size: var(--flo-base-font-size-5);
  font-weight: ${({ isSelected }) =>
    isSelected ? "var(--flo-base-font-weight-7)" : "var(--flo-base-font-weight-5)"};
  line-height: var(--flo-base-line-height-4);
  margin: 0;
  padding-right: ${({ showButton }) => (showButton ? "36px" : "0")};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

export const TooltipContent = styled(Tooltip.Content)`
  transform: translateX(-13px) translateY(-17px);
`;

export const EditOrInfoButton = styled(IconButton)`
  padding: 0;
  position: absolute;
  right: -7px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover {
    background-color: var(--flo-base-color-neutral-300);
  }
`;

export const ButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 16px;
`;

// ReviewStep.tsx
export const ReviewStepCard = styled(Card)<{ isSelected: boolean }>`
  background-color: var(--flo-base-color-neutral-600);
  border: ${({ isSelected }) =>
    isSelected
      ? "2px solid var(--flo-base-color-neutral-1000)"
      : "1px solid var(--flo-base-color-neutral-1000)"};
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: 12px;
  width: 100%;
`;

export const RejectButton = styled(Button)`
  color: var(--flo-base-color-neutral-0);
`;
