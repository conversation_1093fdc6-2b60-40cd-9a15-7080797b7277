import { Card } from "@floqastinc/flow-ui_core";
import { styled } from "styled-components";
import { getBorderStyle } from "./Step.styled";

export const InputsWrapper = styled(Card)`
  border: 1px solid var(--flo-base-color-neutral-300);
  box-sizing: border-box;
  padding: 12px;
  display: flex;
  flex-direction: column;
  height: fit-content;
  overflow: visible;
  width: 100%;
  &:hover {
    background-color: var(--flo-base-color-neutral-200);
    border: ${() => getBorderStyle(false, false, true)};
  }
`;

export const InputsContainer = styled.div`
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  min-width: 0;
  flex-grow: 1;
  height: fit-content;
  padding: 0px 32px;
`;

export const InputWrapper = styled.div<{ $isLast: boolean }>`
  margin-bottom: ${(props) => (props.$isLast ? 0 : 12)}px;
`;
