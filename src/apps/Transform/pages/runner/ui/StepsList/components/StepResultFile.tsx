import TableOutlined from "@floqastinc/flow-ui_icons/material/TableOutlined";
import * as Styled from "./StepResults.styled";

import { FileDownload } from "./FileDownload";
type StepResultFileProps = {
  file: File | null;
  "data-testid"?: string;
};
export const StepResultFile: React.FC<StepResultFileProps> = ({
  file,
  "data-testid": dataTestId,
}: StepResultFileProps) => {
  return (
    <>
      {file ? (
        <Styled.FileWrapper data-testid={dataTestId}>
          <Styled.FileContainer>
            <Styled.IconContainer>
              <TableOutlined size="20" />
            </Styled.IconContainer>
            <FileDownload file={file} />
          </Styled.FileContainer>
        </Styled.FileWrapper>
      ) : null}
    </>
  );
};
