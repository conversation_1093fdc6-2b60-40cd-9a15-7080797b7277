import { useState } from "react";
import { useParams } from "react-router-dom";
import { Text, Flex, Expandable } from "@floqastinc/flow-ui_core";
import ExpandMore from "@floqastinc/flow-ui_icons/material/ExpandMore";
import ExpandLess from "@floqastinc/flow-ui_icons/material/ExpandLess";
import * as types from "@floqastinc/transform-v3";
import { match } from "ts-pattern";
import * as Styled from "./InputsDropDown.styled";
import { FileInput } from "./FileInput";
import { TextInput } from "./TextInput";
import { DateInput } from "./DateInput";
import { PreviewTextInput } from "./PreviewTextInput";
import { t } from "@/utils/i18n";

type InputsDropDownProps = {
  runInputs?: types.WorkflowRunInput[];
  previewInputs?: types.WorkflowInput[];
};

export function InputsDropDown({ runInputs, previewInputs }: InputsDropDownProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const { runId = "" } = useParams();

  const inputs = runId ? runInputs : previewInputs;
  const isExpandedDisabled = !inputs || inputs.length === 0;

  const handleOpen = () => {
    if (!isExpandedDisabled) {
      setIsExpanded((prev) => !prev);
    }
  };

  const color = isExpandedDisabled
    ? "var(--flo-base-color-neutral-400)"
    : "var(--flo-base-color-neutral-900)";

  const InputHeaderText = () => {
    return (
      <Text size={5} color={color}>
        {t("components.InputsDropDown.inputsCount", { count: inputs?.length || 0 })}
      </Text>
    );
  };

  const renderInputView = (input: types.WorkflowRunInput | types.WorkflowInput, index: number) => {
    const isLast = !!inputs && index === inputs.length - 1;

    function isWorkflowRunInput(input: any): input is types.WorkflowRunInput {
      return input && "value" in input;
    }

    if (runId && isWorkflowRunInput(input)) {
      // Run
      return match(input)
        .with({ value: { kind: "FILE" } }, () => (
          <Styled.InputWrapper $isLast={isLast} key={index}>
            <FileInput
              workflowRunInputId={input.id}
              file={(input.value || {}) as types.FileArgument}
            />
          </Styled.InputWrapper>
        ))
        .with({ value: { kind: "DATETIME" } }, () => (
          <Styled.InputWrapper $isLast={isLast} key={index}>
            <DateInput label={input.name} date={(input.value as types.DatetimeArgument).value} />
          </Styled.InputWrapper>
        ))
        .with({ value: { kind: "NUMBER" } }, () => (
          <Styled.InputWrapper $isLast={isLast} key={index}>
            <TextInput label={input.name} text={(input.value as types.TextArgument).value} />
          </Styled.InputWrapper>
        ))
        .with({ value: { kind: "TEXT" } }, () => (
          <Styled.InputWrapper $isLast={isLast} key={index}>
            <TextInput label={input.name} text={(input.value as types.TextArgument).value} />
          </Styled.InputWrapper>
        ))
        .otherwise(() => null);
    } else {
      // Preview
      return <PreviewTextInput label={input.name} />;
    }
  };

  return (
    <Styled.InputsWrapper onClick={handleOpen} data-tracking-id="runner-step-select-input-dropdown">
      <Flex align="center" style={{ gap: "8px", height: "fit-content", cursor: "pointer" }}>
        {isExpanded && !isExpandedDisabled ? <ExpandLess /> : <ExpandMore color={color} />}
        <InputHeaderText />
      </Flex>
      <Expandable open={isExpanded}>
        <Styled.InputsContainer>
          {inputs?.map((input, i) => renderInputView(input, i))}
        </Styled.InputsContainer>
      </Expandable>
    </Styled.InputsWrapper>
  );
}
