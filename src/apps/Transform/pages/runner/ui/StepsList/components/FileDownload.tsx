import React from "react";
import { Button, Text } from "@floqastinc/flow-ui_core";
import Download from "@floqastinc/flow-ui_icons/material/Download";
import { useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import type * as types from "@floqastinc/transform-v3";
import * as Styled from "./FileDownload.styled";
import { downloadFile, downloadFileFromUri } from "@/utils/browser";
import { getRunInputValueUri } from "@BuilderV3/api/runs";

type FileDownloadProps = {
  workflowRunInputId?: string;
  file: types.FileArgument | File;
  taskId?: string;
  exampleInputId?: string;
  exampleSetId?: string;
};
export const FileDownload: React.FC<FileDownloadProps> = ({ workflowRunInputId, file }) => {
  const { runId = "" } = useParams();
  const { data: uri } = useQuery({
    ...getRunInputValueUri({ workflowRunId: runId, workflowRunInputId: workflowRunInputId ?? "" }),
    enabled: !!runId && !!workflowRunInputId,
    select: (data) => data,
  });

  return (
    <Styled.Container>
      <Styled.TextContainer>
        <Text
          truncate={true}
          title={file.name}
          style={{
            whiteSpace: "nowrap",
            textOverflow: "ellipsis",
            overflow: "hidden",
            minWidth: 0,
          }}
        >
          {file.name}
        </Text>
      </Styled.TextContainer>
      <Button
        onClick={() => {
          if (workflowRunInputId && uri?.data) {
            downloadFileFromUri(uri.data, { fileName: file.name });
          } else {
            downloadFile(file as File, { fileName: file.name });
          }
        }}
        size="md"
        variant="ghost"
        color="dark"
        padding={false}
      >
        <Download color="currentColor" />
      </Button>
    </Styled.Container>
  );
};
