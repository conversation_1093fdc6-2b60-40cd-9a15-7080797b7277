import { useQuery } from "@tanstack/react-query";
import { Heading, Text } from "@floqastinc/flow-ui_core";
import * as Styled from "./styles";
import { v3, ApiError } from "@/services/v3";
import { getExamplesQuery } from "@BuilderV3/api/examples";
import { t } from "@/utils/i18n";
import { DetailStepList } from "@/apps/Transform/components/DetailStepsList";

type DetailStepsProps = {
  workflowId: string;
  taskId: string;
  isOpen: boolean;
};

export const DetailSteps = ({ workflowId, taskId, isOpen }: DetailStepsProps) => {
  const activeExampleSetQuery = useQuery({
    ...getExamplesQuery({ workflowId, taskId }),
    select: (data) => data.find((exampleSet) => exampleSet.status === "ACTIVE"),
    enabled: isOpen,
  });

  const examplesQuery = useQuery({
    ...getExamplesQuery({ workflowId, taskId }),
    enabled: isOpen,
  });

  const getDescriptionQuery = useQuery({
    queryKey: ["taskDescription", workflowId, taskId],
    queryFn: async () => {
      const response = await v3.taskDescriptions.getTaskActiveDescription({ workflowId, taskId });

      if (response.errors.length > 0) {
        throw new ApiError(response.errors);
      }

      if (!response.data)
        throw new Error("Unexpected data error: no task description body returned.");

      return response.data;
    },
    enabled: isOpen && !!activeExampleSetQuery.data,
    retry: false,
  });

  if (examplesQuery.data?.[0]?.status === "DRAFT") {
    return (
      <Styled.DetailStep>
        <Styled.DetailStepName>
          <Heading variant="h5" weight="medium">
            {t("components.DetailSteps.unpublishedStep")}
          </Heading>
        </Styled.DetailStepName>
        <Styled.DetailStepDescription>
          <Text truncate={false}>{t("components.DetailSteps.taskUnpublished")}</Text>
        </Styled.DetailStepDescription>
      </Styled.DetailStep>
    );
  }

  return (
    <DetailStepList
      steps={getDescriptionQuery.data?.description ?? []}
      isLoading={activeExampleSetQuery.isPending || getDescriptionQuery.isPending}
    />
  );
};
