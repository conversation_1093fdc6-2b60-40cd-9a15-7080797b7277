import { AgGridReact } from "ag-grid-react";
import { AllCommunityModule, ModuleRegistry, themeQuartz } from "ag-grid-community";
ModuleRegistry.registerModules([AllCommunityModule]);
import {
  Heading,
  IconButton,
  Text,
  EmptyState,
  TableStatusBadge,
  LinkButton,
  Tooltip,
} from "@floqastinc/flow-ui_core";
import Clear from "@floqastinc/flow-ui_icons/legacy/Clear";
import Download from "@floqastinc/flow-ui_icons/material/Download";
import { useEffect, useState, useRef } from "react";
import { ExampleSet, ExampleSetStatus } from "@floqastinc/transform-v3";
import { useParams } from "react-router-dom";
import { useDataProcessor } from "./useDataProcessor";
import * as Styled from "./index.styles";
import { t } from "@/utils/i18n";
import { downloadFile } from "@/utils/browser";
import { Loading } from "@/components/Loading";
import { StatusChangeDialog } from "@Transform/pages/builder/ui/Chat/components/StatusChange/StatusChange";

const gridFilePreviewTheme = themeQuartz.withParams({
  columnBorder: { style: "solid", color: "#cccccc" },
  headerColumnBorder: { style: "solid", color: "#cccccc" },
  rowBorder: { style: "solid", color: "#cccccc" },
  headerRowBorder: { style: "solid", color: "#cccccc" },
  headerBackgroundColor: "#EDEDED",
});

export type FilePreviewMode = "builder" | "runner";

type FileTab = {
  id: string;
  name: string;
  displayName: string;
  type?: "Input" | "Output" | "PreviousOutput";
  stepNumber?: number;
};

type FilePreviewProps = {
  file: File | null;
  isLoading: boolean;
  mode: FilePreviewMode;
  previewName: string;
};

type FilePreviewBuilderProps = FilePreviewProps & {
  mode: "builder";
  example: ExampleSet | undefined;
  fileTabs: FileTab[];
  activeFileId: string;
  onFileTabClick?: (fileId: string) => void;
  onFileTabClose?: (fileId: string) => void;
};

type FilePreviewRunnerProps = FilePreviewProps & {
  mode: "runner";
};

export const FilePreview = (props: FilePreviewBuilderProps | FilePreviewRunnerProps) => {
  const { file, isLoading, mode, previewName } = props;

  const example = props.mode === "builder" ? props.example : undefined;
  const fileTabs = props.mode === "builder" ? props.fileTabs : [];
  const activeFileId = props.mode === "builder" ? props.activeFileId : "";
  const onFileTabClick = props.mode === "builder" ? props.onFileTabClick : undefined;
  const onFileTabClose = props.mode === "builder" ? props.onFileTabClose : undefined;

  const [, setContainerSize] = useState({ width: 0, height: 0 });
  const [statusChange, setStatusChange] = useState<{
    fromStatus: ExampleSetStatus | null;
    toStatus: ExampleSetStatus | null;
    exampleSetId: string | null;
  }>({
    fromStatus: null,
    toStatus: null,
    exampleSetId: null,
  });
  const isStatusChangeDialogOpen = Boolean(
    statusChange.fromStatus && statusChange.toStatus && statusChange.exampleSetId,
  );
  const { workflowId, taskId } = useParams();

  // Refs for container measurements
  const gridContainerRef = useRef<HTMLDivElement>(null);
  const gridRef = useRef<AgGridReact>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  // Use the data processor hook
  const {
    processedData,
    isLoading: isDataProcessorLoading,
    error,
    setCurrentSheet,
  } = useDataProcessor(file);

  // Set up ResizeObserver to monitor container size changes
  useEffect(() => {
    const currentRef = gridContainerRef.current; // Capture ref value
    if (!currentRef) return;

    // Clean up previous observer if it exists
    if (resizeObserverRef.current) {
      resizeObserverRef.current.disconnect();
    }

    // Create new observer
    resizeObserverRef.current = new ResizeObserver((entries) => {
      const entry = entries[0];
      if (entry) {
        const { width, height } = entry.contentRect;
        setContainerSize({ width, height });
      }
    });

    // Start observing
    resizeObserverRef.current.observe(currentRef);

    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, []);

  // Combine loading states - either external loading or internal data processing
  const isCurrentlyLoading = isLoading || isDataProcessorLoading;

  // Show empty state when not loading and no file
  const shouldShowEmptyState = !isCurrentlyLoading && !file;

  const allOutputFileTabs = fileTabs.filter((f) => f.type !== "Input");
  const currentOutputFileExists = allOutputFileTabs.some((f) => f.type === "Output");
  const baseHeaderFileTab = currentOutputFileExists
    ? fileTabs.find((f) => f.type === "Output")
    : fileTabs.find((f) => f.type === "PreviousOutput");
  const clearableFileTabs = currentOutputFileExists
    ? fileTabs.filter((f) => f.type !== "Output")
    : fileTabs.filter((f) => f.type === "Input");

  if (isCurrentlyLoading) {
    return <Loading />;
  }

  if (shouldShowEmptyState) {
    return (
      <Styled.EmptyFilePreview data-testid="empty-file-preview">
        <EmptyState />
        <div>
          <Text weight={5} size={5} lineHeight={4}>
            {t("components.FilePreview.noDataPreview")}
          </Text>
        </div>
      </Styled.EmptyFilePreview>
    );
  }

  if (error) {
    return (
      <Styled.EmptyFilePreview data-testid="error-file-preview">
        <EmptyState />
        <div>
          <Text weight={5} size={5} lineHeight={4}>
            {error}
          </Text>
        </div>
      </Styled.EmptyFilePreview>
    );
  }

  const statusColor = {
    ACTIVE: "success",
    PUBLISHED: "info",
    DRAFT: "default",
    ARCHIVED: "default",
    OUTDATED: "default",
  };

  return (
    <Styled.FilePreviewPanel>
      <StatusChangeDialog
        isStatusChangeDialogOpen={isStatusChangeDialogOpen}
        statusChange={statusChange}
        setStatusChange={setStatusChange}
        workflowId={workflowId || ""}
        taskId={taskId || ""}
        makeActive={statusChange.fromStatus === "PUBLISHED" && statusChange.toStatus === "ACTIVE"}
      />
      <Styled.FilePreviewHeader>
        {!isCurrentlyLoading && file ? (
          <>
            <Styled.FilePreviewHeaderLeft>
              {mode === "runner" ? (
                <Styled.FilePreviewMetaData>
                  <Styled.StepName mode={mode}>
                    <Heading variant="body-base" weight="medium">
                      {previewName}
                    </Heading>
                  </Styled.StepName>
                </Styled.FilePreviewMetaData>
              ) : (
                <Styled.FileTabs>
                  {baseHeaderFileTab && (
                    <Styled.FileTab
                      active={baseHeaderFileTab.id === activeFileId}
                      hasCloseButton={false}
                      onClick={() => {
                        onFileTabClick?.(baseHeaderFileTab.id);
                      }}
                    >
                      <Styled.StepName mode={mode}>
                        <Heading variant="body-base" weight="medium">
                          {previewName}
                        </Heading>
                      </Styled.StepName>
                      {mode === "builder" && (
                        <TableStatusBadge
                          style={{
                            backgroundColor:
                              baseHeaderFileTab.id === activeFileId ? "#EDEDED" : "transparent",
                          }}
                          hasIcon={false}
                          truncateText={false}
                          color={example ? statusColor[example.status] : "default"}
                        >
                          <p>{example?.name}</p>
                          {example && example.status === "PUBLISHED" && (
                            <LinkButton
                              styleOverrides={{
                                text: {
                                  fontSize: "var(--flo-base-font-size-2)",
                                  fontWeight: "var(--flo-base-font-weight-5)",
                                  lineHeight: "var(--flo-base-line-height-2)",
                                },
                              }}
                              onClick={() => {
                                setStatusChange({
                                  fromStatus: example.status,
                                  toStatus: "ACTIVE",
                                  exampleSetId: example.id,
                                });
                              }}
                            >
                              {t("components.FilePreview.makeActive")}
                            </LinkButton>
                          )}
                        </TableStatusBadge>
                      )}
                    </Styled.FileTab>
                  )}
                  <Styled.FileTabsScrollContainer>
                    {(clearableFileTabs ?? []).map((tab) => {
                      return (
                        <Styled.FileTab
                          key={tab.id}
                          active={tab.id === activeFileId}
                          onClick={() => {
                            onFileTabClick?.(tab.id);
                          }}
                          title={tab.displayName}
                          hasCloseButton={true}
                        >
                          <Text size={4} weight={5} lineHeight={3}>
                            {tab.displayName}
                          </Text>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onFileTabClose?.(tab.id);
                            }}
                          >
                            <Clear
                              style={{
                                width: "18px",
                                height: "18px",
                                flexShrink: 0,
                              }}
                            />
                          </button>
                        </Styled.FileTab>
                      );
                    })}
                  </Styled.FileTabsScrollContainer>
                </Styled.FileTabs>
              )}
            </Styled.FilePreviewHeaderLeft>
            <Tooltip>
              <Tooltip.Trigger>
                <IconButton
                  aria-label={t("components.FilePreview.downloadFile")}
                  size="md"
                  data-tracking-id="runner-download-file-button"
                  disabled={!file}
                  onClick={() => {
                    if (file) downloadFile(file, { fileName: file.name });
                  }}
                >
                  <Download />
                </IconButton>
              </Tooltip.Trigger>
              <Tooltip.Content style={{ zIndex: 9001 }}>
                {t("components.FilePreview.downloadFile")}
              </Tooltip.Content>
            </Tooltip>
          </>
        ) : null}
      </Styled.FilePreviewHeader>
      {!isCurrentlyLoading && file && processedData && (
        <Styled.FilePreview>
          <Styled.GridSection>
            <Styled.GridContainer ref={gridContainerRef}>
              <AgGridReact
                key={`${file.name}-${file.lastModified}-${processedData.currentSheet}`}
                ref={gridRef}
                theme={gridFilePreviewTheme}
                columnDefs={processedData.columnDefs}
                rowData={processedData.sheetData}
                domLayout="normal"
                suppressCellFocus={true}
              />
            </Styled.GridContainer>
            <Styled.SheetTabs>
              {processedData.sheetNames.map((tabName) => (
                <Styled.SheetTab
                  key={tabName}
                  active={processedData.currentSheet === tabName}
                  onClick={() => setCurrentSheet(tabName)}
                  title={tabName}
                >
                  <Text size={4} weight={5} lineHeight={3}>
                    {tabName}
                  </Text>
                </Styled.SheetTab>
              ))}
            </Styled.SheetTabs>
          </Styled.GridSection>
        </Styled.FilePreview>
      )}
    </Styled.FilePreviewPanel>
  );
};
