import { useEffect, useRef, useState, useCallback } from "react";
import * as xlsx from "xlsx";

interface ProcessedData {
  currentSheet: string;
  sheetNames: string[];
  sheetData: any[];
  columnDefs: any[];
}

interface UseDataProcessorResult {
  processedData: ProcessedData | null;
  isLoading: boolean;
  error: string | null;
  setCurrentSheet: (sheet: string) => Promise<void>;
}

interface CachedResult {
  processedRowData: any[];
  memoizedColumnDefs: any[];
}

// Utility function to get column letters from Excel range
const getColumnLettersFromRange = (range: string | undefined): string[] => {
  if (!range) return [];

  const match = range.match(/^([A-Z]+)\d+:([A-Z]+)\d+$/);
  if (!match) return [];
  const [_, __, endCol] = match;

  const endNum = xlsx.utils.decode_col(endCol);

  // Create an array of column letters from A to the last column
  return Array.from({ length: endNum + 1 }, (_, i) => xlsx.utils.encode_col(i));
};

// Calculate row number column width based on total row count
const getRowNumberColumnWidth = (rowCount: number): number => {
  const baseWidth = 24;
  const digitWidth = 8;

  if (rowCount <= 0) {
    return Math.max(32, baseWidth + 1 * digitWidth);
  }

  const digits = Math.floor(Math.log10(rowCount)) + 1;
  return Math.max(32, baseWidth + digits * digitWidth);
};

const parseExcel = async (file: File): Promise<xlsx.WorkBook> => {
  const fileBuffer = await file.arrayBuffer();
  const wb = xlsx.read(fileBuffer);
  return wb;
};

export const useDataProcessor = (file: File | null): UseDataProcessorResult => {
  const [isLoading, setIsLoading] = useState(false);
  const [processedData, setProcessedData] = useState<ProcessedData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Internal state
  const [sheets, setSheets] = useState<{ [sheetName: string]: xlsx.WorkSheet } | null>(null);
  const [currentSheet, setCurrentSheet] = useState<string>("");

  // Cache for processed data - using useRef to persist across renders
  const dataCacheRef = useRef<Map<string, CachedResult>>(new Map());

  // Add ref to track latest request for race condition protection
  const latestSheetRequestRef = useRef<string | null>(null);
  const currentFileProcessingRef = useRef<{
    name: string;
    size: number;
    lastModified: number;
  } | null>(null);

  // Memoized data processing function
  const processSheetDataSync = useCallback(
    (
      sheet: xlsx.WorkSheet,
      sheetName: string,
      fileMetadata: {
        name: string;
        size: number;
        lastModified: number;
      },
    ): CachedResult => {
      if (!sheet) {
        return { processedRowData: [], memoizedColumnDefs: [] };
      }

      // Create cache key that includes sheet-specific data
      const cacheKey = `${sheetName}-${JSON.stringify(sheet["!ref"])}-${fileMetadata.name}-${fileMetadata.size}-${fileMetadata.lastModified}`;

      // Check cache first
      const cachedResult = dataCacheRef.current.get(cacheKey);
      if (cachedResult) {
        return cachedResult;
      }

      try {
        const range = getColumnLettersFromRange(sheet["!ref"]);
        const sheetJson = xlsx.utils.sheet_to_json<Record<string, unknown>>(sheet, {
          header: "A",
          defval: "",
          blankrows: true,
          raw: false,
        });

        // Process all data with row numbers
        const processedRowData = sheetJson.map((row, i) => ({ ...row, __: i + 1 }));

        // Generate column definitions
        const columnDefinitions = [
          {
            headerName: " ",
            field: "__",
            width: getRowNumberColumnWidth(processedRowData.length),
            sortable: false,
            pinned: "left" as const,
            cellStyle: {
              backgroundColor: "#EDEDED",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            },
          },
          ...range.map((col: string) => ({
            field: col,
            sortable: false,
          })),
        ];

        const result: CachedResult = {
          processedRowData,
          memoizedColumnDefs: columnDefinitions,
        };

        // Cache the result
        dataCacheRef.current.set(cacheKey, result);

        return result;
      } catch (error) {
        console.error("Error processing sheet data:", error);
        return { processedRowData: [], memoizedColumnDefs: [] };
      }
    },
    [],
  );

  // Process sheet data function
  const processSheetData = useCallback(
    async (
      sheets: { [sheetName: string]: xlsx.WorkSheet },
      activeSheet: string,
      fileMetadata?: {
        name: string;
        size: number;
        lastModified: number;
      },
    ) => {
      if (!sheets || !activeSheet || !sheets[activeSheet]) {
        setIsLoading(false);
        return;
      }

      // Create unique request ID for race condition protection
      const requestId = `${activeSheet}-${Date.now()}-${Math.random()}`;
      latestSheetRequestRef.current = requestId;

      setError(null);

      try {
        // Check if this is still the latest request before proceeding
        if (latestSheetRequestRef.current !== requestId) {
          return; // Bail out if a newer request has started
        }

        // Process the sheet data synchronously
        const { processedRowData, memoizedColumnDefs } = processSheetDataSync(
          sheets[activeSheet],
          activeSheet,
          fileMetadata || { name: "", size: 0, lastModified: 0 },
        );

        // Check again before updating state
        if (latestSheetRequestRef.current !== requestId) {
          return; // Bail out if a newer request has started
        }

        // Update state with processed data
        setProcessedData({
          currentSheet: activeSheet,
          sheetNames: Object.keys(sheets),
          sheetData: processedRowData,
          columnDefs: memoizedColumnDefs,
        });
        setIsLoading(false);
      } catch (err) {
        // Only update error state if this is still the latest request
        if (latestSheetRequestRef.current === requestId) {
          console.error("Data processing error:", err);
          setError(err instanceof Error ? err.message : "Failed to process data");
          setProcessedData(null);
          setIsLoading(false);
        }
      }
    },
    [processSheetDataSync],
  );

  // Process file function
  const processFile = useCallback(
    async (file: File | null) => {
      if (!file) {
        // Reset to idle state when no file
        setIsLoading(false);
        setSheets(null);
        setCurrentSheet("");
        setProcessedData(null);
        setError(null);
        currentFileProcessingRef.current = null;
        return;
      }

      // Set current file being processed for cancellation check
      const fileMetadata = {
        name: file.name,
        size: file.size,
        lastModified: file.lastModified,
      };
      currentFileProcessingRef.current = fileMetadata;

      const isExcel =
        file.name.endsWith(".xlsx") ||
        file.name.endsWith(".csv") ||
        file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

      if (!isExcel) {
        // Only set error if this file is still being processed
        if (currentFileProcessingRef.current === fileMetadata) {
          setError("Unsupported file type");
          setIsLoading(false);
        }
        return;
      }

      try {
        setIsLoading(true);
        setProcessedData(null); // Clear old data immediately to prevent flash
        setError(null);

        const workbook = await parseExcel(file);

        // Check if this file is still being processed (not cancelled by newer file)
        if (currentFileProcessingRef.current !== fileMetadata) {
          return; // Bail out if a newer file has started processing
        }

        if (workbook?.Sheets && Object.keys(workbook.Sheets).length > 0) {
          const firstSheet = Object.keys(workbook.Sheets)[0];

          setSheets(workbook.Sheets);
          setCurrentSheet(firstSheet);

          // Auto-process the first sheet
          await processSheetData(workbook.Sheets, firstSheet, fileMetadata);
        } else {
          // Only update state if this file is still being processed
          if (currentFileProcessingRef.current === fileMetadata) {
            setError("No sheets found in file");
            setIsLoading(false);
          }
        }
      } catch (err) {
        // Only update error state if this file is still being processed
        if (currentFileProcessingRef.current === fileMetadata) {
          console.error("Error parsing file:", err);
          setError("Failed to parse file");
          setIsLoading(false);
        }
      }
    },
    [processSheetData],
  );

  // Handle file changes - this is the main trigger
  useEffect(() => {
    processFile(file);
  }, [file, processFile]);

  // Expose setCurrentSheet to consumer for tab switching
  const handleSetCurrentSheet = useCallback(
    async (sheet: string) => {
      if (sheets && sheets[sheet] && currentSheet !== sheet) {
        setIsLoading(true);
        setCurrentSheet(sheet);
        setProcessedData(null);

        const fileMetadata = file
          ? {
              name: file.name,
              size: file.size,
              lastModified: file.lastModified,
            }
          : undefined;

        await processSheetData(sheets, sheet, fileMetadata);
      }
    },
    [sheets, currentSheet, file, processSheetData],
  );

  // Clean up cache when component unmounts
  useEffect(() => {
    return () => {
      // Clear cache on unmount to prevent memory leaks
      dataCacheRef.current.clear();
    };
  }, []);

  return {
    processedData,
    isLoading,
    error,
    setCurrentSheet: handleSetCurrentSheet,
  };
};
