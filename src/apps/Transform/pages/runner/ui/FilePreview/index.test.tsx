import { describe, test, expect, beforeEach, vi } from "vitest";
import { ExampleSet } from "@floqastinc/transform-v3";
import { FilePreview } from "./index";
import { customRender } from "@/utils/testing";

// Mock the useDataProcessor hook
vi.mock("./useDataProcessor", () => ({
  useDataProcessor: vi.fn(() => ({
    processedData: {
      currentSheet: "Sheet1",
      sheetNames: ["Sheet1"],
      sheetData: [
        { A: "Header1", B: "Header2", C: "Header3", __: 1 },
        { A: "Data1", B: "Data2", C: "Data3", __: 2 },
      ],
      columnDefs: [
        { headerName: " ", field: "__", width: 32, sortable: false, pinned: "left" },
        { field: "A", sortable: false },
        { field: "B", sortable: false },
        { field: "C", sortable: false },
      ],
    },
    isLoading: false,
    error: null,
    setCurrentSheet: vi.fn(),
  })),
}));

// Simplified xlsx mock - only includes functions actually used
vi.mock("xlsx", () => ({
  read: vi.fn().mockReturnValue({
    Sheets: {
      Sheet1: {
        "!ref": "A1:C2",
      },
    },
    SheetNames: ["Sheet1"],
  }),
  utils: {
    sheet_to_json: vi.fn().mockReturnValue([
      { A: "Header1", B: "Header2", C: "Header3" },
      { A: "Data1", B: "Data2", C: "Data3" },
    ]),
    decode_col: vi.fn().mockImplementation((col: string) => {
      const colMap: Record<string, number> = { A: 0, B: 1, C: 2 };
      return colMap[col] || 0;
    }),
    encode_col: vi.fn().mockImplementation((num: number) => {
      const cols = ["A", "B", "C"];
      return cols[num] || "A";
    }),
  },
}));

describe("FilePreview", () => {
  const mockFile = new File(["test-content"], "test.xlsx", {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });

  const defaultProps = {
    file: mockFile,
    isLoading: false,
    mode: "runner" as const,
    previewName: "Step 1 Output",
    example: {
      createdAt: new Date(),
      id: "",
      name: "TEST_VERSION",
      status: "PUBLISHED",
      strategy: {
        conversationStatus: "READY",
        kind: "SCRIPT",
      },
      taskId: "",
      workflowId: "",
    } as ExampleSet,
    fileTabs: [
      {
        id: "output-1",
        name: "output.xlsx",
        displayName: "Output",
        type: "Output" as const,
        stepNumber: 1,
      },
    ],
    activeFileId: "output-1",
    onFileTabClick: (_fileId: string) => {},
    onFileTabClose: (_fileId: string) => {},
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test(`GIVEN a file and isLoading is false,
    THEN it should display the file data, sheet tabs, and name`, async () => {
    const screen = customRender(<FilePreview {...defaultProps} />);

    await expect.element(screen.getByText("Step 1 Output")).toBeVisible();
    await expect.element(screen.getByText("Sheet1")).toBeVisible();
  });

  test(`GIVEN isLoading is true,
       THEN it should show the loading state`, async () => {
    // Mock the hook to return loading state for this test
    const mockUseDataProcessor = vi.mocked(await import("./useDataProcessor")).useDataProcessor;
    mockUseDataProcessor.mockReturnValueOnce({
      processedData: null,
      isLoading: true,
      error: null,
      setCurrentSheet: vi.fn(),
    });

    const screen = customRender(<FilePreview {...defaultProps} isLoading={true} />);

    await expect.element(screen.getByLabelText("Loading Spinner")).toBeVisible();
  });

  test(`GIVEN file is null and isLoading is false,
       THEN it should show the empty state with the i18n text for no data preview`, async () => {
    // Mock the hook to return non-loading state for this test
    const mockUseDataProcessor = vi.mocked(await import("./useDataProcessor")).useDataProcessor;
    mockUseDataProcessor.mockReturnValueOnce({
      processedData: null,
      isLoading: false,
      error: null,
      setCurrentSheet: vi.fn(),
    });

    const screen = customRender(<FilePreview {...defaultProps} file={null} />);

    // Using data-testid to find the empty state container
    await expect.element(screen.getByTestId("empty-file-preview")).toBeVisible();
  });

  // TODO: These two tests will need to be updated once the badge is implemented
  test(`GIVEN builder mode,
       THEN it should display the status badge`, async () => {
    const screen = customRender(<FilePreview {...defaultProps} mode="builder" />);

    await expect.element(screen.getByText("Step 1 Output")).toBeVisible();
    await expect.element(screen.getByText("TEST_VERSION")).toBeVisible();
    await expect.element(screen.getByText("components.FilePreview.makeActive")).toBeVisible();
  });

  test(`GIVEN runner mode,
       THEN it should not display the status badge`, async () => {
    const screen = customRender(<FilePreview {...defaultProps} mode="runner" />);

    // Should display the preview name
    await expect.element(screen.getByText("Step 1 Output")).toBeVisible();
    // Should NOT display the status badge elements
    await expect.element(screen.getByText("TEST_VERSION")).not.toBeInTheDocument();
    await expect
      .element(screen.getByText("components.FilePreview.makeActive"))
      .not.toBeInTheDocument();
  });
});
