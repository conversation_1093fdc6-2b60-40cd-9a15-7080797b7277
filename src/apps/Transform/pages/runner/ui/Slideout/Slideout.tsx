import * as Styled from "./Slideout.styles";

type SlideoutProps = {
  children: React.ReactNode;
  isOpen: boolean;
  expandedWidth: string | number;
  onTransitionEnd: () => void;
  style: React.CSSProperties;
};
// TODO: Reimplement this with SideDrawer. Will need to make a PR
//  to flow-ui to allow for custom widths to be added to the SideDrawer
//  (currently only has two set widths for 'md' and 'lg')
export const Slideout = ({
  children,
  isOpen,
  expandedWidth,
  onTransitionEnd,
  style,
  ...rest
}: SlideoutProps) => {
  const $expandedWidth = typeof expandedWidth === "number" ? `${expandedWidth}px` : expandedWidth;

  return (
    <Styled.RelativeContainer onTransitionEnd={onTransitionEnd} {...rest}>
      <Styled.ExpandableContainer $isOpen={isOpen} $expandedWidth={$expandedWidth} style={style}>
        <Styled.ExpandableContent $expandedWidth={$expandedWidth}>
          {children}
        </Styled.ExpandableContent>
      </Styled.ExpandableContainer>
    </Styled.RelativeContainer>
  );
};
