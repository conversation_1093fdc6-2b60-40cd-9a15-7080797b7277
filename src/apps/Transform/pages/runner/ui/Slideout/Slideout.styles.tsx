import styled from "styled-components";

export const RelativeContainer = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  z-index: var(--fq-z-index-toast);
`;

export const ExpandableContainer = styled.div<{
  $isOpen?: boolean;
  $expandedWidth: string;
}>`
  position: absolute;
  top: 0;
  left: 100%;
  height: 100%;
  overflow: scroll;
  transition: all 0.3s ease-in-out;
  width: ${({ $isOpen, $expandedWidth }) => ($isOpen ? $expandedWidth : "0")};
  min-width: ${({ $isOpen, $expandedWidth }) => ($isOpen ? $expandedWidth : "0")};
`;

export const ExpandableContent = styled.div<{ $expandedWidth: string }>`
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  min-width: ${({ $expandedWidth }) => $expandedWidth};
`;
