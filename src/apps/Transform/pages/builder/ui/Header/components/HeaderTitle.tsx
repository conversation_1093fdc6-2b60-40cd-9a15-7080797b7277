import { useState, useEffect, useCallback } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Heading, IconButton, Flex, Tooltip, useToast } from "@floqastinc/flow-ui_core";
import Edit from "@floqastinc/flow-ui_icons/material/EditOutlined";
import Check from "@floqastinc/flow-ui_icons/material/Check";
import Close from "@floqastinc/flow-ui_icons/material/Close";
import { z } from "zod";
import { t } from "@/utils/i18n";
import { Workflow } from "@floqastinc/transform-v3";

import * as Styled from "./HeaderTitle.styles";
import { getWorkflowQuery } from "@BuilderV3/api/workflows";
import { queryKeys } from "@BuilderV3/api/query-keys";
import { useUpdateWorkflow } from "@v3/workflows";

interface HeaderTitleProps {
  workflowId: string;
}
type UpdateWorkflowContext = {
  previousWorkflow?: Workflow;
  previousWorkflows?: { pages: Array<{ data: Workflow[] }> };
};
export const HeaderTitle = ({ workflowId }: HeaderTitleProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState("");
  const { data: workflow } = useQuery(getWorkflowQuery(workflowId));
  const updateWorkflowMutation = useUpdateWorkflow({
    onMutate: async ({ workflowId, workflow }): Promise<UpdateWorkflowContext> => {
      await queryClient.cancelQueries({ queryKey: queryKeys.workflows.byId(workflowId) });

      const previousWorkflow = queryClient.getQueryData<Workflow>(
        queryKeys.workflows.byId(workflowId),
      );

      // Optimistically update the workflow name
      if (previousWorkflow) {
        queryClient.setQueryData<Workflow>(queryKeys.workflows.byId(workflowId), {
          ...previousWorkflow,
          ...(workflow.name !== undefined && { name: workflow.name }),
        });
      }

      // Also update the workflow in the list if it exists there
      const previousWorkflows = queryClient.getQueryData<{ pages: Array<{ data: Workflow[] }> }>(
        queryKeys.workflows.all(),
      );
      if (previousWorkflows) {
        const updatedPages = previousWorkflows.pages.map((page) => ({
          ...page,
          data: page.data.map((w) => (w.id === workflowId ? { ...w, name: workflow.name } : w)),
        }));

        queryClient.setQueryData(queryKeys.workflows.all(), {
          ...previousWorkflows,
          pages: updatedPages,
        });
      }

      return { previousWorkflow, previousWorkflows };
    },
    onSuccess: () => {
      setIsEditing(false);
      setIsHovered(false);
    },
    onError: (error, { workflowId }, context) => {
      console.error("consumer error: Failed to update agent:", error);

      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.HeaderTitle.toast.errorTitle")}</Toast.Title>
          <Toast.Message>
            {error instanceof Error
              ? error.message
              : t("components.HeaderTitle.toast.updateAgentError")}
          </Toast.Message>
        </Toast>,
        // eslint-disable-next-line i18next/no-literal-string
        { position: "top-right" },
      );

      const typedContext = context as UpdateWorkflowContext; // guaranteed by onMutate return

      // Rollback to the previous value
      if (typedContext?.previousWorkflow) {
        queryClient.setQueryData(
          queryKeys.workflows.byId(workflowId),
          typedContext.previousWorkflow,
        );
      }
      if (typedContext?.previousWorkflows) {
        queryClient.setQueryData(queryKeys.workflows.all(), typedContext.previousWorkflows);
      }
    },
    onSettled: (data, error, { workflowId }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.workflows.byId(workflowId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.workflows.all() });
    },
  });
  const queryClient = useQueryClient();

  // Initialize editedName when workflow data is loaded
  useEffect(() => {
    if (workflow?.name) {
      setEditedName(workflow.name);
    }
  }, [workflow?.name]);

  const { showToast, Toast } = useToast();

  const handleSave = useCallback(() => {
    const MAX_NAME_LENGTH = 100;
    const workflowNameSchema = z
      .string()
      .min(1, t("components.HeaderTitle.errors.nameRequired"))
      .max(
        MAX_NAME_LENGTH,
        t("components.HeaderTitle.errors.nameTooLong", { max: MAX_NAME_LENGTH }),
      );

    try {
      const validatedName = workflowNameSchema.parse(editedName.trim());
      updateWorkflowMutation.mutate({ workflowId, workflow: { name: validatedName } });
    } catch (error) {
      if (error instanceof z.ZodError) {
        showToast(
          <Toast type="error">
            <Toast.Title>{t("components.HeaderTitle.toast.invalidInputTitle")}</Toast.Title>
            <Toast.Message>{error.errors[0].message}</Toast.Message>
          </Toast>,
          // eslint-disable-next-line i18next/no-literal-string
          { position: "top-right" },
        );
      }
    }
  }, [editedName, updateWorkflowMutation, workflowId, showToast, Toast]);

  const handleCancel = useCallback(() => {
    setEditedName(workflow?.name || "");
    setIsHovered(false);
    setIsEditing(false);
  }, [workflow?.name]);

  // Handle Escape key at the document level when in edit mode
  useEffect(() => {
    if (!isEditing) return;

    const handleDocumentKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        handleCancel();
      }
    };

    document.addEventListener("keydown", handleDocumentKeyDown);
    return () => {
      document.removeEventListener("keydown", handleDocumentKeyDown);
    };
  }, [isEditing, handleCancel]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter") {
        handleSave();
      }
    },
    [handleSave],
  );

  return (
    <Styled.TitleContainer
      onMouseEnter={() => !isEditing && setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {isEditing ? (
        <Flex alignItems="center">
          <Styled.StyledInput
            value={editedName}
            onChange={(updatedText: string) => {
              setEditedName(updatedText);
            }}
            onKeyDown={handleKeyDown}
            disabled={updateWorkflowMutation.isPending}
            aria-label={t("components.HeaderTitle.ariaLabels.editWorkflowName")}
            data-testid="edit-workflow-name-input"
          />
          <Flex gap="8px" style={{ marginLeft: "8px" }}>
            <IconButton
              size="small"
              variant="ghost"
              color="success"
              aria-label={t("components.HeaderTitle.ariaLabels.saveChanges")}
              data-testid="save-workflow-name-button"
              onClick={handleSave}
              disabled={updateWorkflowMutation.isPending}
            >
              <Check />
            </IconButton>
            <IconButton
              size="small"
              variant="ghost"
              color="danger"
              aria-label={t("components.HeaderTitle.ariaLabels.cancelEditing")}
              data-testid="cancel-workflow-name-edit-button"
              onClick={handleCancel}
              disabled={updateWorkflowMutation.isPending}
            >
              <Close />
            </IconButton>
          </Flex>
        </Flex>
      ) : (
        <>
          <Heading variant="h4" weight="medium">
            {workflow?.name}
          </Heading>
          {isHovered && (
            <Tooltip>
              <Tooltip.Trigger>
                <IconButton
                  size="small"
                  variant="ghost"
                  color="neutral"
                  onClick={() => {
                    setIsEditing(true);
                  }}
                  data-testid="edit-workflow-name"
                >
                  <Edit />
                </IconButton>
              </Tooltip.Trigger>
              <Tooltip.Content>{t("components.HeaderTitle.tooltip.editName")}</Tooltip.Content>
            </Tooltip>
          )}
        </>
      )}
    </Styled.TitleContainer>
  );
};

export default HeaderTitle;
