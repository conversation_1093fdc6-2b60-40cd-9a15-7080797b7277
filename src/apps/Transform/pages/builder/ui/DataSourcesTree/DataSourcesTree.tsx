import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>iew, Skeleton, Input } from "@floqastinc/flow-ui_core";
import { SearchOutlined } from "@floqastinc/flow-ui_icons";
import Add from "@floqastinc/flow-ui_icons/material/Add";
import { EditorView } from "codemirror";
import { useCallback, useState } from "react";
import Refresh from "@floqastinc/flow-ui_icons/material/Refresh";
import { QueryObserverResult, RefetchOptions } from "@tanstack/react-query";
import { match } from "ts-pattern";
import Coupa from "@floqastinc/flow-ui_icons/fq/Coupa";
import Netsuite from "@floqastinc/flow-ui_icons/fq/Netsuite";
import Intacct from "@floqastinc/flow-ui_icons/fq/Intacct";
import Workday from "@floqastinc/flow-ui_icons/fq/Workday";
import TableOutlined from "@floqastinc/flow-ui_icons/material/TableOutlined";
import { <PERSON><PERSON><PERSON>r, BackgroundSearchContainer, SideBar, SkeletonContainer } from "./styles";
import { createSystemKey } from "@BuilderV3/utils/conversionFunctions";
import { FlolakeConnectionData, FlolakeResponse } from "@/api/shared/types";

type ConnectionIconProps = {
  connection: string;
};
const ConnectionIcon = ({ connection }: ConnectionIconProps) => {
  return match(connection)
    .with("EXCEL", () => <TableOutlined size={20} />)
    .with("COUPA", () => <Coupa size={20} />)
    .with("NETSUITE", () => <Netsuite size={20} />)
    .with("INTACCT", () => <Intacct size={20} />)
    .with("WORKDAY", () => <Workday size={20} />)
    .otherwise(() => null);
};
interface DataSourcesTreeProps {
  connections: FlolakeConnectionData[];
  isFlolakeDataLoading: boolean;
  editor: EditorView | null;
  isDraft: boolean;
  refetch: (
    options?: RefetchOptions | undefined,
  ) => Promise<QueryObserverResult<FlolakeResponse, Error>>;
}
export const DataSourcesTree = ({
  connections,
  isFlolakeDataLoading,
  editor,
  isDraft,
  refetch,
}: DataSourcesTreeProps) => {
  const [searchTerm, setSearchTerm] = useState("");

  const handleInsertSchema = useCallback(
    (schema: string) => {
      if (editor) {
        const cursorPos = editor.state.selection.main.head;
        const insertPosition =
          cursorPos === -1 ||
          cursorPos === editor.state.doc.length ||
          (cursorPos === 0 && editor.state.doc.length > 0)
            ? editor.state.doc.length
            : cursorPos;

        editor.dispatch({
          changes: {
            from: insertPosition,
            to: insertPosition,
            insert: schema,
          },
        });
        editor.focus();
      }
    },
    [editor],
  );

  // Function to sort column names properly, especially for numerical vals
  const sortColumnNames = (columns: any[]): any[] => {
    return [...columns].sort((a, b) => {
      // Extract all numbers from the column names
      const aNumbers = a.name.match(/\d+/g) || [];
      const bNumbers = b.name.match(/\d+/g) || [];

      // If both names contain numbers, compare them numerically
      if (aNumbers.length > 0 && bNumbers.length > 0) {
        // Compare each number in sequence
        for (let i = 0; i < Math.min(aNumbers.length, bNumbers.length); i++) {
          const aNum = parseInt(aNumbers[i], 10);
          const bNum = parseInt(bNumbers[i], 10);

          if (aNum !== bNum) {
            return aNum - bNum;
          }
        }

        // If all numbers are equal, compare the full strings
        return a.name.localeCompare(b.name);
      }

      // Default string comparison for columns without numbers
      return a.name.localeCompare(b.name);
    });
  };

  const filterData = (data: any) => {
    if (!searchTerm) return true;
    const term = searchTerm.toLowerCase();

    const sourceName = data.transformSystem?.toLowerCase() || "";
    const connectionName = data.connectionName?.toLowerCase() || "";

    // If source or connection name matches, show everything
    if (sourceName.includes(term) || connectionName.includes(term)) {
      return true;
    }

    // Check if any tables match the search term
    const hasMatchingTables = data.tableData?.some((table: any) =>
      table.tableName?.toLowerCase().includes(term),
    );

    if (hasMatchingTables) {
      return true;
    }

    // If searching in columns, only show tables with matching columns
    return data.tableData?.some((table: any) =>
      table.columns?.some(
        (column: any) =>
          column.name?.toLowerCase().includes(term) || column.type?.toLowerCase().includes(term),
      ),
    );
  };

  const getFilteredTables = (tables: any[], sourceName: string, connectionName: string) => {
    if (!searchTerm) return tables;
    const term = searchTerm.toLowerCase();

    // If source or connection name matches, show all tables
    if (sourceName?.toLowerCase().includes(term) || connectionName?.toLowerCase().includes(term)) {
      return tables;
    }

    // If searching for table names, only show matching tables
    const hasTableMatches = tables.some((table: any) =>
      table.tableName?.toLowerCase().includes(term),
    );

    if (hasTableMatches) {
      return tables.filter((table: any) => table.tableName?.toLowerCase().includes(term));
    }

    // If searching in columns, only show tables with matching columns
    return tables.filter((table: any) =>
      table.columns?.some(
        (column: any) =>
          column.name?.toLowerCase().includes(term) || column.type?.toLowerCase().includes(term),
      ),
    );
  };

  const getFilteredColumns = (
    columns: any[],
    sourceName: string,
    tableName: string,
    connectionName: string,
  ) => {
    if (!searchTerm) return columns;
    const term = searchTerm.toLowerCase();

    // If source, table, or connection name matches, show all columns
    if (
      sourceName?.toLowerCase().includes(term) ||
      tableName?.toLowerCase().includes(term) ||
      connectionName?.toLowerCase().includes(term)
    ) {
      return columns;
    }

    // Otherwise only show matching columns
    return columns.filter(
      (column: any) =>
        column.name?.toLowerCase().includes(term) || column.type?.toLowerCase().includes(term),
    );
  };

  // Check if we should expand a CONNECTION based on search
  const shouldExpandConnection = (conn: any) => {
    if (!searchTerm) return false;
    const term = searchTerm.toLowerCase();
    const sourceName = conn.transformSystem?.toLowerCase() || "";
    const connectionName = conn.connectionName?.toLowerCase() || "";

    // Expand if connection name matches or if it has matching tables/columns
    return (
      sourceName.includes(term) ||
      connectionName.includes(term) ||
      getFilteredTables(conn.tableData, conn.transformSystem, conn.connectionName).length > 0
    );
  };

  // Check if we should expand a TABLE based on search
  const shouldExpandTable = (table: any, conn: any) => {
    if (!searchTerm) return false;
    const term = searchTerm.toLowerCase();
    const tableName = table.tableName?.toLowerCase() || "";

    // Expand if table name matches or if it has matching columns
    return (
      tableName.includes(term) ||
      getFilteredColumns(table.columns, conn.transformSystem, table.tableName, conn.connectionName)
        .length > 0
    );
  };

  return (
    <SideBar>
      <BackgroundSearchContainer style={{ padding: "0px 8px 8px 0px" }}>
        <SearchContainer>
          <SearchOutlined size={24} style={{}} />
          <Input
            onChange={(value: string) => setSearchTerm(value)}
            aria-label="Search Connections"
            isDisabled={!connections || connections.length === 0}
            placeholder="Start searching..."
            styleOverrides={{
              inputWrapper: {
                border: "none",
                boxShadow: "none",
              },
            }}
            style={{
              border: "none",
              backgroundColor: "var(--flo-sem-color-surface-neutral-weaker)",
            }}
            value={searchTerm}
          />
          <IconButton onClick={refetch} size={20}>
            <Refresh />
          </IconButton>
        </SearchContainer>
      </BackgroundSearchContainer>
      <SideBar.Menu>
        {isFlolakeDataLoading ? (
          <SkeletonContainer>
            <Skeleton variant="rectangle" height={32} width="100%" />
            <Skeleton variant="rectangle" height={32} width="100%" />
          </SkeletonContainer>
        ) : !connections || connections.length === 0 ? (
          <SideBar.Item isDisabled>No connections available</SideBar.Item>
        ) : (
          <TreeView
            key={searchTerm} // Force re-render when search changes
            collapseIcon={<TableOutlined size={20} />}
            expandIcon={<TableOutlined size={20} />}
          >
            {connections &&
              connections.filter(filterData).map((conn: any, index: number) => {
                if (!conn.tableData) {
                  return null;
                }

                const sourceName =
                  conn.transformSystem?.charAt(0).toUpperCase() +
                  conn.transformSystem?.slice(1).toLowerCase();

                return (
                  <TreeView.Section
                    key={`${conn.integrationSystem}-${index}`}
                    itemId={conn.integrationSystem}
                    label={
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                        }}
                      >
                        <span style={{ display: "flex", gap: "4px", alignItems: "center" }}>
                          {<ConnectionIcon connection={sourceName?.toUpperCase()} />}
                          {sourceName} ({conn.connectionName})
                        </span>
                      </div>
                    }
                    initiallyExpanded={shouldExpandConnection(conn)}
                  >
                    {getFilteredTables(
                      conn.tableData,
                      conn.transformSystem,
                      conn.connectionName,
                    ).map((table: any) => {
                      const tableName =
                        table.tableName?.charAt(0).toUpperCase() +
                        table.tableName?.slice(1).toLowerCase();
                      const tableId = `${table.schemaName}.${table.tableName}`;
                      return (
                        <TreeView.Item
                          key={tableId}
                          itemId={tableId}
                          label={tableName}
                          initiallyExpanded={shouldExpandTable(table, conn)}
                        >
                          {getFilteredColumns(
                            sortColumnNames(table.columns),
                            conn.transformSystem,
                            table.tableName,
                            conn.connectionName,
                          ).map((column: any) => {
                            const columnId = `${tableId}.${column.name}`;

                            return (
                              <TreeView.Item
                                key={columnId}
                                itemId={columnId}
                                style={{
                                  width: "100%",
                                }}
                                onClick={() => {
                                  if (!isDraft) return;
                                  const systemName = createSystemKey(conn);
                                  const schema = `{${systemName}}.{${table.tableName?.toUpperCase()}}.{${column.name?.toUpperCase()}}`;
                                  handleInsertSchema(schema);
                                }}
                                label={
                                  <div
                                    style={{
                                      display: "flex",
                                      alignItems: "center",
                                      justifyContent: "flex-start",
                                      width: "100%",
                                      overflow: "hidden",
                                    }}
                                  >
                                    <IconButton
                                      size="sm"
                                      disabled={!isDraft}
                                      onClick={() => {
                                        if (!isDraft) return;
                                        const systemName = createSystemKey(conn);
                                        const schema = `{${systemName}}.{${table.tableName?.toUpperCase()}}.{${column.name?.toUpperCase()}}`;
                                        handleInsertSchema(schema);
                                      }}
                                    >
                                      <Add />
                                    </IconButton>
                                    <span
                                      style={{
                                        whiteSpace: "nowrap",
                                        overflow: "hidden",
                                        textOverflow: "ellipsis",
                                        flex: 1,
                                      }}
                                    >
                                      {`${column.name} (${column.type})`}
                                    </span>
                                  </div>
                                }
                              />
                            );
                          })}
                        </TreeView.Item>
                      );
                    })}
                  </TreeView.Section>
                );
              })}
          </TreeView>
        )}
      </SideBar.Menu>
    </SideBar>
  );
};
