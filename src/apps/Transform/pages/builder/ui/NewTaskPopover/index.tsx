import {
  I<PERSON><PERSON><PERSON><PERSON>,
  Popover,
  Card,
  Text,
  Input,
  DropdownPanel,
  DropdownButton,
  Button,
  InputWrapper,
} from "@floqastinc/flow-ui_core";
import Add from "@floqastinc/flow-ui_icons/material/Add";
import { useState, ReactNode } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useMutation } from "@tanstack/react-query";
import * as Styled from "./index.styles";
import { useStrategies, CreateableStrategyKind } from "./hooks/useStrategies";
import { t } from "@/utils/i18n";
import { LoadingButton } from "@/components/LoadingButton/LoadingButton";
import { createTask, CreateTaskParams } from "@/api/bff/tasks";
import { useTasks } from "@v3/tasks";
import { useExamples } from "@v3/examples";

type NewTaskPopoverProps = {
  trigger?: ReactNode;
  labels?: {
    title: string;
    strategy: string;
    submitButton: string;
  };
  position?: number;
  previousTaskId?: string;
};

export const NewTaskPopover = ({
  trigger,
  labels,
  position,
  previousTaskId,
}: NewTaskPopoverProps) => {
  const { workflowId = "" } = useParams();
  const navigate = useNavigate();

  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [strategyKind, setStrategyKind] = useState<CreateableStrategyKind | undefined>(undefined);
  const [isStrategyDropdownOpen, setIsStrategyDropdownOpen] = useState(false);

  const {
    title: titleLabel,
    strategy: strategyLabel,
    submitButton: submitButtonLabel,
  } = labels ?? {};

  const tasksQuery = useTasks(
    {
      workflowId,
    },
    {
      enabled: !!workflowId,
    },
  );

  const lastTaskId =
    position === undefined ? tasksQuery.data?.[tasksQuery.data.length - 1]?.id : undefined;
  const taskIdForExample = previousTaskId || lastTaskId;

  const examplesQuery = useExamples(
    {
      workflowId,
      taskId: taskIdForExample!, // Using non-null assertion since query is disabled when lastTaskId is undefined
    },
    {
      enabled: !!workflowId && !!taskIdForExample,
    },
  );
  const strategies = useStrategies();
  const defaultStrategy = strategies.find((s) => s.kind === strategyKind)?.defaultStrategy;

  const hasActiveExample = examplesQuery.data?.some((example) => example.status === "ACTIVE");

  // NOTE: This presumes that there is an output for the task.
  const isNewTaskCreationDisabled = (tasksQuery.data?.length ?? 0) > 0 && !hasActiveExample;

  const createTaskMutation = useMutation({
    mutationKey: ["createTask", workflowId],
    mutationFn: (params: CreateTaskParams) => createTask(params),
    onSuccess: (data) => {
      setOpen(false);
      navigate(
        `/builder/v3/agents/${workflowId}/steps/${data.task.id}/examples/${data.example.id}`,
        { replace: true },
      );
    },
    onError: (error) => {
      console.error("Error creating task:", error);
    },
  });

  const handleSubmit = () => {
    if (!title || !defaultStrategy) {
      return;
    }

    createTaskMutation.mutate({
      task: {
        name: title,
        description,
        strategy: defaultStrategy,
        position,
      },
      workflowId,
      previousTaskId: previousTaskId || lastTaskId,
    });
  };

  return (
    <Popover
      data-testid="new-task-popover"
      modal={true}
      open={open}
      onOpenChange={(isOpen: boolean) => {
        setOpen(isOpen);
      }}
    >
      <Popover.Trigger>
        {trigger ? (
          trigger
        ) : (
          <IconButton disabled={isNewTaskCreationDisabled}>
            <Add />
          </IconButton>
        )}
      </Popover.Trigger>
      <Popover.Content side="right">
        <Card>
          <Card.Header separator={false}>
            <Text weight={6}>{titleLabel ?? t("components.NewTaskPopover.createStep")}</Text>
          </Card.Header>
          <Card.Description>
            <Styled.CardContent>
              <Input
                label={t("components.NewTaskPopover.title")}
                placeholder={t("components.NewTaskPopover.enterTitle")}
                value={title}
                onChange={(value: string) => {
                  setTitle(value);
                }}
              />
              <Input
                label={t("components.NewTaskPopover.description")}
                placeholder={t("components.NewTaskPopover.enterDesc")}
                value={description}
                onChange={(value: string) => {
                  setDescription(value);
                }}
              />
              <InputWrapper>
                <InputWrapper.Label>
                  {strategyLabel ?? t("components.NewTaskPopover.selectTool")}
                </InputWrapper.Label>
                <DropdownPanel
                  style={{ width: "100%" }}
                  onChange={(value: CreateableStrategyKind) => {
                    setStrategyKind(value);
                    setIsStrategyDropdownOpen(false);
                  }}
                  isOpen={isStrategyDropdownOpen}
                  onOpenChange={(isOpen: boolean) => {
                    setIsStrategyDropdownOpen(isOpen);
                  }}
                  disableClear={true}
                  disableFilter={true}
                  selectedValues={strategyKind}
                >
                  <DropdownPanel.Trigger>
                    <DropdownButton>
                      <Text>
                        {strategyKind
                          ? strategies.find((s) => s.kind === strategyKind)?.label
                          : t("components.NewTaskPopover.selectStrat")}
                      </Text>
                    </DropdownButton>
                  </DropdownPanel.Trigger>
                  <DropdownPanel.Content>
                    {strategies.map(({ kind, label }) => (
                      <DropdownPanel.Option key={kind} value={kind} name={label}>
                        {label}
                      </DropdownPanel.Option>
                    ))}
                  </DropdownPanel.Content>
                </DropdownPanel>
              </InputWrapper>
              {createTaskMutation.isPending ? (
                <Styled.ButtonContainer>
                  <LoadingButton data-testid="new-task-loading-button">
                    {submitButtonLabel ?? t("components.NewTaskPopover.submit")}
                  </LoadingButton>
                </Styled.ButtonContainer>
              ) : (
                <Styled.ButtonContainer>
                  <Button
                    data-testid="new-task-submit-button"
                    disabled={!title || !strategyKind}
                    onClick={handleSubmit}
                  >
                    {submitButtonLabel ?? t("components.NewTaskPopover.submit")}
                  </Button>
                </Styled.ButtonContainer>
              )}
            </Styled.CardContent>
          </Card.Description>
        </Card>
      </Popover.Content>
    </Popover>
  );
};
