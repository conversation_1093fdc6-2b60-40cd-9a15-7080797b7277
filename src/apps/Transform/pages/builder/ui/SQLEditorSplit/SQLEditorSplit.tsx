import { use<PERSON>allback, useEffect, useMemo, useRef, useState } from "react";
import Markdown from "react-markdown";
import { useParams } from "react-router";
import { match } from "ts-pattern";
import { sql } from "@codemirror/lang-sql";
import { Compartment } from "@codemirror/state";
import type { NewLlmMetricEntityType, NewLlmMetricMetricType } from "@floqastinc/transform-v3";
import {
  Button,
  ButtonGroup,
  IconButton,
  Modal,
  Skeleton,
  Spinner,
  Text,
  TextArea,
  useToast,
} from "@floqastinc/flow-ui_core";
import ContentyCopyOutlined from "@floqastinc/flow-ui_icons/material/ContentCopyOutlined";
import InfoOutlined from "@floqastinc/flow-ui_icons/material/InfoOutlined";
import PlayCircleOutlined from "@floqastinc/flow-ui_icons/material/PlayCircleOutlined";
import ThumbUpOutlined from "@floqastinc/flow-ui_icons/material/ThumbUpOutlined";
import ThumbUp from "@floqastinc/flow-ui_icons/material/ThumbUp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { basicSetup, EditorView } from "codemirror";
import { Parser } from "node-sql-parser";
import { TooltipIconButton } from "../Chat/components/TooltipIconButton";
import * as Styled from "./styled";
import { t } from "@/utils/i18n";
import { queryClient } from "@/components";
import { Li } from "@/components/Li";
import { FlolakeConnectionData } from "@/api/shared/types";
import { v3 } from "@/services/v3";
import { ArrowWarmUpRun } from "@/svg/ArrowWarmUpRun";
import { getExampleOutputsQuery } from "@BuilderV3/api/example-outputs";
import { getExampleQuery } from "@BuilderV3/api/examples";
import { queryKeys } from "@BuilderV3/api/query-keys";
import { getWorkflowTaskQuery } from "@BuilderV3/api/tasks";
import {
  convertSchemaToToken,
  convertTokenToSchema,
  convertVariablesToSnowflake,
  convertSnowflakeToVariables,
} from "@BuilderV3/utils/conversionFunctions";
import { createSchemaReferenceExtensions } from "@BuilderV3/utils/schemaReferenceExtensions";
import { createVariableReferenceExtensions } from "@BuilderV3/utils/variableExtensions";
import {
  hasTooltipWarning,
  scanQueryForWarnings,
  tooltipErrorMessage,
} from "@BuilderV3/utils/tooltip";
import { ConnectionsProvider } from "@Transform/pages/connections/ConnectionsProvider";
import { useNlToSqlConversation } from "@/hooks/useNlToSqlConversation";
import {
  useGetConversations,
  useConversation,
  isSuccessResponse,
} from "@/api/shared/nl-to-sql-conversation";

const METRIC_TYPE: NewLlmMetricMetricType = "PRIMITIVE";
const ENTITY_TYPE: NewLlmMetricEntityType = "TASK";

type SQLEditorSplitProps = {
  connections: FlolakeConnectionData[];
  editor: EditorView | null;
  setEditor: React.Dispatch<React.SetStateAction<EditorView | null>>;
  isFlolakeDataLoading: boolean;
  setIsSQLFileLoading: (isLoading: boolean) => void;
  variableIdMapping?: Record<string, string>;
};

export const SQLEditorSplit = ({
  setIsSQLFileLoading,
  editor,
  setEditor,
  connections,
  isFlolakeDataLoading,
  variableIdMapping,
}: SQLEditorSplitProps) => {
  const { workflowId = "", taskId = "", exampleSetId = "" } = useParams();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentQuery, setCurrentQuery] = useState<string>("");
  const [isQueryConverting, setIsQueryConverting] = useState<boolean>(false);
  const [activeState, setActiveState] = useState({ chat: false, sql: true });
  const [chatMessage, setChatMessage] = useState("");
  const containerRef = useRef<HTMLDivElement | null>(null);
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const [isAtBottom, setIsAtBottom] = useState(true);
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [feedbackType, setFeedbackType] = useState<"thumbsUp" | "thumbsDown">("thumbsUp");
  const [feedbackText, setFeedbackText] = useState("");
  const [isFeedbackSubmitted, setIsFeedbackSubmitted] = useState(false);

  const schemaExtensionsRef = useRef(new Compartment());
  const variableExtensionsRef = useRef(new Compartment());

  const { showToast, Toast } = useToast();
  const sqlParser = useMemo(() => new Parser(), []);
  const taskQuery = useQuery(getWorkflowTaskQuery({ workflowId, taskId }));
  const currentTask = taskQuery.data;
  const taskStrategy = currentTask?.strategy;

  const thumbsUpFeedback = isFeedbackSubmitted && feedbackType === "thumbsUp";
  const thumbsDownFeedback = isFeedbackSubmitted && feedbackType === "thumbsDown";

  // Get task inputs for variable handling
  const taskInputsQuery = useQuery({
    queryKey: ["taskInputs", { workflowId, taskId }],
    queryFn: async () => {
      const response = await v3.taskInputs.getTaskInputs({ workflowId, taskId });
      if (response.errors?.length) {
        throw new Error(response.errors[0]?.title || "Failed to fetch task inputs");
      }
      return response.data || [];
    },
    enabled: !!workflowId && !!taskId,
  });

  const taskInputs = taskInputsQuery.data || [];

  const exampleOutputsQuery = useQuery({
    ...getExampleOutputsQuery({ workflowId, taskId, exampleSetId }),
    enabled: !!workflowId && !!taskId && !!exampleSetId,
  });
  const exampleSetQuery = useQuery({
    ...getExampleQuery({
      workflowId,
      taskId,
      exampleSetId,
    }),
    enabled: !!workflowId && !!taskId && !!exampleSetId,
  });

  const exampleStrategyData = exampleSetQuery.data;
  const exampleStrategy = exampleSetQuery.data?.strategy;
  const isDraft = exampleSetQuery.data?.status === "DRAFT";

  useEffect(() => {
    const view = new EditorView({
      doc: "",
      extensions: [
        basicSetup,
        EditorView.lineWrapping,
        EditorView.lineWrapping,
        EditorView.theme({
          "&": {
            fontSize: "14px",
          },
          ".cm-content": {
            fontFamily: "monospace",
            lineHeight: "1.6",
          },
        }),
        isDraft && !isQueryConverting
          ? EditorView.editable.of(true)
          : EditorView.editable.of(false),
        EditorView.updateListener.of((update) => {
          if (update.docChanged) {
            const query = update.state.doc.toString();
            setCurrentQuery(query);
          }
        }),
        sql({ upperCaseKeywords: true }),
        schemaExtensionsRef.current.of([]),
        variableExtensionsRef.current.of([]),
      ],
      parent: document.querySelector("#editor") as HTMLElement,
    });

    setEditor(view);

    return () => {
      view.destroy();
    };
  }, [isDraft, setCurrentQuery, isQueryConverting, taskStrategy, setEditor]);

  // Get conversation list
  const conversationsQuery = useGetConversations(workflowId, taskId, exampleSetId);
  const conversations = conversationsQuery.data?.data || [];
  const mostRecentConversationId = conversations.length > 0 ? conversations[0].id : undefined;
  const conversationQuery = useConversation(
    workflowId,
    taskId,
    exampleSetId,
    mostRecentConversationId,
    !!mostRecentConversationId,
  );

  // Conversation manager hook
  const {
    conversationMessages: messages,
    sendMessage,
    isStarting,
    isAddingFeedback,
  } = useNlToSqlConversation({
    workflowId,
    taskId,
    exampleSetId,
    initialConversation:
      conversationQuery.data?.data && isSuccessResponse(conversationQuery.data)
        ? conversationQuery.data.data
        : undefined,
  });

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    if (isAtBottom && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, isAtBottom]);

  // Memoized handlers
  const handleSendMessage = useCallback(async () => {
    if (!chatMessage.trim()) return;

    try {
      const msg = chatMessage;
      setChatMessage("");
      await sendMessage(msg);
      // Note: Success toast is handled implicitly by the successful message display
    } catch (error: unknown) {
      console.error("SQLEditorSplit - Message send error:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to send message";
      showAppToast("error", "Message Send Error", errorMessage);
    }
  }, [chatMessage, sendMessage, showAppToast]);

  useEffect(() => {
    if (editor && connections && connections.length > 0) {
      try {
        editor.dispatch({
          effects: schemaExtensionsRef.current.reconfigure(
            createSchemaReferenceExtensions(connections),
          ),
        });
      } catch (error) {
        console.error("Error applying schema extensions:", error);
        // Fallback: try to clear the extensions if there's a conflict
        try {
          editor.dispatch({
            effects: schemaExtensionsRef.current.reconfigure([]),
          });
        } catch (fallbackError) {
          console.error("Error clearing schema extensions:", fallbackError);
        }
      }
    }
  }, [editor, connections]);

  useEffect(() => {
    if (editor && taskInputs.length > 0) {
      try {
        const variableInputs = taskInputs.map((input) => ({
          id: input.id,
          name: input.name,
          type: input.type,
        }));

        // Only reconfigure if we have valid variable inputs
        if (variableInputs.length > 0) {
          editor.dispatch({
            effects: variableExtensionsRef.current.reconfigure(
              createVariableReferenceExtensions(variableInputs),
            ),
          });
        }
      } catch (error) {
        console.error("Error applying variable extensions:", error);
        // Fallback: try to clear the extensions if there's a conflict
        try {
          editor.dispatch({
            effects: variableExtensionsRef.current.reconfigure([]),
          });
        } catch (fallbackError) {
          console.error("Error clearing variable extensions:", fallbackError);
        }
      }
    }
  }, [editor, taskInputs]);

  useEffect(() => {
    if (exampleStrategy?.kind === "FLOLAKE") {
      // Determine which statement to use - fall back to taskStrategy if exampleStrategy statement is empty
      const statement =
        exampleStrategy.statement ||
        (taskStrategy?.kind === "FLOLAKE" ? taskStrategy.statement : "");

      if (statement) {
        setIsQueryConverting(true);
      }

      if (statement && !isFlolakeDataLoading) {
        setIsQueryConverting(false);
      }

      if (statement && editor && !isFlolakeDataLoading) {
        // Convert schema tokens first
        let tokenizedQuery = convertSchemaToToken(statement, connections);

        // Then convert Snowflake parameters to variables if we have bindMapping and taskInputs
        if (exampleStrategy.bindMapping?.length && taskInputs.length > 0) {
          tokenizedQuery = convertSnowflakeToVariables(
            tokenizedQuery,
            exampleStrategy.bindMapping,
            taskInputs,
          );
        }

        editor.dispatch({
          changes: {
            from: 0,
            to: editor.state.doc.length,
            insert: tokenizedQuery,
          },
        });
        editor.focus();
      }
    }
  }, [
    taskStrategy,
    editor,
    isFlolakeDataLoading,
    isDraft,
    exampleStrategy,
    taskInputs,
    connections,
  ]);

  const validateSQL = (sql: string): { isValid: boolean; error?: string } => {
    try {
      // TODO: dc - this is a hack to replace the bindmapping with dummy values so that the sql parser can parse it
      const sqlWithDummyValues = sql.replace(/:\d+/g, "1");

      sqlParser.astify(sqlWithDummyValues, {
        database: "snowflake",
      });
      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  };

  function showAppToast(
    type: "success" | "error",
    title: string,
    message: string | React.ReactNode,
  ) {
    showToast(
      <Toast type={type}>
        <Toast.Title>{title}</Toast.Title>
        <Toast.Message style={{ WebkitLineClamp: 10 }}>{message}</Toast.Message>
      </Toast>,
      { position: "top-right" },
    );
  }

  const extractSourcesFromQuery = (sqlQuery: string): string[] => {
    // Look for TLC format with underscores and extract the source name
    // Example: TLC_66CA67A8F9B2F84CB05E2791_5T_67C1F3BE664390427180208A_ERP_COUPA.ACCOUNT_TYPE
    const sourceRegex = /TLC_[A-Z0-9]+_[A-Z0-9]+_[A-Z0-9]+_ERP_([A-Z]+)\./gi;
    const sources = new Set<string>();

    let match;
    while ((match = sourceRegex.exec(sqlQuery)) !== null) {
      // eslint-disable-next-line no-restricted-syntax
      const sourceName = match[1].toUpperCase();
      sources.add(sourceName);
    }

    return Array.from(sources);
  };

  // Mutations
  const updateExampleMutation = useMutation({
    mutationFn: async ({ query, bindMapping }: { query: string; bindMapping?: string[] }) => {
      if (!exampleStrategyData) {
        throw new Error("Current task not found");
      }

      const sources = extractSourcesFromQuery(query);

      const updatedExample = {
        name: exampleStrategyData.name,
        status: exampleStrategyData.status,
        strategy: {
          kind: "FLOLAKE" as const,
          statement: query,
          bindMapping: bindMapping || [],
          sources: sources as any,
        },
      };

      return v3.examples.updateExample({
        workflowId,
        taskId,
        exampleSetId,
        example: updatedExample,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.workflows.byId(workflowId),
      });
    },
    onError: (data) => {
      console.error("Error updating task:", data);
    },
  });

  const executeFlolakeSQL = useMutation({
    mutationFn: async ({ exampleOutputId }: { exampleOutputId: string }) => {
      const response = await v3.flolakeExecuteService.executeFlolakeSQL({
        workflowId,
        taskId,
        exampleSetId,
        exampleOutputId: exampleOutputId,
      });

      if (response?.errors?.length) {
        console.error(response.errors);
        throw new Error("Network response was not ok");
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.taskOutputs.byTask({ workflowId, taskId }),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.exampleOutputs.getExampleOutputs({
          workflowId,
          taskId,
          exampleSetId,
        }),
      });
      queryClient.invalidateQueries({
        queryKey: [
          {
            resource: "exampleOutputs",
            params: { workflowId, taskId, exampleSetId },
          },
        ],
      });
    },
    onError: (error) => {
      showAppToast(
        "error",
        t("components.SQLEditorSplit.taskRunFailed"),
        t("components.SQLEditorSplit.pleaseCheckQuery"),
      );
      console.error("Error executing workflow:", error);
    },
  });

  const handleExecute = async (query?: string, generated?: boolean) => {
    try {
      setIsLoading(true);
      setIsSQLFileLoading(true);

      const queryToUse = query || currentQuery;
      scanQueryForWarnings(queryToUse, connections);

      if (hasTooltipWarning) {
        showAppToast(
          "error",
          t("components.SQLEditorSplit.Errors.invalidConnection"),
          tooltipErrorMessage || t("components.SQLEditorSplit.checkSQLSyntax"),
        );
        setIsLoading(false);
        setIsSQLFileLoading(false);
        return;
      }

      let convertedQuery = queryToUse;
      let bindMapping: string[] = [];

      if (!generated) {
        // First convert schema tokens
        convertedQuery = convertTokenToSchema(queryToUse, connections);

        // Then convert variables to Snowflake parameters and get bind mapping
        const variableConversion = convertVariablesToSnowflake(
          convertedQuery,
          taskInputs,
          variableIdMapping,
        );
        convertedQuery = variableConversion.convertedQuery;
        bindMapping = variableConversion.bindMapping;
      }

      if (!convertedQuery) {
        showAppToast(
          "error",
          t("components.SQLEditorSplit.Errors.noConvertedSQL"),
          t("components.SQLEditorSplit.Errors.noConvertedSQLMessage"),
        );
        setIsLoading(false);
        setIsSQLFileLoading(false);
        return false;
      }

      const validationResult = validateSQL(convertedQuery);
      if (!validationResult.isValid) {
        const errorMessage = validationResult.error ? (
          <>
            {t("components.SQLEditorSplit.checkSQLSyntax")}
            <br />
            {validationResult.error}
          </>
        ) : (
          t("components.SQLEditorSplit.checkSQLSyntax")
        );

        showAppToast("error", t("components.SQLEditorSplit.invalidSQLQuery"), errorMessage);
        setIsLoading(false);
        setIsSQLFileLoading(false);
        return false;
      }

      await updateExampleMutation.mutateAsync({ query: convertedQuery, bindMapping });

      const exampleOutputId = exampleOutputsQuery.data?.[0]?.id;

      if (!exampleOutputId) {
        throw new Error("No example output id found");
      }

      await executeFlolakeSQL.mutateAsync({ exampleOutputId });

      showAppToast(
        "success",
        t("components.SQLEditorSplit.taskRunSuccess"),
        t("components.SQLEditorSplit.taskBeenRunSuccessfully"),
      );
      setIsLoading(false);
      setIsSQLFileLoading(false);
    } catch (error) {
      console.error("Error executing workflow:", error);
      setIsLoading(false);
      setIsSQLFileLoading(false);
    }
  };

  const handleScroll = () => {
    if (containerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
      // Consider "at bottom" if within 100px of the bottom
      const atBottom = scrollHeight - scrollTop - clientHeight < 100;
      setIsAtBottom(atBottom);
    }
  };

  const submitFeedbackMutation = useMutation({
    mutationFn: async ({
      feedbackType,
      feedbackText,
    }: {
      feedbackType: "thumbsUp" | "thumbsDown";
      feedbackText: string;
    }) => {
      // TODO: consider updating messages to use Message type defined in @floqastinc/transform-v3
      // so we can include `messageId` in the metrics log
      // Other data we can include: userId, model, entityId
      return v3.llmMetricsLogsService.createLlmMetric({
        metric: {
          metricName: "thumbsUp",
          value: {
            kind: "NUMBER",
            numberValue: feedbackType === "thumbsUp" ? 1 : 0,
          },
          metricType: METRIC_TYPE,
          entityType: ENTITY_TYPE,
          taskId,
          workflowId,
          exampleSetId,
          metadata: {
            additionalFeedback: feedbackText,
            stepType: "sql",
            prompt: messages[messages.length - 2]?.content || "",
            responseSql: messages[messages.length - 1]?.metadata?.sqlQuery || "",
            responseMessage: messages[messages.length - 1]?.metadata?.sqlDescription || "",
            responseStatus: messages[messages.length - 1]?.metadata?.errorDetails
              ? "error"
              : "success",
            responseError: messages[messages.length - 1]?.metadata?.errorDetails || "",
          },
        },
        workflowId,
      });
    },
    onSuccess: () => {
      showAppToast(
        "success",
        t("components.SQLEditorSplit.LlmFeedback.feedbackSubmitted"),
        t("components.SQLEditorSplit.LlmFeedback.feedbackSubmittedSuccess"),
      );
      setFeedbackText("");
      setIsFeedbackModalOpen(false);
      setIsFeedbackSubmitted(true);
    },
    onError: (error) => {
      console.error("Failed to submit feedback:", error);
      showAppToast(
        "error",
        t("components.SQLEditorSplit.LlmFeedback.feedbackSubmittedFailed"),
        t("components.SQLEditorSplit.LlmFeedback.feedbackSubmittedFailedMessage"),
      );
    },
  });

  const handleFeedbackClick = useCallback((type: "thumbsUp" | "thumbsDown") => {
    setFeedbackType(type);
    setIsFeedbackModalOpen(true);
  }, []);

  const handleSubmitFeedback = () => {
    // TODO: uncomment this once the metrics API bug is fixed
    //submitFeedbackMutation.mutate({ feedbackType, feedbackText });

    // Tracking in Gainsight while we wait for the metrics API bug to be fixed
    if (window.aptrinsic)
      window.aptrinsic("track", "builder-rate-sql-response", {
        rating: feedbackType === "thumbsUp" ? 1 : 0,
        additionalFeedback: feedbackText,
        prompt: messages[messages.length - 2]?.content || "",
        responseSql: messages[messages.length - 1]?.metadata?.sqlQuery || "",
        responseMessage: messages[messages.length - 1]?.metadata?.sqlDescription || "",
        responseStatus: messages[messages.length - 1]?.metadata?.errorDetails ? "error" : "success",
        responseError: messages[messages.length - 1]?.metadata?.errorDetails || "",
      });

    showAppToast(
      "success",
      t("components.SQLEditorSplit.LlmFeedback.feedbackSubmitted"),
      t("components.SQLEditorSplit.LlmFeedback.feedbackSubmittedSuccess"),
    );
    setFeedbackText("");
    setIsFeedbackModalOpen(false);
    setIsFeedbackSubmitted(true);
  };

  const handleCloseFeedbackModal = useCallback(() => {
    setFeedbackText("");
    setIsFeedbackModalOpen(false);
  }, []);

  return (
    <ConnectionsProvider>
      <Styled.SQLEditorContainer>
        <Styled.SQLEditorHeader>
          <Text weight={4} size={5} lineHeight={8} style={{ paddingLeft: "10px" }}>
            {t("components.SQLEditorSplit.editor")}
          </Text>
          <Styled.SQLEditorActionsContainer>
            <ButtonGroup style={{ padding: "12px 8px 12px 0px" }}>
              <ButtonGroup.Button
                onClick={() => setActiveState({ chat: true, sql: false })}
                style={{ height: "32px" }}
                isActive={activeState.chat}
              >
                <Text style={{}}>{t("components.SQLEditorSplit.chat")}</Text>
              </ButtonGroup.Button>
              <ButtonGroup.Button
                isActive={activeState.sql}
                onClick={() => setActiveState({ chat: false, sql: true })}
                style={{ height: "32px", width: "100%" }}
              >
                <Text style={{}}>{t("components.SQLEditorSplit.sqlEditor")}</Text>
              </ButtonGroup.Button>
            </ButtonGroup>
            <Button
              style={{ height: "32px", width: "92px" }}
              onClick={() => handleExecute()}
              disabled={isLoading || !isDraft}
            >
              {t("components.SQLEditorSplit.generate")}
            </Button>
          </Styled.SQLEditorActionsContainer>
        </Styled.SQLEditorHeader>

        <Styled.SQLEditorWrapper
          id="editor"
          style={{ display: activeState.sql ? "block" : "none" }}
        />

        {/* Can possibly be pulled out into separate component */}
        {activeState.chat && (
          <Styled.ChatContent>
            <Styled.ChatLog ref={containerRef} onScroll={handleScroll}>
              <Styled.ChatLogMessages>
                {messages.map((message, index) => {
                  return match(message)
                    .with({ role: "USER" }, (msg) => (
                      <Styled.UserChatContainer key={index}>
                        <Styled.UserChat>
                          {message.content && (
                            <>
                              <Markdown
                                components={{
                                  li({ node: _node, ...rest }) {
                                    return <Li {...rest} />;
                                  },
                                }}
                              >
                                {message.content}
                              </Markdown>
                              <TooltipIconButton
                                title={t("components.SQLEditorSplit.copyUserPrompt")}
                                tooltipMessage={t("components.SQLEditorSplit.copyUserPrompt")}
                                icon={<ContentyCopyOutlined />}
                                onClick={() => navigator.clipboard.writeText(msg.content ?? "")}
                              />
                            </>
                          )}
                        </Styled.UserChat>
                      </Styled.UserChatContainer>
                    ))

                    .with({ role: "ASSISTANT" }, () => {
                      const hasError = !!message.metadata?.errorDetails;
                      const sqlQuery = message.metadata?.sqlQuery;
                      const sqlDescription = message.metadata?.sqlDescription;

                      return (
                        <Styled.AssistantChat key={index}>
                          <Markdown
                            components={{
                              li({ node: _node, ...rest }) {
                                return <Li {...rest} />;
                              },
                            }}
                          >
                            {hasError ? message.metadata?.errorDetails : sqlQuery}
                          </Markdown>
                          {!hasError && sqlQuery && (
                            <div
                              style={{
                                display: "flex",
                                flexDirection: "row",
                                gap: 4,
                                marginTop: 8,
                              }}
                            >
                              <TooltipIconButton
                                title={t("components.SQLEditorSplit.copySQL")}
                                tooltipMessage={t("components.SQLEditorSplit.copySQL")}
                                icon={<ContentyCopyOutlined />}
                                onClick={() => navigator.clipboard.writeText(sqlQuery ?? "")}
                                data-tracking-id="sql-editor-copy-sql"
                              />

                              {index === messages.length - 1 && (
                                <>
                                  <div style={thumbsDownFeedback ? { display: "none" } : {}}>
                                    <TooltipIconButton
                                      title={t(
                                        "components.SQLEditorSplit.LlmFeedback.rateResponseGood",
                                      )}
                                      tooltipMessage={t(
                                        "components.SQLEditorSplit.LlmFeedback.rateResponseGood",
                                      )}
                                      icon={
                                        thumbsUpFeedback ? (
                                          <ThumbUp
                                            style={{
                                              fill: "var(--flo-sem-color-content-success-strong)",
                                            }}
                                          />
                                        ) : (
                                          <ThumbUpOutlined />
                                        )
                                      }
                                      onClick={() => handleFeedbackClick("thumbsUp")}
                                      disabled={isFeedbackSubmitted}
                                      data-tracking-id="sql-editor-feedback-thumbs-up"
                                    />
                                  </div>

                                  <div style={thumbsUpFeedback ? { display: "none" } : {}}>
                                    <TooltipIconButton
                                      title={t(
                                        "components.SQLEditorSplit.LlmFeedback.rateResponseBad",
                                      )}
                                      tooltipMessage={t(
                                        "components.SQLEditorSplit.LlmFeedback.rateResponseBad",
                                      )}
                                      icon={
                                        thumbsDownFeedback ? (
                                          <ThumbUp
                                            style={{
                                              transform: "rotate(180deg)",
                                              fill: "var(--flo-sem-color-content-danger-stronger)",
                                            }}
                                          />
                                        ) : (
                                          <ThumbUpOutlined
                                            style={{ transform: "rotate(180deg)" }}
                                          />
                                        )
                                      }
                                      onClick={() => handleFeedbackClick("thumbsDown")}
                                      disabled={isFeedbackSubmitted}
                                      data-tracking-id="sql-editor-feedback-thumbs-down"
                                    />
                                  </div>
                                </>
                              )}

                              <TooltipIconButton
                                title={t("components.SQLEditorSplit.executeLLMGeneratedSQL")}
                                tooltipMessage={t(
                                  "components.SQLEditorSplit.executeLLMGeneratedSQL",
                                )}
                                icon={<PlayCircleOutlined />}
                                onClick={() => handleExecute(sqlQuery, true)}
                                data-tracking-id="sql-editor-execute-sql"
                              />

                              <TooltipIconButton
                                title={t("components.SQLEditorSplit.viewLLMDescription")}
                                tooltipMessage={sqlDescription}
                                icon={<InfoOutlined />}
                                onClick={() => {}}
                                data-tracking-id="sql-editor-view-description"
                              />
                            </div>
                          )}

                          {hasError && message.metadata?.errorDetails && (
                            <div
                              style={{
                                display: "flex",
                                flexDirection: "row",
                                gap: 4,
                                marginTop: 8,
                              }}
                            >
                              <TooltipIconButton
                                title={t("components.SQLEditorSplit.copySQLErrorMessage")}
                                tooltipMessage={t("components.SQLEditorSplit.copySQLErrorMessage")}
                                icon={<ContentyCopyOutlined />}
                                onClick={() =>
                                  navigator.clipboard.writeText(
                                    message.metadata?.errorDetails ?? "",
                                  )
                                }
                                data-tracking-id="sql-editor-copy-error-message"
                              />

                              {index === messages.length - 1 && (
                                <>
                                  <div style={thumbsDownFeedback ? { display: "none" } : {}}>
                                    <TooltipIconButton
                                      title={t(
                                        "components.SQLEditorSplit.LlmFeedback.rateResponseGood",
                                      )}
                                      tooltipMessage={t(
                                        "components.SQLEditorSplit.LlmFeedback.rateResponseGood",
                                      )}
                                      icon={
                                        thumbsUpFeedback ? (
                                          <ThumbUp
                                            style={{
                                              fill: "var(--flo-sem-color-content-success-strong)",
                                            }}
                                          />
                                        ) : (
                                          <ThumbUpOutlined />
                                        )
                                      }
                                      onClick={() => handleFeedbackClick("thumbsUp")}
                                      disabled={isFeedbackSubmitted}
                                      data-tracking-id="sql-editor-feedback-thumbs-up"
                                    />
                                  </div>

                                  <div style={thumbsUpFeedback ? { display: "none" } : {}}>
                                    <TooltipIconButton
                                      title={t(
                                        "components.SQLEditorSplit.LlmFeedback.rateResponseBad",
                                      )}
                                      tooltipMessage={t(
                                        "components.SQLEditorSplit.LlmFeedback.rateResponseBad",
                                      )}
                                      icon={
                                        thumbsDownFeedback ? (
                                          <ThumbUp
                                            style={{
                                              transform: "rotate(180deg)",
                                              fill: "var(--flo-sem-color-content-danger-stronger)",
                                            }}
                                          />
                                        ) : (
                                          <ThumbUpOutlined
                                            style={{ transform: "rotate(180deg)" }}
                                          />
                                        )
                                      }
                                      onClick={() => handleFeedbackClick("thumbsDown")}
                                      disabled={isFeedbackSubmitted}
                                      data-tracking-id="sql-editor-feedback-thumbs-down"
                                    />
                                  </div>
                                </>
                              )}
                            </div>
                          )}
                        </Styled.AssistantChat>
                      );
                    })
                    .otherwise(() => null);
                })}
                {(isStarting || isAddingFeedback) && (
                  <Styled.AssistantChat>
                    <div style={{ display: "flex", flexDirection: "row", gap: "2px" }}>
                      <Skeleton variant="circle" radius="4" />
                      <Skeleton variant="circle" radius="4" />
                      <Skeleton variant="circle" radius="4" />
                    </div>
                  </Styled.AssistantChat>
                )}
                <div ref={messagesEndRef} style={{ height: "0" }}></div>
              </Styled.ChatLogMessages>
              {isDraft && (
                <Styled.ChatBox>
                  <TextArea
                    aria-label={t("components.SQLEditorSplit.chatMSGTextArea")}
                    placeholder={t("components.SQLEditorSplit.typeCommand")}
                    value={chatMessage}
                    onChange={setChatMessage}
                    disabled={isStarting || isAddingFeedback || isLoading}
                    styleOverrides={{
                      textarea: {
                        width: "100%",
                        height: "60px",
                        minHeight: "60px",
                        borderBottomRightRadius: "0",
                        borderBottomLeftRadius: "0",
                        borderTopRightRadius: "8px",
                        borderTopLeftRadius: "8px",
                        border: "1px solid var(--flo-sem-color-border)",
                        borderBottom: "none",
                        outline: "none",
                        boxShadow: "none",
                      },
                    }}
                  />

                  <Styled.TextAreaActionBar>
                    {/* For future add button */}
                    <div></div>
                    <Styled.SubmitChatButton
                      onClick={handleSendMessage}
                      disabled={!chatMessage.trim() || isStarting || isAddingFeedback}
                      data-testid="submit-chat-button"
                    >
                      {isStarting || isAddingFeedback ? <Spinner /> : <ArrowWarmUpRun />}
                    </Styled.SubmitChatButton>
                  </Styled.TextAreaActionBar>
                </Styled.ChatBox>
              )}
            </Styled.ChatLog>
          </Styled.ChatContent>
        )}

        {/* Feedback Modal */}
        <Modal open={isFeedbackModalOpen} onOpenChange={handleCloseFeedbackModal}>
          <Modal.Header>{t("components.SQLEditorSplit.LlmFeedback.submitFeedback")}</Modal.Header>
          <Modal.Body>
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                gap: "16px",
                marginBottom: "32px",
              }}
            >
              <IconButton
                onClick={() => setFeedbackType("thumbsUp")}
                size="lg"
                data-tracking-id="sql-editor-feedback-thumbs-up"
              >
                {feedbackType === "thumbsUp" ? (
                  <ThumbUp style={{ fill: "var(--flo-sem-color-content-success-strong)" }} />
                ) : (
                  <ThumbUpOutlined />
                )}
              </IconButton>
              <IconButton
                onClick={() => setFeedbackType("thumbsDown")}
                size="lg"
                data-tracking-id="sql-editor-feedback-thumbs-down"
              >
                {feedbackType === "thumbsDown" ? (
                  <ThumbUp
                    style={{
                      transform: "rotate(180deg)",
                      fill: "var(--flo-sem-color-content-danger-stronger)",
                    }}
                  />
                ) : (
                  <ThumbUpOutlined style={{ transform: "rotate(180deg)" }} />
                )}
              </IconButton>
            </div>
            <TextArea
              label={t("components.SQLEditorSplit.LlmFeedback.additionalFeedbackOptional")}
              isLabelVisible
              value={feedbackText}
              onChange={setFeedbackText}
              autoGrowVertical
              autoGrowVerticalMinHeight="60px"
              styleOverrides={{
                textarea: { width: "100%" },
              }}
            />
          </Modal.Body>
          <Modal.Footer>
            <Modal.FooterCancelBtn
              onClick={handleCloseFeedbackModal}
              data-tracking-id="sql-editor-feedback-cancel-button"
            >
              {t("components.SQLEditorSplit.cancel")}
            </Modal.FooterCancelBtn>
            <Modal.FooterActionBtn
              onClick={handleSubmitFeedback}
              disabled={submitFeedbackMutation?.isPending}
              data-tracking-id="sql-editor-feedback-submit-button"
            >
              {t("components.SQLEditorSplit.submit")}
            </Modal.FooterActionBtn>
          </Modal.Footer>
        </Modal>
      </Styled.SQLEditorContainer>
    </ConnectionsProvider>
  );
};
