import { useParams } from "react-router-dom";
import { Heading, Skeleton } from "@floqastinc/flow-ui_core";
import { t } from "@/utils/i18n";
import { useSuspenseTask, useSuspenseTasks } from "@v3/tasks";

export const BuilderSectionHeader = () => {
  const { workflowId = "", taskId = "" } = useParams();
  const { data: tasks } = useSuspenseTasks(
    {
      workflowId,
    },
    {
      enabled: !!workflowId,
    },
  );
  const { data: task } = useSuspenseTask(
    {
      workflowId,
      taskId,
    },
    {
      enabled: !!workflowId && !!taskId,
    },
  );

  const taskIndex = tasks.findIndex((task) => task.id === taskId);
  if (taskIndex === -1) {
    return (
      <Heading variant="h5" weight="medium">
        {t("components.BuilderSectionHeader.Errors.taskNotFound")}
      </Heading>
    );
  }
  const offsetTaskIndex = taskIndex + 1;

  return (
    <Heading variant="h5" weight="medium">
      {/* eslint-disable-next-line i18next/no-literal-string */}
      {offsetTaskIndex}. {task.name}
    </Heading>
  );
};

const Pending = () => {
  return <Skeleton variant="text" height="100%" lineHeight={24} width="50%" />;
};

BuilderSectionHeader.Pending = Pending;
