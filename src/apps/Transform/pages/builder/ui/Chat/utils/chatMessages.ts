import { Message } from "@floqastinc/transform-v3";
import { match } from "ts-pattern";
import { not } from "@/utils/functional";
import { BUILDER_PAGE_STRINGS } from "@/utils/string";

const isInputPrompt = (message: Message) => {
  return !!message.metadata?.isInputPrompt;
};
const isMessageActionPrompt = (
  message: Message,
): message is Message & {
  metadata: {
    isMessageActionPrompt: true;
    action: string;
  };
} => {
  // TODO: update this with proper type narrowing
  return !!message.metadata?.isMessageActionPrompt;
};

const isTextContent = (
  message: Message,
): message is Message & {
  content: {
    text: {
      value: string;
    };
  }[];
} => {
  return message.content?.[0].type === "text" && !!message.content[0].text?.value;
};

export type SelectedMessage = {
  id: string | undefined;
  content: string;
  role: string;
};
export const selectMessages = (messages: Message[]): SelectedMessage[] => {
  // Regex to match variations of "Download the <file> file" (e.g., "Download the highlighted file", etc.)
  const fileDownloadRegex =
    /You can download the (highlighted|modified|updated|filtered|correct|latest|.*) file(?: using the link below:)?/gi;

  // Regex to match the file link pattern (sandbox path or URLs)
  const fileLinkRegex = /\[.*\]\(sandbox:[^)]+\)/gi;

  return messages
    .filter(not(isInputPrompt))
    .filter(not(isMessageActionPrompt))
    .map((message) => ({
      id: message.id,
      content: match(message)
        .when(isTextContent, (message) => message.content[0].text.value)
        .otherwise(() => "")
        .replace(fileDownloadRegex, BUILDER_PAGE_STRINGS.USE_DOWNLOAD_BUTTON)
        .replace(fileLinkRegex, "")
        .trim(),
      role: message.role ? message.role : "assistant",
    }))
    .reverse();
};
