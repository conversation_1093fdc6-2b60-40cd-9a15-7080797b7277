import * as styled from "./styles";
import WarningIcon from "@/components/WarningIcon";
import { useEditAgentStep } from "@/hooks/useEditAgentStep";
import { t } from "@/utils/i18n";

interface PropTypes {
  workflowId: string;
  taskId: string;
  exampleSetId?: string;
}

export const StepWarningMessage = ({ workflowId, taskId, exampleSetId }: PropTypes) => {
  const { mutate, isPending } = useEditAgentStep();
  const handleClick = () => {
    mutate({
      workflowId,
      taskId,
      example: {
        status: "DRAFT",
        copyFromExampleSetId: exampleSetId,
      },
    });
  };
  return (
    <styled.WarningChat>
      <WarningIcon /> {t("components.StepWarningMessage.stepWarningMessage")}
      <styled.Button variant="link" onClick={handleClick} disabled={isPending} loading={isPending}>
        {t("components.StepWarningMessage.stepWarningMessageEdit")}
      </styled.Button>
    </styled.WarningChat>
  );
};
