import styled from "styled-components";
import { Button as ButtonComponent } from "@floqastinc/flow-ui_core";
import { ChatMessage } from "../../index.styles";

export const WarningChat = styled(ChatMessage)`
  border: 1px solid var(--flo-sem-color-warning);
  background-color: var(--flo-sem-color-surface-warning-weaker);
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
`;

export const Button = styled(ButtonComponent)`
  text-decoration: underline;
  padding: 0;
  font-weight: normal;
`;
