import { Dialog } from "@floqastinc/flow-ui_core";
import { match } from "ts-pattern";
import { t } from "@/utils/i18n";

type ConfirmationDialogProps = {
  message: any;
  action: "edit" | "delete" | "regenerate";
  onConfirm: () => void;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
};
export const ConfirmationDialog = ({
  message: _message,
  action,
  isOpen,
  onOpenChange,
  onConfirm,
}: ConfirmationDialogProps) => {
  const confirmationDialogContent = match(action)
    .with("edit", () => ({
      header: "Edit Message",
      body: t("components.Chat.editMessage"),
      action: () => {},
    }))
    .with("delete", () => ({
      header: "Delete Message",
      body: t("components.Chat.deleteMessage"),
      action: () => {},
    }))
    .with("regenerate", () => ({
      header: "Regenerate Assistant Response",
      body: t("components.Chat.areYouSureRegenerateResponse"),
      action: () => {},
    }))
    .exhaustive();

  return isOpen ? (
    <Dialog
      type="warning"
      open={action !== null}
      onOpenChange={(isOpen: boolean) => {
        if (!isOpen) {
          onOpenChange(false);
        }
      }}
    >
      <Dialog.Header>{confirmationDialogContent.header}</Dialog.Header>
      <Dialog.Body>{confirmationDialogContent.body}</Dialog.Body>
      <Dialog.Footer>
        <Dialog.FooterCancelBtn
          onClick={() => onOpenChange(false)}
          data-tracking-id="builder-message-dialog-confirmation-cancel-button"
        >
          {t("components.Chat.cancel")}
        </Dialog.FooterCancelBtn>
        <Dialog.FooterActionBtn
          onClick={() => {
            onConfirm();
            onOpenChange(false);
          }}
          data-tracking-id="builder-message-dialog-confirmation-confirm-button"
        >
          {t("components.Chat.confirm")}
        </Dialog.FooterActionBtn>
      </Dialog.Footer>
    </Dialog>
  ) : null;
};
