import { LocalStorage } from "@/utils/local-storage";
import { t } from "@/utils/i18n";
import { useAtom } from "jotai";
import { atomWithStorage } from "jotai/utils";
import { debounce } from "lodash";
import { useCallback, useEffect, useMemo } from "react";
import { z } from "zod";

export interface UseChatMessagesProps {
  workflowId: string;
  taskId: string;
  exampleSetId: string;
  storageKeyPrefix?: string;
}

interface UseChatMessagesReturn {
  message: string;
  setMessage: (message: string) => void;
  originalMessage: string;
  setOriginalMessage: (message: string) => void;
  optimizedContent: string;
  setOptimizedContent: (message: string) => void;
}

const MAX_AGE = 90 * 24 * 60 * 60 * 1000; // 90 days in milliseconds
const STORAGE_KEY_PREFIX = "chatMessage";
const DEBOUNCE_TIME = 300; // 300ms

const ChatMessageSchema = z.object({
  value: z.string(),
  timestamp: z.number(),
});

/**
 * Hook for managing chat messages with the following features:
 * 1. Local Storage Management
 *
 * TODO: consolidate the following hooks into this ones:
 * 2. Message Fetching
 * 3. Message Streaming
 * 4. Message Mutations
 */
export const useChatMessages = ({
  workflowId,
  taskId,
  exampleSetId,
  storageKeyPrefix = STORAGE_KEY_PREFIX,
}: UseChatMessagesProps): UseChatMessagesReturn => {
  const localStorageKey = `${storageKeyPrefix}-${workflowId}-${taskId}-${exampleSetId}`;
  const originalMessageKey = `${storageKeyPrefix}-original-${workflowId}-${taskId}-${exampleSetId}`;
  const optimizedContentKey = `${storageKeyPrefix}-optimized-${workflowId}-${taskId}-${exampleSetId}`;

  const messageAtom = useMemo(
    () =>
      atomWithStorage(
        localStorageKey,
        "",
        {
          setItem: debounce((key, value) => {
            LocalStorage.set(key, { value, timestamp: Date.now() });
          }, DEBOUNCE_TIME),
          getItem: (key, initialValue) => {
            const entry = LocalStorage.get(key);
            const parsedEntry = ChatMessageSchema.safeParse(entry);
            if (!parsedEntry.success) {
              console.warn(
                t("components.Builder.Errors.invalidEntryKey", { key }),
                parsedEntry.error,
              );
              return initialValue;
            }
            return parsedEntry.data.value;
          },
          removeItem: () => {
            LocalStorage.remove(localStorageKey);
          },
        },
        { getOnInit: true },
      ),
    [localStorageKey],
  );

  const originalMessageAtom = useMemo(
    () =>
      atomWithStorage(
        originalMessageKey,
        "",
        {
          setItem: debounce((key, value) => {
            LocalStorage.set(key, { value, timestamp: Date.now() });
          }, DEBOUNCE_TIME),
          getItem: (key, initialValue) => {
            const entry = LocalStorage.get(key);
            const parsedEntry = ChatMessageSchema.safeParse(entry);
            if (!parsedEntry.success) {
              return initialValue;
            }
            return parsedEntry.data.value;
          },
          removeItem: () => {
            LocalStorage.remove(originalMessageKey);
          },
        },
        { getOnInit: true },
      ),
    [originalMessageKey],
  );

  const optimizedContentAtom = useMemo(
    () =>
      atomWithStorage(
        optimizedContentKey,
        "",
        {
          setItem: debounce((key, value) => {
            LocalStorage.set(key, { value, timestamp: Date.now() });
          }, DEBOUNCE_TIME),
          getItem: (key, initialValue) => {
            const entry = LocalStorage.get(key);
            const parsedEntry = ChatMessageSchema.safeParse(entry);
            if (!parsedEntry.success) {
              return initialValue;
            }
            return parsedEntry.data.value;
          },
          removeItem: () => {
            LocalStorage.remove(optimizedContentKey);
          },
        },
        { getOnInit: true },
      ),
    [optimizedContentKey],
  );

  const [message, setMessage] = useAtom(messageAtom);
  const [originalMessage, setOriginalMessage] = useAtom(originalMessageAtom);
  const [optimizedContent, setOptimizedContent] = useAtom(optimizedContentAtom);

  /**
   * Cleans up local storage entries based on age
   */
  const cleanupOldEntries = useCallback(
    ({ maxAge }: { maxAge: number }) => {
      const now = Date.now();
      const removedKeys: string[] = [];

      try {
        // Get all keys in local storage
        const chatMessageEntries = LocalStorage.findAllByPrefix(storageKeyPrefix);

        Object.keys(chatMessageEntries)
          .filter((key) => {
            const entry = chatMessageEntries[key];

            const parsedEntry = ChatMessageSchema.safeParse(entry);

            if (!parsedEntry.success) {
              console.warn(
                t("components.Builder.Errors.invalidEntryKey", { key }),
                parsedEntry.error,
              );
              return false; // Skip invalid entries
            }

            const timestamp = parsedEntry.data.timestamp || 0;
            const age = now - timestamp;
            return age > maxAge;
          })
          // Remove entries older than maxAge
          .forEach((key) => {
            LocalStorage.remove(key);
            removedKeys.push(key);
          });
      } catch (error) {
        console.error(t("components.Builder.Errors.errorDuringCleanup"), error);
      }

      return removedKeys;
    },
    [storageKeyPrefix],
  );

  /**
   * Clean up old local storage entries on mount
   *
   * The cleanup time is currently set to 7 days, under the assumption that
   * users will not want to keep draft unsent messages for longer than that.
   */
  useEffect(() => {
    cleanupOldEntries({
      maxAge: MAX_AGE,
    });
  }, [cleanupOldEntries]);

  return useMemo(
    () => ({
      message,
      setMessage,
      originalMessage,
      setOriginalMessage,
      optimizedContent,
      setOptimizedContent,
    }),
    [
      message,
      setMessage,
      originalMessage,
      setOriginalMessage,
      optimizedContent,
      setOptimizedContent,
    ],
  );
};
