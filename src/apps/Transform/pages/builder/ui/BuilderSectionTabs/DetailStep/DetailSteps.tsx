import { useMutation, useQuery } from "@tanstack/react-query";
import { v3, ApiError } from "@/services/v3";
import { queryClient } from "@/components";
import { DetailStepList } from "@/apps/Transform/components/DetailStepsList";

type DetailStepsProps = {
  workflowId: string;
  taskId: string;
  exampleSetId: string;
  isOpen: boolean;
};

export const DetailSteps = ({ workflowId, taskId, exampleSetId, isOpen }: DetailStepsProps) => {
  const createTaskDescriptionMutation = useMutation({
    mutationKey: ["taskDescription", "create", { workflowId, taskId }],
    mutationFn: async ({ exampleSetId }: { exampleSetId: string }) => {
      const response = await v3.taskDescriptions.createTaskDescription({
        workflowId,
        taskId,
        exampleSetId,
        description: { generate: true },
      });
      if (response.errors.length > 0) throw new ApiError(response.errors);
      if (!response.data)
        throw new Error("Unexpected data error: no task description body returned.");
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["taskDescription", workflowId, taskId] });
    },
  });

  const getDescriptionQuery = useQuery({
    queryKey: ["taskDescription", workflowId, taskId, exampleSetId],
    queryFn: async () => {
      const response = await v3.taskDescriptions.getTaskDescriptionByCompositeKey({
        workflowId,
        taskId,
        exampleSetId,
      });

      if (response.errors.length > 0) {
        if (response.errors[0].code === "NOT_FOUND") {
          createTaskDescriptionMutation.mutate({ exampleSetId });
        }
        throw new ApiError(response.errors);
      }

      if (!response.data)
        throw new Error("Unexpected data error: no task description body returned.");

      if (!response.data.id) {
        createTaskDescriptionMutation.mutate({ exampleSetId });
      }

      return response.data;
    },
    enabled: isOpen,
    retry: false,
  });

  return (
    <DetailStepList
      steps={getDescriptionQuery.data?.description ?? []}
      isLoading={
        getDescriptionQuery.isPending ||
        getDescriptionQuery.isFetching ||
        createTaskDescriptionMutation.isPending
      }
    />
  );
};
