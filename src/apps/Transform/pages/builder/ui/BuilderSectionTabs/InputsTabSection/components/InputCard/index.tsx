import { Text } from "@floqastinc/flow-ui_core";
import { DataType } from "@floqastinc/transform-v3";
import { match } from "ts-pattern";
import * as Styled from "./index.styles";
import { ExternalInput } from "@/svg/ExternalInput";
import { FileTable } from "@/svg/FileTable";
import { TextSelectStart } from "@/svg/TextSelectStart";
import { DateEvent } from "@/svg/DateEvent";

type InputCardProps = {
  name: string;
  secondaryText?: string;
  type: DataType | "OUTPUT";
  actions?: () => React.ReactNode;
} & React.HTMLAttributes<HTMLDivElement>;

export const InputCard = ({ name, secondaryText, type, actions, ...rest }: InputCardProps) => {
  return (
    <Styled.InputTab {...rest}>
      <Styled.InputTabLeft isFile={type === "FILE"}>
        <div>
          {match(type)
            /* eslint-disable i18next/no-literal-string */
            .with("FILE", () => <FileTable />)
            .with("TEXT", () => <TextSelectStart />)
            .with("DATETIME", () => <DateEvent />)
            .with("OUTPUT", () => <ExternalInput />)
            /* eslint-enable */
            .otherwise(() => (
              <TextSelectStart />
            ))}
        </div>
        <Styled.InputTabText>
          <Text weight={5} size={4}>
            {name}
          </Text>
          {!secondaryText ? (
            <></>
          ) : (
            <Text weight={4} size={3} color="subtitle">
              {secondaryText}
            </Text>
          )}
        </Styled.InputTabText>
      </Styled.InputTabLeft>
      {actions?.()}
    </Styled.InputTab>
  );
};
