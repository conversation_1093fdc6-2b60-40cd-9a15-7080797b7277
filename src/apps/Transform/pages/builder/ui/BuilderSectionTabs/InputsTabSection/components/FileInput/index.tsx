import { FileUpload } from "@floqastinc/flow-ui_core";
import { useState } from "react";
import { t } from "@/utils/i18n";

type FileInputProps = {
  onChange: (file: File | null) => void;
  allowedFileTypes?: string;
  allowedTypeHelpText: string;
};
export const FileInput = ({ onChange, allowedFileTypes, allowedTypeHelpText }: FileInputProps) => {
  const [file, setFile] = useState<File | null>(null);

  return (
    <div style={{ width: "100%" }}>
      <FileUpload
        multiple={false}
        onChange={(files: File[]) => {
          setFile(files[0]);
          onChange(files[0]);
        }}
        allowedFileTypes={allowedFileTypes}
      >
        {!file && (
          <FileUpload.DropZone>
            <FileUpload.UploadButton>
              {t("components.FileInput.uploadFiles")}
            </FileUpload.UploadButton>
            <FileUpload.HelperText>{t("components.FileInput.dropToUpload")}</FileUpload.HelperText>
            <FileUpload.HelperText>{allowedTypeHelpText}</FileUpload.HelperText>
          </FileUpload.DropZone>
        )}
        <FileUpload.FileList>
          {file ? (
            <FileUpload.File
              file={file}
              onDelete={() => {
                setFile(null);
                onChange(null);
              }}
            />
          ) : null}
        </FileUpload.FileList>
      </FileUpload>
    </div>
  );
};
