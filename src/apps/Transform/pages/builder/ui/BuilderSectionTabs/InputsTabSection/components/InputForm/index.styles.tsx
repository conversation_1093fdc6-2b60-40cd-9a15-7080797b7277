import { styled } from "styled-components";
import { Card } from "@floqastinc/flow-ui_core";

export const CardContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: start;
  gap: 16px;
  width: 100%;
`;

export const Header = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`;

export const HeaderActions = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`;

export const Body = styled.div`
  display: flex;
  flex-direction: column;
  align-items: start;
  gap: 12px;
  width: 100%;
`;

export const InputFormatButton = styled(Card)`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 4px;
  padding: 6px 8px;
  border-radius: 6px;
  border: 1px solid var(--flo-sem-color-border);

  &.disabled {
    opacity: var(--flo-sem-opacity-disabled);
  }
`;

export const ButtonText = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: var(--flo-base-font-family-1);
  font-size: var(--flo-base-font-size-3);
`;
