import { styled } from "styled-components";

export const InputTab = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  width: 100%;
  min-height: 58px;
  padding: 16px 16px 16px 12px;
  border: 1px solid var(--flo-base-color-neutral-300);
  border-radius: 6px;
`;

export const InputTabLeft = styled.div<{ isFile: boolean }>`
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: ${({ isFile }) => (isFile ? "center" : "top")};
  justify-content: start;
  max-width: 60%;
`;
export const InputTabText = styled.div`
  display: flex;
  flex-direction: column;
  max-width: 100%;
`;
