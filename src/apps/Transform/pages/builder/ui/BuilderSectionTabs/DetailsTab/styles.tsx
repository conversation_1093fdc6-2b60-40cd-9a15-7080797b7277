import { styled } from "styled-components";
import { Tooltip } from "@floqastinc/flow-ui_core";

export const DetailsTab = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

export const DetailsTabHeader = styled.div`
  align-items: center;
  background-color: var(--flo-sem-color-surface-neutral-weakest);
  display: flex;
  gap: 8px;
  justify-content: end;
  padding: 8px 16px;
  position: sticky;
  top: 0;
  width: 100%;
`;

export const DetailsTabBody = styled.div`
  padding: 0 12px 12px 12px;
`;

export const EmptyStateWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  height: 100%;
  margin-top: 25%;
`;

export const TooltipContent = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
`;

export const StyledTooltipContent = styled(Tooltip.Content)`
  padding: 4px 6px;
`;
