import { describe, test, expect, vi, beforeEach, afterEach } from "vitest";
import { userEvent } from "@vitest/browser/context";
import {
  BuildStatus,
  Task,
  TaskDescription,
  TaskDescriptionDescription,
  TaskStrategy,
} from "@floqastinc/transform-v3";
import { DetailsTab } from ".";
import { customRender } from "@/utils/testing";
import { t } from "@/utils/i18n";

import { useTask } from "@v3/tasks";
import { useExampleOutputs } from "@v3/example-outputs";
import { useExampleInputs } from "@v3/example-inputs";
import * as v3 from "@/services/v3";

vi.mock("@v3/tasks");
vi.mock("@v3/example-outputs");
vi.mock("@v3/example-inputs");
vi.mock("@/services/v3");
vi.mock("@/utils/i18n");

// Mock useToast hook
const mockUseToast = vi.fn(() => ({
  showToast: vi.fn(),
  Toast: (props: { children: React.ReactNode }) => <div>{props.children}</div>,
}));

vi.mock("@floqastinc/flow-ui_core", async (importOriginal) => {
  const original = await importOriginal<typeof import("@floqastinc/flow-ui_core")>();
  return {
    ...original,
    useToast: mockUseToast,
  };
});

vi.mock("@/components/Loading", () => ({
  Loading: () => <div>Loading...</div>,
}));
vi.mock("../DetailStep/DetailSteps", () => ({
  DetailSteps: () => <div>DetailSteps Component</div>,
}));
vi.mock("@BuilderV3/routes/workflows/components/DetailsSlideout/DetailsCard", () => ({
  __esModule: true,
  default: ({
    onChange,
    taskDescription,
  }: {
    onChange: (value: TaskDescriptionDescription[]) => void;
    taskDescription: TaskDescriptionDescription[];
  }) => (
    <div>
      <span>DetailsCard Component</span>
      <p>{JSON.stringify(taskDescription)}</p>
      <button onClick={() => onChange([{ heading: "new heading", details: "new details" }])}>
        Change Description
      </button>
    </div>
  ),
}));

const mockedUseTask = vi.mocked(useTask);
const mockedUseExampleOutputs = vi.mocked(useExampleOutputs);
const mockedUseExampleInputs = vi.mocked(useExampleInputs);
const mockedV3 = vi.mocked(v3);
const mockedT = vi.mocked(t);

const defaultProps = {
  workflowId: "wf-1",
  taskId: "task-1",
  exampleSetId: "ex-1",
};

const mockTaskDescription: TaskDescription = {
  id: "desc-1",
  workflowId: "wf-1",
  taskId: "task-1",
  exampleSetId: "ex-1",
  description: [{ heading: "This is a heading", details: "This is a detail" }],
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockTask = (strategy: TaskStrategy): Task => ({
  id: "task-1",
  workflowId: "wf-1",
  name: "Test Task",
  status: "ACTIVE",
  strategy,
  createdAt: new Date(),
  updatedAt: new Date(),
});

const llmScriptStrategy: TaskStrategy = {
  kind: "LLM_SCRIPT",
  buildStatus: "SUCCEEDED" as BuildStatus,
  buildErrors: [],
  conversationStatus: "READY",
  script: "test script",
};

const reviewStrategy: TaskStrategy = {
  kind: "REVIEW",
};

const jemExportStrategy: TaskStrategy = {
  kind: "JEM_EXPORT",
};

describe("DetailsTab", () => {
  beforeEach(() => {
    mockedT.mockImplementation((key) => key);
    (mockedV3.v3.taskDescriptions.getTaskDescriptionByCompositeKey as any) = vi
      .fn()
      .mockResolvedValue({
        data: mockTaskDescription,
        errors: [],
      });
    (mockedV3.v3.taskDescriptions.createTaskDescription as any) = vi.fn().mockResolvedValue({
      data: mockTaskDescription,
      errors: [],
    });
    (mockedV3.v3.taskDescriptions.updateTaskDescription as any) = vi.fn().mockResolvedValue({
      data: mockTaskDescription,
      errors: [],
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  test("GIVEN data is loading THEN it should display the loading indicator", () => {
    mockedUseTask.mockReturnValue({ data: undefined } as any);
    mockedUseExampleOutputs.mockReturnValue({ isLoading: true } as any);
    const screen = customRender(<DetailsTab {...defaultProps} />);
    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  test("GIVEN cannot produce details THEN it should display the empty state", () => {
    mockedUseTask.mockReturnValue({ data: mockTask(jemExportStrategy) } as any);
    mockedUseExampleInputs.mockReturnValue({ data: [], isLoading: false } as any);
    mockedUseExampleOutputs.mockReturnValue({ data: [], isLoading: false } as any);

    const screen = customRender(<DetailsTab {...defaultProps} />);

    expect(screen.getByText("components.DetailSteps.noSummaryToPreview")).toBeInTheDocument();
  });

  test("GIVEN data is loaded THEN it should display the view mode by default", async () => {
    mockedUseTask.mockReturnValue({ data: mockTask(llmScriptStrategy) } as any);
    mockedUseExampleInputs.mockReturnValue({ data: [], isLoading: false } as any);
    mockedUseExampleOutputs.mockReturnValue({
      data: [{ value: "output" }],
      isLoading: false,
    } as any);

    const screen = customRender(<DetailsTab {...defaultProps} />);

    await expect(screen.findByText("DetailSteps Component")).resolves.toBeInTheDocument();
    await expect(screen.findByText("components.DetailsTab.edit")).resolves.toBeInTheDocument();
  });

  test("WHEN user clicks Edit button THEN it should switch to edit mode", async () => {
    mockedUseTask.mockReturnValue({ data: mockTask(llmScriptStrategy) } as any);
    mockedUseExampleInputs.mockReturnValue({ data: [], isLoading: false } as any);
    mockedUseExampleOutputs.mockReturnValue({
      data: [{ value: "output" }],
      isLoading: false,
    } as any);

    const screen = customRender(<DetailsTab {...defaultProps} />);

    const editButton = await screen.findByText("components.DetailsTab.edit");
    await userEvent.click(editButton);

    await expect(screen.findByText("DetailsCard Component")).resolves.toBeInTheDocument();
    await expect(screen.findByText("components.DetailsTab.save")).resolves.toBeInTheDocument();
    await expect(screen.findByText("components.DetailsTab.cancel")).resolves.toBeInTheDocument();
  });

  test("WHEN in edit mode AND user clicks Cancel THEN it should revert and return to view mode", async () => {
    mockedUseTask.mockReturnValue({ data: mockTask(llmScriptStrategy) } as any);
    mockedUseExampleInputs.mockReturnValue({ data: [], isLoading: false } as any);
    mockedUseExampleOutputs.mockReturnValue({
      data: [{ value: "output" }],
      isLoading: false,
    } as any);

    const screen = customRender(<DetailsTab {...defaultProps} />);
    const editButton = await screen.findByText("components.DetailsTab.edit");
    await userEvent.click(editButton);

    await expect(screen.findByText("DetailsCard Component")).resolves.toBeInTheDocument();

    await userEvent.click(screen.getByText("Change Description"));

    await userEvent.click(screen.getByText("components.DetailsTab.cancel"));

    await expect(screen.findByText("DetailSteps Component")).resolves.toBeInTheDocument();
    expect(mockedV3.v3.taskDescriptions.updateTaskDescription).not.toHaveBeenCalled();
  });

  test("WHEN in edit mode AND user makes a change and clicks Save THEN it should save the changes", async () => {
    mockedUseTask.mockReturnValue({ data: mockTask(llmScriptStrategy) } as any);
    mockedUseExampleInputs.mockReturnValue({ data: [], isLoading: false } as any);
    mockedUseExampleOutputs.mockReturnValue({
      data: [{ value: "output" }],
      isLoading: false,
    } as any);

    const screen = customRender(<DetailsTab {...defaultProps} />);
    const editButton = await screen.findByText("components.DetailsTab.edit");
    await userEvent.click(editButton);

    const saveButton = await screen.findByText<HTMLButtonElement>("components.DetailsTab.save");
    expect(saveButton.disabled).toBe(true);

    await userEvent.click(screen.getByText("Change Description"));

    expect(saveButton.disabled).toBe(false);

    await userEvent.click(saveButton);

    expect(mockedV3.v3.taskDescriptions.updateTaskDescription).toHaveBeenCalledWith({
      taskDescriptionId: mockTaskDescription.id,
      workflowId: defaultProps.workflowId,
      taskId: defaultProps.taskId,
      exampleSetId: defaultProps.exampleSetId,
      description: {
        description: [{ heading: "new heading", details: "new details" }],
        generate: false,
      },
    });
  });

  test("WHEN user clicks Regenerate button THEN it should trigger regeneration", async () => {
    mockedUseTask.mockReturnValue({ data: mockTask(llmScriptStrategy) } as any);
    mockedUseExampleInputs.mockReturnValue({ data: [], isLoading: false } as any);
    mockedUseExampleOutputs.mockReturnValue({
      data: [{ value: "output" }],
      isLoading: false,
    } as any);

    const screen = customRender(<DetailsTab {...defaultProps} />);
    const regenerateButton = await screen.findByText("components.DetailsTab.regenerate");

    await userEvent.click(regenerateButton);

    expect(mockedV3.v3.taskDescriptions.updateTaskDescription).toHaveBeenCalledWith(
      expect.objectContaining({
        description: expect.objectContaining({
          generate: true,
        }),
      }),
    );
  });

  describe("canProduceDetails logic", () => {
    describe("WHEN task strategy is LLM_SCRIPT", () => {
      beforeEach(() => {
        mockedUseTask.mockReturnValue({ data: mockTask(llmScriptStrategy) } as any);
        mockedUseExampleInputs.mockReturnValue({ data: [], isLoading: false } as any);
      });
      test("GIVEN there are outputs THEN canProduceDetails should be true", async () => {
        mockedUseExampleOutputs.mockReturnValue({
          data: [{ value: "output" }],
          isLoading: false,
        } as any);
        const screen = customRender(<DetailsTab {...defaultProps} />);
        await expect(
          screen.findByText("components.DetailSteps.noSummaryToPreview"),
        ).resolves.toBeNull();
      });
      test("GIVEN there are no outputs THEN canProduceDetails should be false", () => {
        mockedUseExampleOutputs.mockReturnValue({ data: [], isLoading: false } as any);
        const screen = customRender(<DetailsTab {...defaultProps} />);
        expect(screen.getByText("components.DetailSteps.noSummaryToPreview")).toBeInTheDocument();
      });
    });

    describe("WHEN task strategy is REVIEW", () => {
      beforeEach(() => {
        mockedUseTask.mockReturnValue({ data: mockTask(reviewStrategy) } as any);
        mockedUseExampleOutputs.mockReturnValue({ data: [], isLoading: false } as any);
      });
      test("GIVEN there are inputs THEN canProduceDetails should be true", async () => {
        mockedUseExampleInputs.mockReturnValue({
          data: [{ value: "input" }],
          isLoading: false,
        } as any);
        const screen = customRender(<DetailsTab {...defaultProps} />);
        await expect(
          screen.findByText("components.DetailSteps.noSummaryToPreview"),
        ).resolves.toBeNull();
      });
      test("GIVEN there are no inputs THEN canProduceDetails should be false", () => {
        mockedUseExampleInputs.mockReturnValue({ data: [], isLoading: false } as any);
        const screen = customRender(<DetailsTab {...defaultProps} />);
        expect(screen.getByText("components.DetailSteps.noSummaryToPreview")).toBeInTheDocument();
      });
    });
  });

  test("GIVEN no description exists WHEN component mounts THEN it should create a new description", async () => {
    mockedUseTask.mockReturnValue({ data: mockTask(llmScriptStrategy) } as any);
    mockedUseExampleOutputs.mockReturnValue({
      data: [{ value: "output" }],
      isLoading: false,
    } as any);
    (mockedV3.v3.taskDescriptions.getTaskDescriptionByCompositeKey as any).mockResolvedValue({
      data: null,
      errors: [],
    });

    customRender(<DetailsTab {...defaultProps} />);

    await expect(mockedV3.v3.taskDescriptions.createTaskDescription).resolves.toHaveBeenCalledWith({
      workflowId: defaultProps.workflowId,
      taskId: defaultProps.taskId,
      exampleSetId: defaultProps.exampleSetId,
      description: {
        generate: true,
      },
    });
  });

  describe("Error handling", () => {
    test("GIVEN API error when fetching description THEN it should show error toast", async () => {
      const mockShowToast = vi.fn();
      vi.mocked(useToast).mockReturnValue({
        showToast: mockShowToast,
        Toast: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
      } as any);

      mockedUseTask.mockReturnValue({ data: mockTask(llmScriptStrategy) } as any);
      mockedUseExampleOutputs.mockReturnValue({
        data: [{ value: "output" }],
        isLoading: false,
      } as any);

      (mockedV3.v3.taskDescriptions.getTaskDescriptionByCompositeKey as any).mockResolvedValue({
        data: null,
        errors: [{ detail: "API Error" }],
      });

      customRender(<DetailsTab {...defaultProps} />);

      await vi.waitFor(() => {
        expect(mockShowToast).toHaveBeenCalled();
      });
    });

    test("GIVEN API error when updating description THEN it should show error toast", async () => {
      const mockShowToast = vi.fn();
      vi.mocked(useToast).mockReturnValue({
        showToast: mockShowToast,
        Toast: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
      } as any);

      mockedUseTask.mockReturnValue({ data: mockTask(llmScriptStrategy) } as any);
      mockedUseExampleOutputs.mockReturnValue({
        data: [{ value: "output" }],
        isLoading: false,
      } as any);

      (mockedV3.v3.taskDescriptions.updateTaskDescription as any).mockResolvedValue({
        data: null,
        errors: [{ detail: "Update failed" }],
      });

      const screen = customRender(<DetailsTab {...defaultProps} />);

      // Switch to edit mode
      const editButton = await screen.findByText("components.DetailsTab.edit");
      await userEvent.click(editButton);

      // Make a change
      await userEvent.click(screen.getByText("Change Description"));

      // Save changes
      const saveButton = await screen.findByText("components.DetailsTab.save");
      await userEvent.click(saveButton);

      await vi.waitFor(() => {
        expect(mockShowToast).toHaveBeenCalled();
      });
    });
  });

  describe("Loading states", () => {
    test("GIVEN example inputs are loading THEN it should show loading indicator", () => {
      mockedUseTask.mockReturnValue({ data: mockTask(llmScriptStrategy) } as any);
      mockedUseExampleInputs.mockReturnValue({ data: undefined, isLoading: true } as any);
      mockedUseExampleOutputs.mockReturnValue({ data: [], isLoading: false } as any);

      const screen = customRender(<DetailsTab {...defaultProps} />);
      expect(screen.getByText("Loading...")).toBeInTheDocument();
    });

    test("GIVEN task data is undefined THEN it should show loading indicator", () => {
      mockedUseTask.mockReturnValue({ data: undefined } as any);
      mockedUseExampleInputs.mockReturnValue({ data: [], isLoading: false } as any);
      mockedUseExampleOutputs.mockReturnValue({ data: [], isLoading: false } as any);

      const screen = customRender(<DetailsTab {...defaultProps} />);
      expect(screen.getByText("Loading...")).toBeInTheDocument();
    });
  });

  describe("Strategy-specific behavior", () => {
    test("GIVEN FLOLAKE strategy THEN canProduceDetails should be false", () => {
      const flolakeStrategy: TaskStrategy = { kind: "FLOLAKE" };
      mockedUseTask.mockReturnValue({ data: mockTask(flolakeStrategy) } as any);
      mockedUseExampleInputs.mockReturnValue({ data: [], isLoading: false } as any);
      mockedUseExampleOutputs.mockReturnValue({ data: [], isLoading: false } as any);

      const screen = customRender(<DetailsTab {...defaultProps} />);
      expect(screen.getByText("components.DetailSteps.noSummaryToPreview")).toBeInTheDocument();
    });

    test("GIVEN SCRIPT strategy with outputs THEN canProduceDetails should be true", async () => {
      const scriptStrategy: TaskStrategy = { kind: "SCRIPT" };
      mockedUseTask.mockReturnValue({ data: mockTask(scriptStrategy) } as any);
      mockedUseExampleInputs.mockReturnValue({ data: [], isLoading: false } as any);
      mockedUseExampleOutputs.mockReturnValue({
        data: [{ value: "output" }],
        isLoading: false,
      } as any);

      const screen = customRender(<DetailsTab {...defaultProps} />);
      await expect(screen.findByText("DetailSteps Component")).resolves.toBeInTheDocument();
    });

    test("GIVEN LLM_SCRIPT strategy with chat conversation status THEN regenerate button should be visible", async () => {
      const chatStrategy: TaskStrategy = {
        kind: "LLM_SCRIPT",
        buildStatus: "SUCCEEDED" as BuildStatus,
        buildErrors: [],
        conversationStatus: "READY",
        script: "test script",
      };
      mockedUseTask.mockReturnValue({ data: mockTask(chatStrategy) } as any);
      mockedUseExampleInputs.mockReturnValue({ data: [], isLoading: false } as any);
      mockedUseExampleOutputs.mockReturnValue({
        data: [{ value: "output" }],
        isLoading: false,
      } as any);

      const screen = customRender(<DetailsTab {...defaultProps} />);
      await expect(
        screen.findByText("components.DetailsTab.regenerate"),
      ).resolves.toBeInTheDocument();
    });
  });
});
