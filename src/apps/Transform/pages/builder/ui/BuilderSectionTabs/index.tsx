import { TabGroup, Tab, Spinner } from "@floqastinc/flow-ui_core";
import { useParams } from "react-router-dom";
import { match, P } from "ts-pattern";
import { TaskStrategy } from "@floqastinc/transform-v3";
import { t } from "@/utils/i18n";
import { useExampleInputs } from "@v3/example-inputs";

export type BuilderSectionTab = "build" | "inputs" | "versions" | "summary";

type BuilderSectionTabsProps = {
  value: BuilderSectionTab;
  onValueChange: (value: BuilderSectionTab) => void;
  strategy: TaskStrategy["kind"] | undefined;
};
export const BuilderSectionTabs = ({ value, onValueChange, strategy }: BuilderSectionTabsProps) => {
  const { workflowId = "", taskId = "", exampleSetId = "" } = useParams();

  const { data: inputs, isPending } = useExampleInputs({
    workflowId,
    taskId,
    exampleSetId,
  });

  const inputsTab = match({ isPending, inputs })
    .with({ isPending: true }, () => (
      <Tab
        tabId="inputs"
        title={
          <button type="button">
            {t("components.BuilderSectionTabs.inputs")} <Spinner color="info" size={12} />
          </button>
        }
      />
    ))
    .with({ inputs: [] }, () => (
      <Tab tabId="inputs" title={t("components.BuilderSectionTabs.inputs")} />
    ))
    .with({ inputs: P.nullish }, () => (
      <Tab tabId="inputs" title={t("components.BuilderSectionTabs.inputs")} />
    ))
    .otherwise(() => (
      <Tab
        tabId="inputs"
        title={`${t("components.BuilderSectionTabs.inputs")} (${inputs?.length || 0})`}
      />
    ));

  const tabs = match(strategy)
    .with("REVIEW", () => [
      <Tab tabId="summary" title={t("components.BuilderSectionTabs.summary")} key="summary" />,
    ])
    .with("PARSE_PDF", () => [
      <Tab tabId="build" title={t("components.BuilderSectionTabs.build")} key="build" />,
      inputsTab,
      <Tab tabId="versions" title={t("components.BuilderSectionTabs.versions")} key="versions" />,
    ])
    .otherwise(() => [
      <Tab tabId="build" title={t("components.BuilderSectionTabs.build")} key="build" />,
      inputsTab,
      <Tab tabId="versions" title={t("components.BuilderSectionTabs.versions")} key="versions" />,
      <Tab tabId="summary" title={t("components.BuilderSectionTabs.summary")} key="summary" />,
    ]);

  return (
    <TabGroup defaultValue="build" value={value} onValueChange={onValueChange}>
      {tabs}
    </TabGroup>
  );
};
