import { Text } from "@floqastinc/flow-ui_core";
import { ExampleSet } from "@floqastinc/transform-v3";
import { useQuery } from "@tanstack/react-query";
import { useParams } from "react-router";
import * as Styled from "./styles";
import { VersionsCard } from "./VersionCard";
import { t } from "@/utils/i18n";
import { getExamplesQuery } from "@BuilderV3/api/examples";

export const VersionsTab = () => {
  const { workflowId = "", taskId = "" } = useParams();
  const versionsQuery = useQuery({
    ...getExamplesQuery({
      workflowId,
      taskId,
    }),
    enabled: !!taskId,
    select: (data) => {
      const sortedData = data.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
      const examples = sortedData.reduce(
        (accExamples, curExample) => {
          if (curExample.status === "ACTIVE" || curExample.status === "PUBLISHED") {
            accExamples.activeAndPublishedExamples.push({
              ...curExample,
              name:
                curExample.name ??
                `Version ${curExample.createdAt.toLocaleString("en-US", {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                  hour: "numeric",
                  minute: "2-digit",
                  second: "2-digit",
                })}`,
            });
          } else if (curExample.status === "DRAFT") {
            accExamples.draftExamples.push(curExample);
          }

          accExamples.allExamples.push(curExample);

          return accExamples;
        },
        {
          activeAndPublishedExamples: [] as (ExampleSet & { name: string })[],
          draftExamples: [] as (ExampleSet & { name: string })[],
          allExamples: [] as ExampleSet[],
        },
      );

      return examples;
    },
    throwOnError: true,
  });
  const {
    data: versions = {
      activeAndPublishedExamples: [],
      draftExamples: [],
      allExamples: [],
    },
  } = versionsQuery;
  const { activeAndPublishedExamples, draftExamples } = versions;
  const activeExample = activeAndPublishedExamples.filter((example) => example.status === "ACTIVE");
  const publishedExamples = activeAndPublishedExamples.filter(
    (example) => example.status === "PUBLISHED",
  );

  return (
    <Styled.VersionsTabContainer>
      {activeExample.length ? (
        <Styled.StatusContainer data-testid="active-example-container">
          <Text weight={5}>{t("components.VersionsTab.active")}</Text>
          {activeExample.map((example) => (
            <VersionsCard key={example.id} example={example} status="active" />
          ))}
        </Styled.StatusContainer>
      ) : null}

      {publishedExamples.length ? (
        <Styled.StatusContainer data-testid="saved-example-container">
          <Text weight={5}>{t("components.VersionsTab.versionsTabSaved")}</Text>
          {publishedExamples.map((example) => (
            <VersionsCard key={example.id} example={example} status="saved" />
          ))}
        </Styled.StatusContainer>
      ) : null}

      {draftExamples.length ? (
        <Styled.StatusContainer data-testid="draft-example-container">
          <Text weight={5}>{t("components.VersionsTab.draft")}</Text>
          {draftExamples.map((example) => (
            <VersionsCard key={example.id} example={example} status="draft" />
          ))}
        </Styled.StatusContainer>
      ) : null}
    </Styled.VersionsTabContainer>
  );
};
