import { useEffect, useState } from "react";
import {
  <PERSON>ton,
  IconButton,
  Flex,
  Text,
  TableStatusBadge,
  DropdownPanel,
  Dialog,
  Input,
} from "@floqastinc/flow-ui_core";
import MoreVert from "@floqastinc/flow-ui_icons/material/MoreVert";
import { useMutation } from "@tanstack/react-query";
import { useNavigate, useParams } from "react-router-dom";
import { ExampleSet, ExampleSetStatus, ExampleSetUpdate } from "@floqastinc/transform-v3";
import { StatusChangeDialog } from "../../Chat/components/StatusChange/StatusChange";
import * as Styled from "./styles";
import { t } from "@/utils/i18n";
import { queryKeys } from "@BuilderV3/api/query-keys";
import { v3 } from "@/services/v3";
import { queryClient } from "@/components";
import { AGENTS, BUILDER, EXAMPLES, STEPS, V3 } from "@/constants";
import { useTask } from "@v3/tasks";

const STATUS_COLOR_MAP: Record<string, "success" | "danger" | "info"> = {
  COMPILED: "success",
  FAILED: "danger",
  BUSY: "info",
} as const;

const getActiveColor = (buildStatus: string | undefined) => {
  return buildStatus ? (STATUS_COLOR_MAP[buildStatus] ?? "success") : "success";
};

type VersionCardProps = {
  example: ExampleSet;
  status: "active" | "saved" | "draft" | "archived";
};

export const VersionsCard = ({ example, status }: VersionCardProps) => {
  const { exampleSetId = "" } = useParams();
  const navigate = useNavigate();
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [newVersionName, setNewVersionName] = useState("");
  const [statusActiveVersion, setStatusActiveVersion] = useState<string | undefined>(undefined);
  const [statusChange, setStatusChange] = useState<{
    fromStatus: ExampleSetStatus | null;
    toStatus: ExampleSetStatus | null;
    exampleSetId: string | null;
  }>({
    fromStatus: null,
    toStatus: null,
    exampleSetId: null,
  });
  const isStatusChangeDialogOpen = Boolean(
    statusChange.fromStatus && statusChange.toStatus && statusChange.exampleSetId,
  );
  const [currentlyOpenStatusDropdown, setCurrentlyOpenStatusDropdown] = useState<string | null>(
    null,
  );
  const { workflowId, taskId } = example;

  const taskQuery = useTask(
    { workflowId: workflowId, taskId: taskId },
    {
      enabled: !!workflowId && !!taskId,
    },
  );

  useEffect(() => {
    if (taskQuery.data?.strategy?.kind === "SCRIPT" && status === "active") {
      setStatusActiveVersion(taskQuery.data?.strategy?.buildStatus);
    } else {
      setStatusActiveVersion(undefined);
    }
  }, [taskQuery.data, status]);

  const updateVersionMutation = useMutation({
    mutationFn: (updatedExample: ExampleSetUpdate) =>
      v3.examples.updateExample({
        workflowId,
        taskId,
        exampleSetId: example.id,
        example: updatedExample,
      }),
    onSuccess: async () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.examples.getExample({
          workflowId,
          taskId,
          exampleSetId: example.id,
        }),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.examples.getExamples({ workflowId, taskId }),
      });
      setIsRenameDialogOpen(false);
    },
  });

  if (!workflowId || !taskId) {
    console.error(t("components.VersionCard.Errors.noIDsprovided"));
    throw new Error(t("components.VersionCard.Errors.noIDsprovided"));
  }

  return (
    <Styled.VersionsCard data-testid={example.id} key={example.id}>
      <Dialog type="info" open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
        <Dialog.Header>{t("components.VersionCard.renameVersion")}</Dialog.Header>
        <Dialog.Body>
          <Input
            label={t("components.VersionCard.newVersionName")}
            placeholder={example.name}
            value={newVersionName}
            onChange={setNewVersionName}
          />
        </Dialog.Body>
        <Dialog.Footer>
          <Dialog.FooterCancelBtn
            onClick={() => setIsRenameDialogOpen(false)}
            data-tracking-id="builder-cancel-rename-version-button"
          >
            {t("components.VersionCard.cancel")}
          </Dialog.FooterCancelBtn>
          <Dialog.FooterActionBtn
            disabled={!newVersionName || updateVersionMutation.isPending}
            onClick={() => {
              updateVersionMutation.mutate({
                name: newVersionName,
              });
            }}
            data-tracking-id="builder-confirm-rename-version-button"
          >
            {t("components.VersionCard.confirm")}
          </Dialog.FooterActionBtn>
        </Dialog.Footer>
      </Dialog>
      <StatusChangeDialog
        isStatusChangeDialogOpen={isStatusChangeDialogOpen}
        statusChange={statusChange}
        setStatusChange={setStatusChange}
        workflowId={workflowId}
        taskId={taskId}
        makeActive={statusChange.fromStatus === "PUBLISHED" && statusChange.toStatus === "ACTIVE"}
      />
      <Styled.VersionsCardContent>
        <Flex align="center">
          <Text weight={5} size={4}>
            {example.name}
          </Text>
        </Flex>
        {status === "active" ? (
          <Flex gap={0} style={{ alignItems: "center", height: "20px", gap: "5px" }}>
            <Button
              variant="outlined"
              color="dark"
              size="sm"
              style={{ padding: "5px 5px 5px 5px", width: "38px" }}
              disabled={example.id === exampleSetId}
              onClick={() => {
                navigate(
                  `/${BUILDER}/${V3}/${AGENTS}/${workflowId}/${STEPS}/${taskId}/${EXAMPLES}/${example.id}`,
                );
              }}
              data-testid="builder-view-example-button"
            >
              {t("components.VersionCard.view")}
            </Button>
            <TableStatusBadge color={getActiveColor(statusActiveVersion)} hasIcon={false}>
              {t("components.VersionCard.active")}
            </TableStatusBadge>
          </Flex>
        ) : (
          <Flex gap={0} style={{ alignItems: "center", height: "20px" }}>
            <Button
              variant="outlined"
              color="dark"
              size="sm"
              style={{ padding: "5px 5px 5px 5px", width: "38px" }}
              disabled={example.id === exampleSetId}
              onClick={() => {
                navigate(
                  `/${BUILDER}/${V3}/${AGENTS}/${workflowId}/${STEPS}/${taskId}/${EXAMPLES}/${example.id}`,
                );
              }}
              data-testid="builder-view-example-button"
            >
              {t("components.VersionCard.view")}
            </Button>
            <DropdownPanel
              disableFilter
              disableClear
              isOpen={currentlyOpenStatusDropdown === example.id}
              onOpenChange={(isOpen: boolean) => {
                setCurrentlyOpenStatusDropdown(isOpen ? example.id : null);
              }}
              onChange={(value: string) => {
                if (value === "delete") {
                  setStatusChange({
                    fromStatus: example.status,
                    toStatus: "ARCHIVED",
                    exampleSetId: example.id,
                  });
                  setCurrentlyOpenStatusDropdown(null);
                } else if (value === "rename") {
                  setIsRenameDialogOpen(true);
                } else if (value === "ACTIVE") {
                  setStatusChange({
                    fromStatus: example.status,
                    toStatus: value,
                    exampleSetId: example.id,
                  });
                  setCurrentlyOpenStatusDropdown(null);
                }
              }}
            >
              <DropdownPanel.Trigger>
                <IconButton variant="ghost" color="dark">
                  <MoreVert />
                </IconButton>
              </DropdownPanel.Trigger>
              <DropdownPanel.Content size="sm">
                <DropdownPanel.Option
                  value="ACTIVE"
                  key="edit-workflow"
                  isDisabled={example.status !== "PUBLISHED"}
                  data-testid="builder-set-as-active-option"
                >
                  <div>{t("components.VersionCard.setActive")}</div>
                </DropdownPanel.Option>
                <DropdownPanel.Option
                  value="rename"
                  key="edit-workflow"
                  data-testid="builder-edit-title-option"
                >
                  <div>{t("components.VersionCard.editTitle")}</div>
                </DropdownPanel.Option>
                <DropdownPanel.Option
                  value="delete"
                  key="delete-example"
                  data-testid="builder-set-as-archive-option"
                >
                  <div>{t("components.VersionCard.delete")}</div>
                </DropdownPanel.Option>
              </DropdownPanel.Content>
            </DropdownPanel>
          </Flex>
        )}
      </Styled.VersionsCardContent>
    </Styled.VersionsCard>
  );
};
