import styled from "styled-components";
import { Card } from "@floqastinc/flow-ui_core";

export const VersionsTabContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px;
`;

export const VersionsTab = styled.div`
  width: 100%;
  padding: 16px 16px 16px 12px;
  border: 1px solid var(--flo-base-color-neutral-300);
  border-radius: 6px;
  min-height: 58px;
`;

export const VersionsTabContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
`;

export const StatusContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

export const VersionsCard = styled(Card)`
  width: 100%;
  padding: 12px 8px 12px 12px;
  height: 50px;
`;
export const VersionsCardContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`;
