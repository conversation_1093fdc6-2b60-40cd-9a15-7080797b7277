import { describe, test } from "vitest";

describe("BuilderSectionActionBar", () => {
  test.todo("Cancel/Save/Edit buttons disabled if any action is pending");
  test.todo("Cancel/Save/Edit show progress spinner is their own action is pending");
  test.todo("Cancel _doesnt_ appear if there is only one example set");
  test.todo("Save button is disabled if the example set is not publishable");
  test.todo("If example set is not publishable, hovering the Save button shows a helpful tooltip");
  test.todo("If the example set is in the DRAFT status, the Cancel and Save buttons appear");
  test.todo("If the example set is in the PUBLISHED or ACTIVE statuses, the Edit button appears");
  test.todo(
    "Clicking on the MoreVert icon opens a dropdown with the option to delete the example set",
  );
  test.todo("Clicking on the Delete Step option deletes the entire step");
});
