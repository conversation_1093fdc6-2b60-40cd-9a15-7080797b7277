import { describe, expect, test, vi } from "vitest";
import { WorkflowInput } from "@floqastinc/transform-v3";
import { useParams } from "react-router-dom";
import { userEvent } from "@vitest/browser/context";
import { FQIntl } from "@floqastinc/fq-intl";
import GlobalInputs from ".";
import { useWorkflowInputExampleUri, useWorkflowInputs } from "@v3/workflow-inputs";
import { customRender } from "@/utils/testing";
import { useCreateWorkflowInputAndSetValue } from "@/hooks/useCreateWorkflowInputAndSetValue";
import { useFeatureFlags } from "@/components/FeatureFlag";
import { useUpdateWorkflowInputAndSetValue } from "@/hooks/useUpdateWorkflowInputAndSetValue";

vi.mock("@v3/workflow-inputs");
vi.mock("@v3");
vi.mock("react-router-dom");
vi.mock("@/components/FeatureFlag");
vi.mock("@/hooks/useCreateWorkflowInputAndSetValue");
vi.mock("@/hooks/useUpdateWorkflowInputAndSetValue");
vi.mock("i18next", () => ({
  t: (key: string) => key,
}));
vi.mock("@floqastinc/fq-intl", () => ({
  FQIntl: {
    DateTimeFormat: vi.fn(),
  },
}));
const mockedDateTimeFormat = vi.mocked(FQIntl.DateTimeFormat);

const mockDate = (expectedFormat: string) => {
  const mockInstance = {
    format: vi.fn().mockImplementation(() => expectedFormat),
    formatToParts: vi.fn(),
    resolvedOptions: vi.fn(),
    resolvedIsoOptions: vi.fn(),
    formatRange: vi.fn(),
    formatRangeToParts: vi.fn(),
  } as unknown as InstanceType<typeof FQIntl.DateTimeFormat>;
  mockedDateTimeFormat.mockImplementation(() => mockInstance);
};

vi.mocked(useFeatureFlags).mockReturnValue({
  getFlag: vi.fn((args) => {
    if (args === "enable-builder-input-editing") {
      return true;
    }
    return false;
  }),
  setFlag: vi.fn(),
  removeFlag: vi.fn(),
  flags: {
    "enable-builder-input-editing": true,
  },
} as ReturnType<typeof useFeatureFlags>);
vi.mocked(useParams).mockReturnValue({ workflowId: "test-workflow-id", taskId: "inputs" });
vi.mocked(useWorkflowInputExampleUri).mockReturnValue({
  data: undefined,
} as ReturnType<typeof useWorkflowInputExampleUri>);
vi.mocked(useWorkflowInputs).mockReturnValue({
  data: undefined,
} as ReturnType<typeof useWorkflowInputs>);

const WORKFLOW_INPUT_DATE: WorkflowInput = {
  id: "input-1",
  workflowId: "test-workflow-id",
  name: "Date Input",
  type: "DATETIME",
  description: "This is a date input",
  value: {
    value: new Date("2023-10-01T00:00:00Z"),
    kind: "DATETIME",
  },
};

const WORKFLOW_INPUT_TEXT: WorkflowInput = {
  id: "input-2",
  workflowId: "test-workflow-id",
  name: "Text Input",
  type: "TEXT",
  description: "This is a text input",
  value: {
    value: "Sample text",
    kind: "TEXT",
  },
};

describe(
  "GlobalInputs",
  () => {
    test("GIVEN a user with global inputs THEN they should see their inputs listed", async () => {
      // Arrange
      vi.mocked(useWorkflowInputs).mockReturnValue({
        data: [WORKFLOW_INPUT_DATE, WORKFLOW_INPUT_TEXT],
      } as ReturnType<typeof useWorkflowInputs>);

      mockDate("10/1/2023");

      // Act
      const screen = customRender(<GlobalInputs />);

      // Assert
      await expect.element(screen.getByText("Date Input")).toBeInTheDocument();
      await expect.element(screen.getByText("10/1/2023")).toBeInTheDocument();
      await expect.element(screen.getByText("Text Input")).toBeInTheDocument();
      await expect.element(screen.getByText("Sample text")).toBeInTheDocument();
    });

    test('WHEN a user clicks on the "Plus" button, they can navigate to input forms', async () => {
      // Arrange
      vi.mocked(useWorkflowInputs).mockReturnValue({
        data: undefined,
      } as ReturnType<typeof useWorkflowInputs>);

      const screen = customRender(<GlobalInputs />);
      // Act
      await userEvent.click(
        screen.getByRole("button", { name: "components.GlobalInputs.addInput" }),
      );
      await userEvent.click(screen.getByText("components.GlobalInputs.file"));

      // Assert
      await expect
        .element(screen.getByText("components.InputForm.builderInput"))
        .toBeInTheDocument();
    });

    test('WHEN a user clicks on the "Cancel" button THEN the input form should close', async () => {
      // Arrange
      vi.mocked(useWorkflowInputs).mockReturnValue({
        data: undefined,
      } as ReturnType<typeof useWorkflowInputs>);

      const screen = customRender(<GlobalInputs />);

      // Act
      await userEvent.click(
        screen.getByRole("button", { name: "components.GlobalInputs.addInput" }),
      );
      await userEvent.click(screen.getByText("components.GlobalInputs.file"));
      await expect
        .element(screen.getByText("components.InputForm.builderInput"))
        .toBeInTheDocument();

      await userEvent.click(screen.getByRole("button", { name: "components.InputForm.cancel" }));

      // Assert
      await expect
        .element(screen.getByText("components.InputForm.builderInput"))
        .not.toBeInTheDocument();
    });

    test('WHEN a user clicks on the "Save" button THEN their input should be saved', async () => {
      // Arrange
      vi.mocked(useWorkflowInputs).mockReturnValue({
        data: [WORKFLOW_INPUT_DATE, WORKFLOW_INPUT_TEXT],
      } as ReturnType<typeof useWorkflowInputs>);

      const mutateMock = vi.fn();
      vi.mocked(useCreateWorkflowInputAndSetValue).mockReturnValue({
        mutate: mutateMock,
      } as unknown as ReturnType<typeof useCreateWorkflowInputAndSetValue>);

      const screen = customRender(<GlobalInputs />);

      // Act
      await userEvent.click(
        screen.getByRole("button", { name: "components.GlobalInputs.addInput" }),
      );
      await userEvent.click(screen.getByText("components.GlobalInputs.file"));
      await expect
        .element(screen.getByText("components.InputForm.builderInput"))
        .toBeInTheDocument();

      await userEvent.type(screen.getByPlaceholder("components.InputForm.enterTitle"), "New Input");
      await userEvent.type(
        screen.getByPlaceholder("components.InputForm.enterDescription"),
        "This is a new input",
      );

      await userEvent.click(screen.getByRole("button", { name: "components.InputForm.inputType" }));
      await userEvent.click(screen.getByRole("option", { name: "Text" }));

      await userEvent.type(
        screen.getByPlaceholder("components.InputForm.enterValue"),
        "New input value",
      );

      // Save the new input
      await userEvent.click(screen.getByRole("button", { name: "components.InputForm.save" }));

      // Assert
      expect(mutateMock).toHaveBeenCalledWith(
        {
          workflowId: "test-workflow-id",
          input: {
            name: "New Input",
            description: "This is a new input",
            type: "TEXT",
            value: "New input value",
          },
        },
        expect.anything(),
      );
    });

    test("GIVEN a user is filling out a new input form WHEN they have unsaved changes THEN they cannot add another input mid form fill", async () => {
      // Arrange
      vi.mocked(useWorkflowInputs).mockReturnValue({
        data: [WORKFLOW_INPUT_DATE, WORKFLOW_INPUT_TEXT],
      } as ReturnType<typeof useWorkflowInputs>);

      mockDate("10/1/2023");

      const mutateMock = vi.fn();
      vi.mocked(useCreateWorkflowInputAndSetValue).mockReturnValue({
        mutate: mutateMock,
      } as unknown as ReturnType<typeof useCreateWorkflowInputAndSetValue>);

      const screen = customRender(<GlobalInputs />);
      const addInputButton = screen.getByRole("button", {
        name: "components.GlobalInputs.addInput",
      });

      // Testing: Title
      await userEvent.click(addInputButton);
      await userEvent.click(screen.getByText("components.GlobalInputs.text"));

      await userEvent.type(screen.getByPlaceholder("components.InputForm.enterTitle"), "New Input");
      await expect.element(addInputButton).toBeDisabled();

      await userEvent.click(screen.getByRole("button", { name: "components.InputForm.cancel" }));
      await expect.element(addInputButton).not.toBeDisabled();

      // Testing: Description
      await userEvent.click(addInputButton);
      await userEvent.click(screen.getByText("components.GlobalInputs.text"));

      await userEvent.type(
        screen.getByPlaceholder("components.InputForm.enterDescription"),
        "This is a new input",
      );
      await expect.element(addInputButton).toBeDisabled();

      await userEvent.click(screen.getByRole("button", { name: "components.InputForm.cancel" }));
      await expect.element(addInputButton).not.toBeDisabled();

      // Testing: Value
      await userEvent.click(addInputButton);
      await userEvent.click(screen.getByText("components.GlobalInputs.text"));

      await userEvent.type(
        screen.getByPlaceholder("components.InputForm.enterValue"),
        "New input value",
      );
      await expect.element(addInputButton).toBeDisabled();

      await userEvent.click(screen.getByRole("button", { name: "components.InputForm.cancel" }));
      await expect.element(addInputButton).not.toBeDisabled();
    });

    test('WHEN a user clicks "Edit" on an existing input THEN they should see the input form with pre-filled values', async () => {
      // Arrange
      vi.mocked(useWorkflowInputs).mockReturnValue({
        data: [WORKFLOW_INPUT_TEXT],
      } as ReturnType<typeof useWorkflowInputs>);

      const screen = customRender(<GlobalInputs />);

      // Act
      await userEvent.click(screen.getByText("Edit"));

      // Assert
      await expect
        .element(screen.getByPlaceholder("components.InputForm.enterTitle"))
        .toHaveValue("Text Input");
      await expect
        .element(screen.getByPlaceholder("components.InputForm.enterDescription"))
        .toHaveValue("This is a text input");
      await expect
        .element(screen.getByPlaceholder("components.InputForm.enterValue"))
        .toHaveValue("Sample text");
    });

    // When editing a form
    // should not be able to change the type
    // saved changes should be saved
    test("WHEN a user is editing an existing input THEN the they should not be allowed to change the type", async () => {
      // Arrange
      vi.mocked(useWorkflowInputs).mockReturnValue({
        data: [WORKFLOW_INPUT_TEXT],
      } as ReturnType<typeof useWorkflowInputs>);

      const screen = customRender(<GlobalInputs />);

      // Act
      await userEvent.click(screen.getByText("Edit"));

      // Assert
      const inputTypeButton = screen.getByRole("button", {
        name: "components.InputForm.inputType",
      });
      await expect.element(inputTypeButton).toBeDisabled();
    });

    test('WHEN a user edits input form and clicks "Save" THEN the changes should be saved', async () => {
      // Arrange
      vi.mocked(useWorkflowInputs).mockReturnValue({
        data: [WORKFLOW_INPUT_TEXT],
      } as ReturnType<typeof useWorkflowInputs>);

      const mutateMock = vi.fn();
      vi.mocked(useUpdateWorkflowInputAndSetValue).mockReturnValue({
        mutate: mutateMock,
      } as unknown as ReturnType<typeof useUpdateWorkflowInputAndSetValue>);

      const screen = customRender(<GlobalInputs />);

      // Act
      await userEvent.click(screen.getByText("Edit"));

      await userEvent.clear(screen.getByPlaceholder("components.InputForm.enterTitle"));
      await userEvent.type(
        screen.getByPlaceholder("components.InputForm.enterTitle"),
        "Updated Text Input",
      );

      await userEvent.clear(screen.getByPlaceholder("components.InputForm.enterDescription"));
      await userEvent.type(
        screen.getByPlaceholder("components.InputForm.enterDescription"),
        "Updated description",
      );

      await userEvent.clear(screen.getByPlaceholder("components.InputForm.enterValue"));
      await userEvent.type(
        screen.getByPlaceholder("components.InputForm.enterValue"),
        "Updated value",
      );

      // Save the updated input
      await userEvent.click(screen.getByRole("button", { name: "components.InputForm.save" }));

      // Assert
      expect(mutateMock).toHaveBeenCalledWith({
        workflowId: "test-workflow-id",
        workflowInputId: "input-2",
        input: {
          id: "input-2",
          workflowId: "test-workflow-id",
          name: "Updated Text Input",
          description: "Updated description",
          type: "TEXT",
          value: {
            value: "Updated value",
            kind: "TEXT",
          },
        },
      });
    });
  },
  {
    timeout: 5000,
  },
);
