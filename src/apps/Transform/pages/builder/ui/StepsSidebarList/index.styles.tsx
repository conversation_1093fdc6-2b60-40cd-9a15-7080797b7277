import { styled } from "styled-components";
import { Tooltip } from "@floqastinc/flow-ui_core";

export const AddStepSeparatorButton = styled.button`
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  transform: translate(-50%, -50%);
  padding: 2px;
  margin-top: 5px;
  &:hover {
    opacity: 1;
    cursor: pointer;
  }
`;

export const StepSeparator = styled.div`
  position: relative;
  width: 2px;
  min-width: 2px;
  height: 8px;
  min-height: 8px;
  background-color: var(--flo-sem-color-stroke-forms);
  z-index: 10;
`;

export const TooltipContent = styled(Tooltip.Content)`
  z-index: 100;
`;
