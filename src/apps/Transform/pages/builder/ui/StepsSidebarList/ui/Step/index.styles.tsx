import { styled } from "styled-components";

export const StatusIndicator = styled.div`
  position: absolute;
  top: 4px;
  right: 6px;
  width: 8px;
  height: 8px;
`;

export const RotatingIcon = styled.div`
  @keyframes rotateClockwise {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  display: inline-block;
  animation: rotateClockwise 1s linear infinite;
`;

type SemColor = "success" | "warning" | "info" | "danger";
const getFloSemColor = (color: SemColor) => {
  return `var(--flo-sem-color-${color})`;
};
export const Indicator = styled.div<{ $color: SemColor }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${(props) => getFloSemColor(props.$color)};
`;
