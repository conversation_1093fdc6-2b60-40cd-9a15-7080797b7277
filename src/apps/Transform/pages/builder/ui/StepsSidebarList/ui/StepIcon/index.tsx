import { TaskStrategy } from "@floqastinc/transform-v3";
import { Heading } from "@floqastinc/flow-ui_core";
import { match } from "ts-pattern";
import { Pause } from "@/svg/Pause";
import { Bolt } from "@/svg/Bolt";

interface StepIconProps {
  taskKind: TaskStrategy["kind"] | undefined;
  stepNum: number;
}

export default function StepIcon({ taskKind, stepNum }: StepIconProps) {
  if (!taskKind) {
    return null;
  }

  return match(taskKind)
    .with("REVIEW", () => <Pause />)
    .with("JEM_EXPORT", "JEM_TEMPLATE_FETCH", "FLOLAKE", () => <Bolt />)
    .otherwise(() => (
      <Heading variant="h5" weight="medium" className="step-number">
        {stepNum}
      </Heading>
    ));
}
