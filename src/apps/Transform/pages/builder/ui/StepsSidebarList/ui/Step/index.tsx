import { useParams } from "react-router-dom";
import { match } from "ts-pattern";
import CheckCircleIcon from "@floqastinc/flow-ui_icons/material/CheckCircle";
import AutoRenewIcon from "@floqastinc/flow-ui_icons/material/Autorenew";
import { useMutationState } from "@tanstack/react-query";
import { ExampleSet } from "@floqastinc/transform-v3";
import { useEffect, useState } from "react";
import StepIcon from "../StepIcon";
import { StepFailedWarningToast } from "../StepFailedWarningToast";
import * as Styled from "./index.styles";
import { useTask } from "@v3/tasks";
import WarningIcon from "@/components/WarningIcon";
import { getIsChatBasedStrategy } from "@/utils/strategy";

type StepProps = {
  taskId: string;
  stepNum: number;
};

export const Step = ({ taskId, stepNum }: StepProps) => {
  const { workflowId = "" } = useParams();
  const [isOptimisticallyBuilding, setIsOptimisticallyBuilding] = useState(false);
  const updateExampleSetMutationState = useMutationState({
    filters: { mutationKey: ["updateExampleSet", { workflowId, taskId }] },
    select: (mutation) => mutation.state.data,
  }) as unknown as ExampleSet[];

  // The task is optimistically building if an transform example set update is active and
  // its build status is not set or is busy
  useEffect(() => {
    const updatedExampleSet =
      updateExampleSetMutationState[0] && updateExampleSetMutationState[0].taskId === taskId
        ? updateExampleSetMutationState[0]
        : undefined;
    const updatedExampleSetBuildStatus =
      updatedExampleSet?.strategy?.kind === "SCRIPT"
        ? updatedExampleSet?.strategy?.megascriptStatus
        : undefined;
    if (
      updatedExampleSet?.status === "ACTIVE" &&
      getIsChatBasedStrategy(updatedExampleSet?.strategy) &&
      (!updatedExampleSetBuildStatus || updatedExampleSetBuildStatus === "BUSY")
    ) {
      setIsOptimisticallyBuilding(true);
    } else {
      setIsOptimisticallyBuilding(false);
    }
  }, [updateExampleSetMutationState, taskId]);

  // TODO: add optimistic update for script status using
  //  the updateExample mutation.
  const taskQuery = useTask(
    { workflowId: workflowId, taskId: taskId },
    {
      refetchInterval: (query) => {
        const buildStatus =
          query.state.data?.data?.strategy?.kind === "SCRIPT"
            ? query.state.data?.data?.strategy?.buildStatus
            : undefined;
        if (buildStatus === "BUSY" || isOptimisticallyBuilding) {
          return 2000;
        }
        return undefined;
      },
      enabled: !!workflowId && !!taskId,
    },
  );

  useEffect(() => {
    if (
      taskQuery.data?.strategy?.kind === "SCRIPT" &&
      taskQuery.data?.strategy?.buildStatus === "BUSY"
    ) {
      setIsOptimisticallyBuilding(false);
    }
  }, [taskQuery.data]);

  let buildStatus =
    taskQuery.data?.strategy?.kind === "SCRIPT" ? taskQuery.data?.strategy?.buildStatus : undefined;
  if (isOptimisticallyBuilding) {
    buildStatus = "BUSY";
  }

  return (
    <>
      {buildStatus === "FAILED" ? (
        <WarningIcon className="failed-icon" />
      ) : (
        <StepIcon taskKind={taskQuery.data?.strategy?.kind} stepNum={stepNum} />
      )}
      <Styled.StatusIndicator>
        {match(buildStatus)
          .with("BUSY", () => (
            <div style={{ top: "-4px", position: "absolute" }}>
              <Styled.RotatingIcon>
                <AutoRenewIcon className="busy-icon" size={12} color="var(--flo-sem-color-info)" />
              </Styled.RotatingIcon>
            </div>
          ))
          .with("COMPILED", () => (
            <CheckCircleIcon
              className="compiled-icon"
              size={12}
              color="var(--flo-sem-color-success)"
            />
          ))
          .with("NONE", undefined, () => null)
          .otherwise(() => null)}
      </Styled.StatusIndicator>
      <StepFailedWarningToast task={taskQuery.data} stepNum={stepNum} />
    </>
  );
};
