import { describe, expect, afterEach, test, vi } from "vitest";
import { Task } from "@floqastinc/transform-v3";
import { Step } from ".";
import { customRender } from "@/utils/testing";
import { useTask } from "@v3/tasks";
import { useUpdateExample } from "@v3/examples";

vi.mock("@v3/tasks");
vi.mock("@v3/examples");

const mockScriptTask = {
  id: "task-1",
  kind: "SCRIPT",
  name: "Test Task",
  strategy: {
    kind: "SCRIPT",
    buildStatus: "BUSY",
  },
} as unknown as Task;

const mockReviewTask = {
  id: "task-2",
  kind: "REVIEW",
  name: "Review Task",
  strategy: {
    kind: "REVIEW",
  },
} as unknown as Task;

const mockJemExportTask = {
  id: "task-3",
  kind: "JEM_EXPORT",
  name: "JEM Export Task",
  strategy: {
    kind: "JEM_EXPORT",
  },
} as unknown as Task;

describe("Step", () => {
  afterEach(() => {
    vi.resetAllMocks();
  });

  test("GIVEN a step is a REVIEW step, THEN it should display a pause icon", async () => {
    vi.mocked(useTask).mockReturnValue({
      data: mockReviewTask,
    } as unknown as ReturnType<typeof useTask>);

    const { TransformProvider } = await import("@v3/context");

    const { container } = customRender(
      <TransformProvider>
        <Step taskId={mockReviewTask.id} stepNum={1} />
      </TransformProvider>,
    );

    await vi.waitFor(() => expect(container.querySelector(".pause-icon")).toBeInTheDocument());
  });

  test("GIVEN a step is a JEM_EXPORT step, THEN it should display a JEM icon", async () => {
    vi.mocked(useTask).mockReturnValue({
      data: mockJemExportTask,
    } as unknown as ReturnType<typeof useTask>);

    const { TransformProvider } = await import("@v3/context");

    const { container } = customRender(
      <TransformProvider>
        <Step taskId={mockJemExportTask.id} stepNum={2} />
      </TransformProvider>,
    );

    await vi.waitFor(() => expect(container.querySelector(".bolt-icon")).toBeInTheDocument());
  });

  test("GIVEN a step is a SCRIPT step with BUSY status, THEN it should display an loading icon", async () => {
    const mockBusyTask = {
      ...mockScriptTask,
      strategy: {
        ...mockScriptTask.strategy,
        buildStatus: "BUSY",
      },
    } as unknown as Task;
    vi.mocked(useTask).mockReturnValue({
      data: mockBusyTask,
    } as unknown as ReturnType<typeof useTask>);

    const { TransformProvider } = await import("@v3/context");

    const { container } = customRender(
      <TransformProvider>
        <Step taskId={mockScriptTask.id} stepNum={3} />
      </TransformProvider>,
    );

    await vi.waitFor(() => expect(container.querySelector(".busy-icon")).toBeInTheDocument());
  });

  test("GIVEN a step is a SCRIPT step with COMPILED status, THEN it should display a check icon", async () => {
    const mockCompiledTask = {
      ...mockScriptTask,
      strategy: {
        ...mockScriptTask.strategy,
        buildStatus: "COMPILED",
      },
    } as unknown as Task;

    vi.mocked(useTask).mockReturnValue({
      data: mockCompiledTask,
    } as unknown as ReturnType<typeof useTask>);

    const { TransformProvider } = await import("@v3/context");

    const { container } = customRender(
      <TransformProvider>
        <Step taskId={mockCompiledTask.id} stepNum={4} />
      </TransformProvider>,
    );

    await vi.waitFor(() => expect(container.querySelector(".compiled-icon")).toBeInTheDocument());
  });

  test("GIVEN a step is a SCRIPT step with FAILED status, THEN it should display a warning icon", async () => {
    const mockFailedTask = {
      ...mockScriptTask,
      strategy: {
        ...mockScriptTask.strategy,
        buildStatus: "FAILED",
      },
    } as unknown as Task;

    vi.mocked(useTask).mockReturnValue({
      data: mockFailedTask,
    } as unknown as ReturnType<typeof useTask>);

    const { TransformProvider } = await import("@v3/context");

    const { container } = customRender(
      <TransformProvider>
        <Step taskId={mockFailedTask.id} stepNum={5} />
      </TransformProvider>,
    );

    const failedIcon = await vi.waitFor(async () => container.querySelector(".failed-icon"));

    expect(failedIcon).toBeInTheDocument();
  });

  test("GIVEN a step is a SCRIPT step, THEN it should display the step number", async () => {
    vi.mocked(useTask).mockReturnValue({
      data: mockScriptTask,
    } as unknown as ReturnType<typeof useTask>);

    const { TransformProvider } = await import("@v3/context");

    const { container } = customRender(
      <TransformProvider>
        <Step taskId={mockScriptTask.id} stepNum={4} />
      </TransformProvider>,
    );

    await vi.waitFor(() => expect(container.querySelector(".step-number")).toHaveTextContent("4"));
  });

  test("GIVEN a step is optimistically building, THEN it should display the AutoRenewIcon", async () => {
    // Simulate updateExampleSetMutation.data with ACTIVE status and kind SCRIPT, no megascriptStatus
    const mockTask = {
      ...mockScriptTask,
      strategy: {
        ...mockScriptTask.strategy,
      },
    } as unknown as Task;

    vi.mocked(useTask).mockReturnValue({
      data: mockTask,
    } as unknown as ReturnType<typeof useTask>);

    // Mock useUpdateExample to simulate an active update with status ACTIVE and kind SCRIPT, no megascriptStatus
    // Instead of vi.spyOn, directly mock the module export before any imports/use
    vi.mocked(useUpdateExample).mockReturnValue({
      data: {
        taskId: mockTask.id,
        status: "ACTIVE",
        strategy: {
          kind: "SCRIPT",
        },
      },
    } as unknown as ReturnType<typeof useUpdateExample>);

    const { TransformProvider } = await import("@v3/context");

    const { container } = customRender(
      <TransformProvider>
        <Step taskId={mockTask.id} stepNum={2} />
      </TransformProvider>,
    );

    await vi.waitFor(() => {
      const busyIcon = container.querySelector(".busy-icon");
      expect(busyIcon).toBeInTheDocument();
    });
  });
});
