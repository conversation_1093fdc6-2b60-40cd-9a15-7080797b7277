import { useToast } from "@floqastinc/flow-ui_core";
import { Task } from "@floqastinc/transform-v3";
import { useEffect, useRef } from "react";
import * as styled from "./index.styles";
import { AGENTS, BUILDER, STEPS, V3 } from "@/constants";

const TOAST_DURATION = 5000; // 5 seconds
const TOAST_POSITION = "bottom-left";
const TOAST_ID = "step-failed-warning-toast";

interface StepFailedWarningToastProps {
  task: Task | undefined;
  stepNum: number;
}

/**
 * This component will monitor changes to the task status and show a warning toast
 * when a step fails after being in a busy state.
 */
export const StepFailedWarningToast = ({ task, stepNum }: StepFailedWarningToastProps) => {
  const { showToast, removeToast, Toast } = useToast();
  const buildStatus = task?.strategy?.kind === "SCRIPT" ? task?.strategy?.buildStatus : undefined;
  const prevScriptTaskBuildStatus = useRef(buildStatus);

  useEffect(() => {
    if (prevScriptTaskBuildStatus.current === "BUSY" && buildStatus === "FAILED") {
      showToast(
        <Toast type="warning">
          <Toast.Title>There was an issue with Step {stepNum}</Toast.Title>
          <Toast.Message>
            <styled.ToastMessageContents>
              <styled.Link
                to={`/${BUILDER}/${V3}/${AGENTS}/${task?.workflowId}/${STEPS}/${task?.id}`}
                onClick={() => removeToast(TOAST_ID)}
              >
                Edit this step
              </styled.Link>
              to try again.
            </styled.ToastMessageContents>
          </Toast.Message>
        </Toast>,
        {
          duration: TOAST_DURATION,
          position: TOAST_POSITION,
          id: TOAST_ID,
        },
      );
    }
    prevScriptTaskBuildStatus.current = buildStatus;
  }, [buildStatus, showToast, stepNum, task?.id, task?.workflowId, Toast, removeToast]);

  return null;
};
