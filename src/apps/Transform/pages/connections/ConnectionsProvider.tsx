import { ReactNode } from "react";
import {
  useActiveConnections,
  useAvailableConnections,
} from "../../../../api/shared/connections-query";
import { ConnectionsContext } from "./ConnectionsContext";

export const ConnectionsProvider = ({ children }: { children: ReactNode }) => {
  const availableConnections = useAvailableConnections();
  const activeConnections = useActiveConnections();

  return (
    <ConnectionsContext.Provider
      value={
        {
          availableConnections: availableConnections.data ?? [],
          activeConnections: activeConnections.data ?? [],
          isLoading: availableConnections.isLoading || activeConnections.isLoading,
          error: availableConnections.error || activeConnections.error,
        } as any
      }
    >
      {children}
    </ConnectionsContext.Provider>
  );
};
