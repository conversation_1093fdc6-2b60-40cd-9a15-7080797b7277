import { createContext } from "react";
import { ActiveConnection, ProvidersData, FlolakeConnectionData } from "@/api/shared/types";

interface ConnectionsContextType {
  availableConnections: ProvidersData[];
  activeConnections: ActiveConnection[];
  flolakeConnections?: FlolakeConnectionData[];
  isLoading: boolean;
  error: Error | null;
}

export const ConnectionsContext = createContext<ConnectionsContextType | null>(null);
