export const TipaltiIcon = () => (
  <svg
    width="56"
    height="56"
    viewBox="0 0 56 56"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <rect width="56" height="56" rx="6" fill="#FFBC00" fillOpacity="0.06" />
    <rect x="16.8887" y="8" width="22.2222" height="40" fill="url(#pattern0_2130_23468)" />
    <defs>
      <pattern
        id="pattern0_2130_23468"
        patternContentUnits="objectBoundingBox"
        width="1"
        height="1"
      >
        <use xlinkHref="#image0_2130_23468" transform="scale(0.01 0.00555556)" />
      </pattern>
      <image
        id="image0_2130_23468"
        width="100"
        height="180"
        xlinkHref="data:image/png;base64,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"
      />
    </defs>
  </svg>
);
