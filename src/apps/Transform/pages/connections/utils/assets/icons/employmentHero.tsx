export const EmploymentHeroIcon = () => (
  <svg
    width="38"
    height="37"
    viewBox="0 0 38 37"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <rect width="38" height="37" fill="url(#pattern0_509_8088)" />
    <defs>
      <pattern id="pattern0_509_8088" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_509_8088" transform="matrix(0.0103093 0 0 0.0105263 -0.958763 0)" />
      </pattern>
      <image
        id="image0_509_8088"
        width="282"
        height="153"
        preserveAspectRatio="none"
        xlinkHref="data:image/png;base64,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"
      />
    </defs>
  </svg>
);
