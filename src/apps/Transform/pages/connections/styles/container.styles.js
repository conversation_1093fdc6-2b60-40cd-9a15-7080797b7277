import styled from "styled-components";

export const Styled_Header = styled.section`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background-color: var(--flo-base-color-neutral-0);
`;

export const Styled_List = styled.ul`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(312px, 1fr));
  grid-auto-rows: 1fr;
  gap: 24px;
  width: 100%;
  padding-bottom: 24px;
`;

export const Styled_ListItem = styled.li`
  height: 100%;
  display: flex;
`;

export const StyledOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #1d2433a6;
  z-index: 100;
  display: flex;
  align-items: flex-end;
`;

export const StyledModal = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: white;
  padding: 16px;
  box-sizing: border-box;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  max-height: 175px;
  display: flex;
  flex-direction: column;
  h3 {
    line-height: 20px;
  }
  & > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }
  & > ul {
    list-style: disc;
    padding-left: 28px;
    overflow-y: auto;
    max-height: 320px;
    padding-right: 10px;
  }
`;

export const Styled_SectionContainer = styled.section`
  ${({ contentType }) => {
    switch (contentType) {
      case "tabs":
        return `
                padding: 16px 24px;
                padding-bottom: 0;
            `;
      case "category":
        return `
                padding: 40px;
                padding-bottom: 120px;
                `;
      case "action":
        return `
                padding: 16px 40px;
                padding-bottom: 120px;
                `;
      case "content":
        return `
                padding: 32px 24px;
                `;
      default:
        return "padding: 16px 24px;";
    }
  }}

  ${({ contentStyle = "" }) => {
    switch (contentStyle) {
      case "light":
        return `background-color: var(--flo-base-color-neutral-0);`;
      default:
        return "";
    }
  }}

    ${({ direction = "column" }) => {
    switch (direction) {
      case "row":
        return `
                display: grid;
                grid-template-columns: 3fr 2fr;
                align-items: start;
                justify-content: space-between;
            `;
      case "column":
        return `
                display: flex;
                flex-direction: column;
                align-items: start;
                gap: 8px;
            `;
      default:
        return "";
    }
  }}
`;

export const Styled_ContentContainer = styled.div`
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  ${({ contentType }) => {
    switch (contentType) {
      case "action":
        return `
                padding: 16px 24px;
                padding-bottom: 120px;
                `;
      case "category":
      default:
        return `
                padding: 40px 24px;
                padding-bottom: 120px;
                `;
    }
  }}
`;

export const Styled_Card = styled.div`
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: var(--flo-base-color-neutral-0);
  border: 1px solid var(--flo-base-color-neutral-300);
  border-radius: 8px;
  padding: 16px;
  gap: 24px;
`;

export const Styled_ActionHeading = styled.div`
  padding: 32px 40px;
`;

export const Styled_LogoStatusWrapper = styled.div`
  display: flex;
  justify-content: space-between;
`;

export const BetaBadgeContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;
