import styled from "styled-components";

export const Styled_SectionTitle = styled.h2`
  font-family: var(--flo-base-font-family-1);
  font-size: var(--flo-base-font-size-8);
  font-weight: var(--flo-base-font-weight-6);
  line-height: var(--flo-base-line-height-6);
`;

export const Styled_TabLabel = styled.h3`
  font-family: var(--flo-base-font-family-1);
  font-size: var(--flo-base-font-size-3);
  font-weight: var(--flo-base-font-weight-6);
  line-height: var(--flo-base-line-height-3);
  padding-bottom: var(--flo-base-font-size-6);
`;

export const Styled_SectionDescription = styled.p`
  font-family: var(--flo-base-font-family-1);
  font-size: var(--flo-base-font-size-6);
  font-weight: var(--flo-base-font-weight-4);
  line-height: var(--flo-base-line-height-4);

  ${({ contentStyle }) => {
    switch (contentStyle) {
      case "light":
        return "color: var(--flo-base-color-neutral-600);";
      default:
        return "color: var(--fq-text-default);";
    }
  }}
`;

export const Styled_CategoryTitle = styled.h3`
  font-family: var(--flo-base-font-family-1);
  font-size: var(--flo-base-font-size-8);
  font-weight: var(--flo-base-font-weight-6);
  line-height: var(--flo-base-line-height-6);
`;

export const Styled_CardTitle = styled.h5`
  font-family: var(--flo-base-font-family-1);
  font-size: var(--flo-base-font-size-6);
  font-weight: var(--flo-base-font-weight-4);
  line-height: var(--flo-base-line-height-4);
`;

export const Styled_CardDescription = styled.p`
  font-family: var(--flo-base-font-family-2);
  font-size: var(--flo-base-font-size-3);
  line-height: var(--flo-base-line-height-3);
  font-weight: var(--flo-base-font-weight-4);
`;

export const Styled_CardDate = styled.p`
  font-family: var(--flo-base-font-family-2);
  font-size: var(--flo-base-font-size-3);
  font-weight: var(--flo-base-font-weight-4);
  line-height: var(--flo-base-line-height-3);
  color: var(--fq-grey-2);
`;

export const Styled_ModalText = styled.p`
  font-family: var(--flo-base-font-family-2);
  font-size: var(--flo-base-font-size-3);
  min-height: 76px;
`;

export const IconRowContainer = styled.div`
  display: flex;
  gap: 8px;
`;

export const IconContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f3f9;
  overflow: hidden;
  width: ${({ width }) => width};
  height: ${({ height }) => height};
  border-radius: ${({ borderRadius }) => borderRadius};
`;
