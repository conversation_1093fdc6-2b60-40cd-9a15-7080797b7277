import { useState } from "react";
import { Expandable } from "@floqastinc/flow-ui_core";
import ArrowRight from "@floqastinc/flow-ui_icons/material/KeyboardArrowRight";
import ArrowDown from "@floqastinc/flow-ui_icons/material/KeyboardArrowDown";
import { Styled_CollapseButton } from "../../styles/button.styles";
import { Styled_CategoryTitle } from "../../styles/text.styles";
import { ConnectionServicesList } from "./ConnectionServicesList";
import { ProvidersData } from "@/api/shared/types";

const Icon = ({ expanded }: { expanded: boolean }) => {
  return expanded ? <ArrowRight /> : <ArrowDown />;
};

export const ConnectionCategory = (category: ProvidersData) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const categoryName = category.categoryName;

  return (
    <div key={categoryName.key} style={{ marginBottom: "16px" }}>
      <Styled_CollapseButton onClick={() => setIsExpanded(!isExpanded)}>
        <Icon expanded={isExpanded} />
        <Styled_CategoryTitle>{categoryName.value}</Styled_CategoryTitle>
      </Styled_CollapseButton>

      <Expandable open={isExpanded}>
        <div>
          <ConnectionServicesList
            providers={category.providers}
            categoryName={category.categoryName}
          />
        </div>
      </Expandable>
    </div>
  );
};
