/* eslint-disable i18next/no-literal-string, no-restricted-syntax */
import { t } from "@/utils/i18n";
import { useMemo } from "react";
import { But<PERSON>, TableStatusBadge, Tooltip } from "@floqastinc/flow-ui_core";
import { ProtectedComponent } from "@floqastinc/auth-module-client";
import { getBrandIcon, getProductIcon } from "../../utils/asset-utils";
import {
  BetaBadgeContainer,
  Styled_Card,
  Styled_List,
  Styled_ListItem,
  Styled_LogoStatusWrapper,
} from "../../styles/container.styles";
import {
  Styled_CardDescription,
  Styled_CardTitle,
  IconContainer,
  IconRowContainer,
} from "../../styles/text.styles";
import { useConnections } from "../../hooks/useConnections";
import { ProvidersData, Provider } from "@/api/shared/types";
import { USER_ACTION_KEYS } from "@/authorization";

interface ConnectionItemProps {
  service: Provider;
  isActive: boolean;
  connectionId?: string;
}

const ConnectionItem = ({ service, isActive }: ConnectionItemProps) => {
  const { isFFEnabled } = service;
  const buttonText = isActive
    ? t("components.Connections.Actions.manage")
    : isFFEnabled
      ? t("components.Connections.Actions.setup")
      : t("components.Connections.Actions.requestAccess");

  const Icon = getBrandIcon(service.name.slug);
  const { activeConnections } = useConnections();

  const { matchingConnectionId } = useMemo<{
    matchingConnectionId: string;
  }>(() => {
    if (!activeConnections || !activeConnections.length) {
      return { matchingConnectionId: "" };
    }

    const matchingConnections = activeConnections.filter(
      (connection: { integrationSystem: string }) =>
        connection.integrationSystem === service.name.slug,
    );

    if (matchingConnections.length === 0) {
      return { matchingConnectionId: "", matchingTableNames: [] };
    }

    const sortedConnections = [...matchingConnections].sort((a, b) => {
      try {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      } catch (error) {
        console.error(error);
        return 0;
      }
    });

    return {
      matchingConnectionId: sortedConnections[0]._id,
      matchingTableNames: service.availableData || [],
    };
  }, [activeConnections, service]);

  const getConnectionUrl = (slug: string) => {
    const baseUrl =
      process.env.NODE_ENV === "development" ? "http://localhost:3000" : window.location.origin;

    if (isActive && matchingConnectionId) {
      return `${baseUrl}/settings/connections/${matchingConnectionId}`;
    }
    return `${baseUrl}/settings/connections/new/${slug}`;
  };

  const DisabledBtn = () => {
    return (
      <Tooltip>
        <Tooltip.Trigger>
          <Button
            color="dark"
            variant="outlined"
            aria-label={[service.name.label].join(" ")}
            style={{ maxWidth: "max-content" }}
            disabled
          >
            {buttonText}
          </Button>
        </Tooltip.Trigger>
        <Tooltip.Content side="top" size="sm">
          {t("components.Connections.Permissions.adminAccessRequired")}
        </Tooltip.Content>
      </Tooltip>
    );
  };

  return (
    <Styled_Card style={{ position: "relative" }}>
      <Styled_LogoStatusWrapper>
        <IconRowContainer>
          <IconContainer width="56px" height="56px" borderRadius="6px">
            {Icon && <Icon width="56px" height="56px" />}
          </IconContainer>
          {service.products.map((product) => {
            const ProductIcon = getProductIcon(product.slug);
            return (
              <Tooltip key={product.slug}>
                <Tooltip.Trigger>
                  <IconContainer width="24px" height="24px" borderRadius="50%">
                    <ProductIcon />
                  </IconContainer>
                </Tooltip.Trigger>
                <Tooltip.Content hasArrow side="top">
                  {product.label}
                </Tooltip.Content>
              </Tooltip>
            );
          })}
        </IconRowContainer>
        {isActive && (
          <div>
            <TableStatusBadge color="success" hasIcon={false}>
              {t("components.Connections.Status.connected")}
            </TableStatusBadge>
          </div>
        )}
      </Styled_LogoStatusWrapper>

      <div>
        <BetaBadgeContainer>
          <Styled_CardTitle>{service.name.label}</Styled_CardTitle>
          {service.isBeta && (
            <TableStatusBadge size="xs" color="warning" hasIcon={false} data-qe-id="beta-status">
              Beta
            </TableStatusBadge>
          )}
        </BetaBadgeContainer>
        <Styled_CardDescription>{service.description.value}</Styled_CardDescription>
      </div>

      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <ProtectedComponent
          actionKey={USER_ACTION_KEYS.CONNECTIONS_FULL}
          fallback={<DisabledBtn />}
        >
          <Button
            color="dark"
            variant="outlined"
            aria-label={[service.name.label].join(" ")}
            style={{ maxWidth: "max-content" }}
            onClick={() => window.open(getConnectionUrl(service.name.slug), "_blank")}
          >
            {buttonText}
          </Button>
        </ProtectedComponent>
      </div>
    </Styled_Card>
  );
};
export const ConnectionServicesList = (category: ProvidersData) => {
  const { activeConnections } = useConnections();

  const providerConnections = useMemo(() => {
    return category.providers.map((provider) => {
      const slug = provider.name.slug.toLowerCase();
      const connections = activeConnections[slug] || [];
      const isActive = connections.length > 0;
      const connectionId = isActive ? connections[0]._id : "";

      return {
        provider,
        isActive,
        connectionId,
      };
    });
  }, [category.providers, activeConnections]);

  return (
    <Styled_List style={{ margin: "24px 0" }}>
      {providerConnections.map(({ provider, isActive, connectionId }) => (
        <Styled_ListItem key={provider.name.slug}>
          <ConnectionItem service={provider} isActive={isActive} connectionId={connectionId} />
        </Styled_ListItem>
      ))}
    </Styled_List>
  );
};
