import { RunStatus } from "@floqastinc/transform-v3";
import { match } from "ts-pattern";
import { t } from "@/utils/i18n";

export const getRunStatusDisplayProperties = (status: RunStatus) => {
  return match(status)
    .with("CANCELED", "FAILED", () => ({
      badgeColor: "warning",
      statusText: t("components.WorkflowRuns.failed"),
    }))
    .with("REJECTED", () => ({
      badgeColor: "danger",
      statusText: t("components.WorkflowRuns.rejected"),
    }))
    .with("COMPLETED", () => ({
      badgeColor: "success",
      statusText: t("components.WorkflowRuns.completed"),
    }))
    .with("BLOCKED", "PENDING", "REVIEW", "RUNNING", () => ({
      badgeColor: "info",
      statusText: t("components.WorkflowRuns.inProgress"),
    }))
    .with("READY", () => ({
      badgeColor: undefined,
      statusText: undefined,
    }))
    .exhaustive();
};
