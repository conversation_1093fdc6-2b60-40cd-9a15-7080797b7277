import { Outlet } from "react-router-dom";
import { Toaster } from "@floqastinc/flow-ui_core";
import { useModal } from "@/components";
import { FeatureFlagDevTools } from "@/components/FeatureFlag";
import {
  AddWorkflowModal,
  EditWorkflowModal,
} from "@BuilderV3/app-components/Modals/WorkflowModals";
import { WorkflowRunNotesModal } from "@BuilderV3/app-components/Modals/WorkflowRunNotes";
import { AddTaskModal } from "@BuilderV3/app-components/Modals/AddTaskModal";
import { DeleteWorkflowModal } from "@BuilderV3/app-components/Modals/DeleteWorkflowModal";
import { DeleteWorkflowRunModal } from "@BuilderV3/app-components/Modals/DeleteWorkflowRunModal";
import { AddInputModal } from "@BuilderV3/app-components/Modals/AddInputModal";

export const App = () => {
  // TODO: Need to rework modals and routing.
  // - collapse everything into single app and routing structure
  // - change modals to have better type hinting, discoverability, flexibility
  const { currentModal } = useModal();
  const renderModal = () => {
    if (!currentModal) return null;
    const { name, props } = currentModal;
    switch (name) {
      case "AddWorkflow":
        return <AddWorkflowModal {...props} />;
      case "EditWorkflow":
        return <EditWorkflowModal {...props} />;
      case "EditWorkflowRunNotes":
        return <WorkflowRunNotesModal {...props} />;
      case "AddTask":
        return <AddTaskModal {...props} />;
      case "DeleteWorkflow":
        return <DeleteWorkflowModal {...props} />;
      case "DeleteWorkflowRun":
        return <DeleteWorkflowRunModal {...props} />;
      case "AddInput":
        return <AddInputModal {...props} />;
      default:
        return null;
    }
  };

  return (
    <>
      <Toaster />
      <FeatureFlagDevTools />
      {renderModal()}
      <Outlet />
    </>
  );
};
