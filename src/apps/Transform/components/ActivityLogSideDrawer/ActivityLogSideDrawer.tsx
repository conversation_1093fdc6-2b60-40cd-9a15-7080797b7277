import { SideDrawer, CloseButton } from "@floqastinc/flow-ui_core";
import * as Styled from "./ActivityLogSideDrawer.styles";
import { AgentHistoryList } from "./AgentHistoryList";
import { t } from "@/utils/i18n";

type RunHistorySideDrawerProps = {
  show: boolean;
  agentName: string;
  agentId: string;
  onClose: () => void;
};
export const ActivityLogSideDrawer = ({
  show,
  agentName,
  agentId,
  onClose,
}: RunHistorySideDrawerProps) => {
  return (
    <SideDrawer onCancel={onClose} show={show} title="Run History" onClose={onClose}>
      <Styled.SideDrawerHeader>
        <SideDrawer.Title>{t("components.ActivityLogSideDrawer.activityLog")}</SideDrawer.Title>
        <SideDrawer.Subtitle>{agentName}</SideDrawer.Subtitle>
        <SideDrawer.TopRight>
          <CloseButton aria-label={t("components.ActivityLogSideDrawer.close")} onClick={onClose} />
        </SideDrawer.TopRight>
      </Styled.SideDrawerHeader>
      <SideDrawer.Body>{show && <AgentHistoryList agentId={agentId} />}</SideDrawer.Body>
    </SideDrawer>
  );
};
