import { Fragment } from "react";
import { range } from "es-toolkit";
import { match, P } from "ts-pattern";
import { EmptyState, Heading, Skeleton } from "@floqastinc/flow-ui_core";
import Error from "@floqastinc/flow-ui_icons/material/Error";
import { useQuery } from "@tanstack/react-query";
import { AgentHistoryItem } from "./ui/AgentHistoryItem";
import * as Styled from "./AgentHistoryList.styles";
import { t } from "@/utils/i18n";
import { useInfiniteScrollObserver } from "@/hooks/useInfiniteScroll";
import { useInfiniteWorkflowRuns } from "@v3/runs";
import { getPrincipalsQuery } from "@BuilderV3/app-components/Audit";

export const AgentHistoryList = ({ agentId }: { agentId: string }) => {
  const agentRunsQuery = useInfiniteWorkflowRuns({
    workflowId: agentId,
    runType: "REGULAR",
  });

  const lastRowRef = useInfiniteScrollObserver({
    onIntersect: agentRunsQuery.fetchNextPage,
    isFetching: agentRunsQuery.isFetchingNextPage,
    hasMore: agentRunsQuery.hasNextPage,
  });

  const agentRunUsersIds = Array.from(
    new Set(agentRunsQuery.data?.map((run) => run.createdBy) ?? []),
  );

  const isEmptyData = agentRunsQuery.data?.length === 0;

  const principalsQuery = useQuery({
    ...getPrincipalsQuery(agentRunUsersIds),
    enabled: agentRunUsersIds.length > 0,
    select: (data) => {
      // Transform into a { [id]: name } format to allow for easier
      //  melding into the agentRuns data for display in the UI
      return data.reduce(
        (acc, principal) => {
          const displayName = match(principal)
            .with({ kind: "API_KEY" }, ({ friendlyName }) => friendlyName ?? "API Key")
            .otherwise(({ name }) => name);

          return {
            ...acc,
            [principal.id]: displayName,
          };
        },
        {} as Record<string, string>,
      );
    },
  });

  // TODO: This doesn't bring joy; Also need to find a workaround when working in standalone
  const headerNavHeight = 61;
  // TODO: This brings even less joy: it doesn't seem like we can pass a ref to
  //  SideDrawer.Header, meaning we can't get its bounding rect. So we just have to do this by-hand.
  //  Contact the team to see if we can add a ref prop, or if there's just something we're missing.
  const sidedrawerHeaderHeight = 92;
  const agentRunListHeight = window.innerHeight - headerNavHeight - sidedrawerHeaderHeight;

  const numberOfSkeletonsToRender = Math.floor(agentRunListHeight / 74);

  return (
    <Styled.AgentHistoryList>
      {match({ agentRunsQuery, principalsQuery })
        .with({ agentRunsQuery: { error: P.nonNullable } }, () => (
          <Styled.ErrorBodyContainer>
            <Error color="var(--flo-sem-color-danger-default)" size={32} />
            <Heading variant="h5">
              {t("components.AgentHistoryList.errorOccurredWhileLoadingRuns")}
            </Heading>
          </Styled.ErrorBodyContainer>
        ))
        .with(
          { agentRunsQuery: { isLoading: true } },
          { principalsQuery: { isLoading: true } },
          () => (
            <Styled.AgentHistoryList>
              {range(numberOfSkeletonsToRender).map((i) => (
                <Skeleton key={i} variant="rectangle" height="74px" width="100%" />
              ))}
            </Styled.AgentHistoryList>
          ),
        )
        .with(
          {
            agentRunsQuery: {
              isSuccess: true,
              data: P.when(() => isEmptyData),
            },
          },
          () => (
            <Styled.BodyContainer>
              <EmptyState />
              <Heading variant="h5">{t("components.AgentHistoryList.noRunsFound")}</Heading>
            </Styled.BodyContainer>
          ),
        )
        .with(
          {
            agentRunsQuery: {
              isSuccess: true,
              data: P.when(() => !isEmptyData),
            },
          },
          ({ agentRunsQuery: { data } }) =>
            data.map((agentRun, i) => (
              <Fragment key={agentRun.id}>
                <AgentHistoryItem
                  ref={i === data.length - 1 ? lastRowRef : null}
                  key={agentRun.id}
                  runBy={principalsQuery.data?.[agentRun.createdBy]}
                  status={agentRun.status}
                  createdAt={agentRun.createdAt}
                  runLink={`/runner/v3/agents/${agentId}/runs/${agentRun.id}`}
                />
              </Fragment>
            )),
        )
        .otherwise((matchState) => {
          console.error("Unexpected state: ", matchState);
          return (
            <Styled.ErrorBodyContainer>
              <Error color="var(--flo-sem-color-danger-default)" size={32} />
              <Heading variant="h5">{t("components.AgentHistoryList.unexpectedError")}</Heading>
            </Styled.ErrorBodyContainer>
          );
        })}
    </Styled.AgentHistoryList>
  );
};
