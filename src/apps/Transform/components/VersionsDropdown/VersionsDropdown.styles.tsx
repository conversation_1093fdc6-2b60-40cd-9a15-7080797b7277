import { Button, TabGroup, TH, TD, Popover } from "@floqastinc/flow-ui_core";
import { styled } from "styled-components";

export const StyledTabGroup = styled(TabGroup)`
  margin: 0 16px;
`;

export const StyledTH = styled(TH)`
  background-color: var(--flo-base-color-neutral-100);
`;

export const EmptyStateContainer = styled.div`
  padding: 16px;
  text-align: center;
`;

export const VersionIdentifier = styled.span`
  font-size: var(--flo-base-font-size-3);
  font-weight: var(--flo-base-font-weight-5);
  color: var(--flo-sem-color-text-body-tertiary);
  background-color: var(--flo-base-color-neutral-200);
  padding: 4px 6px;
  border-radius: 8px;
`;

export const VersionSelectorButton = styled(Button)`
  height: 40px;
  min-height: 40px;
  max-height: 40px;
  min-width: 40px;
`;

export const VersionSelector = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

export const VersionsDropdownHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const VersionsDropdownContent = styled.div`
  padding: 16px;
  background-color: var(--flo-base-color-neutral-0);
  border-radius: 8px;
  border: 1px solid var(--flo-base-color-neutral-200);
  box-shadow: 0px 12px 13px 0px rgba(0, 0, 0, 0.06);
`;

export const PopoverContent = styled(Popover.Content)`
  z-index: 100;
  width: 708px;
`;

export const ActionsTD = styled(TD)`
  padding: 0px 8px;
  vertical-align: middle;
`;

export const ViewingVersionTag = styled.div`
  background-color: var(--flo-sem-color-surface-info-weakest);
  color: var(--flo-sem-color-content-info-medium);
  padding: 4px 8px;
  margin: -4px 8px;
  border-radius: 4px;
  font-size: var(--flo-base-font-size-3);
  font-weight: var(--flo-base-font-weight-5);
  display: inline-block;
`;
