import { describe, test, expect, beforeEach, vi } from "vitest";
import { userEvent } from "@vitest/browser/context";
import { useNavigate } from "react-router-dom";
import { VersionsDropdown } from "./VersionsDropdown";
import { customRender } from "@/utils/testing";

// Mock the dependencies
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useNavigate: vi.fn(),
  };
});

vi.mock("@v3/workflows", () => ({
  useWorkflow: vi.fn(),
  useWorkflowVersions: vi.fn(),
  useDuplicateWorkflow: vi.fn(),
}));

vi.mock("@v0/principals", () => ({
  usePrincipals: vi.fn(),
}));

vi.mock("./VersionsDropdownContextMenu", () => ({
  VersionsDropdownContextMenu: ({ onView }: { onView: () => void }) => (
    <button data-testid="context-menu" onClick={onView}>
      Context Menu
    </button>
  ),
}));

vi.mock("@/utils/i18n", () => ({
  t: (key: string) => key, // Simple mock that returns the key
}));

// Import the mocked hooks
import { useWorkflow, useWorkflowVersions, useDuplicateWorkflow } from "@v3/workflows";
import { usePrincipals } from "@v0/principals";

const mockNavigate = vi.fn();
const mockWorkflow = vi.mocked(useWorkflow);
const mockWorkflowVersions = vi.mocked(useWorkflowVersions);
const mockDuplicateWorkflow = vi.mocked(useDuplicateWorkflow);
const mockPrincipals = vi.mocked(usePrincipals);

// Mock data
const mockWorkflowData = {
  id: "workflow-123",
  name: "Test Workflow",
  entityId: "entity-1",
  status: "ACTIVE" as const,
  description: "Test description",
  createdAt: new Date("2023-01-01"),
  updatedAt: new Date("2023-01-02"),
  createdBy: "user-1",
  updatedBy: "user-1",
  createdById: "user-1",
  updatedById: "user-1",
  settings: { formulas: false, highCapacity: false },
};

const mockActiveVersions = [
  {
    ...mockWorkflowData,
    id: "workflow-123",
    status: "ACTIVE" as const,
    version: 2,
    updatedAt: new Date("2023-01-03"),
  },
  {
    ...mockWorkflowData,
    id: "workflow-122",
    status: "ACTIVE" as const,
    version: 1,
    updatedAt: new Date("2023-01-02"),
  },
];

const mockDraftVersions = [
  {
    ...mockWorkflowData,
    id: "workflow-124",
    status: "DRAFT" as const,
    version: undefined,
    updatedAt: new Date("2023-01-04"),
  },
];

const mockPrincipalData = [
  {
    id: "user-1",
    name: "Test User",
    email: "<EMAIL>",
  },
];

describe("VersionsDropdown", () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mocks
    vi.mocked(useNavigate).mockReturnValue(mockNavigate);

    mockWorkflow.mockReturnValue({
      data: mockWorkflowData,
      isPending: false,
      error: null,
    } as any);

    mockWorkflowVersions.mockReturnValue({
      data: {
        data: [...mockActiveVersions, ...mockDraftVersions],
        pageInfo: { hasNextPage: false, hasPreviousPage: false },
        errors: [],
      },
      isPending: false,
      error: null,
    } as any);

    mockDuplicateWorkflow.mockReturnValue({
      mutateAsync: vi.fn().mockResolvedValue({ id: "new-workflow-id" }),
      isPending: false,
      error: null,
    } as any);

    mockPrincipals.mockReturnValue({
      data: mockPrincipalData,
      isPending: false,
      error: null,
    } as any);
  });

  test("GIVEN VersionsDropdown WHEN rendered THEN it should display the version button", async () => {
    const screen = customRender(<VersionsDropdown workflowId="workflow-123" />);

    // Should show the version selector button
    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();

    // Should show version number for active workflow
    await expect.element(screen.getByText("V2")).toBeInTheDocument();
  });

  test("GIVEN VersionsDropdown WHEN workflowId is draft THEN it should display draft label", async () => {
    mockWorkflow.mockReturnValue({
      data: { ...mockWorkflowData, id: "workflow-124", status: "DRAFT" },
      isPending: false,
      error: null,
    } as any);

    const screen = customRender(<VersionsDropdown workflowId="workflow-124" />);

    await expect.element(screen.getByText("components.VersionsDropdown.draft")).toBeInTheDocument();
  });

  test("GIVEN VersionsDropdown WHEN loading THEN it should show skeleton loader", async () => {
    mockWorkflowVersions.mockReturnValue({
      data: null,
      isPending: true,
      error: null,
    } as any);

    const screen = customRender(<VersionsDropdown workflowId="workflow-123" />);

    const button = screen.getByRole("button");
    await userEvent.click(button);

    // Should show loading skeleton in the button
    expect(screen.container.querySelector(".react-loading-skeleton")).toBeInTheDocument();
  });

  test("GIVEN VersionsDropdown WHEN button clicked THEN it should open popover with versions", async () => {
    const screen = customRender(<VersionsDropdown workflowId="workflow-123" />);

    const button = screen.getByRole("button");
    await userEvent.click(button);

    // Should show the versions header
    await expect
      .element(screen.getByText("components.VersionsDropdown.versions"))
      .toBeInTheDocument();

    // Should show the new version button
    await expect
      .element(screen.getByText("components.VersionsDropdown.newVersion"))
      .toBeInTheDocument();

    // Should show tabs
    await expect
      .element(screen.getByText("components.VersionsDropdown.published"))
      .toBeInTheDocument();
    await expect.element(screen.getByText("components.VersionsDropdown.draft")).toBeInTheDocument();
  });

  test("GIVEN VersionsDropdown WHEN New Version button is pending THEN it should show loading state", async () => {
    mockDuplicateWorkflow.mockReturnValue({
      mutateAsync: vi.fn(),
      isPending: true,
      error: null,
    } as any);

    const screen = customRender(<VersionsDropdown workflowId="workflow-123" />);

    const button = screen.getByRole("button");
    await userEvent.click(button);

    const newVersionButton = screen.getByText("components.VersionsDropdown.newVersion");
    expect(newVersionButton).toBeDisabled();
  });

  test("GIVEN VersionsDropdown WHEN no published versions exist THEN it should show empty state", async () => {
    mockWorkflowVersions.mockReturnValue({
      data: {
        data: mockDraftVersions, // Only draft versions
        pageInfo: { hasNextPage: false, hasPreviousPage: false },
        errors: [],
      },
      isPending: false,
      error: null,
    } as any);

    const screen = customRender(<VersionsDropdown workflowId="workflow-123" />);

    const button = screen.getByRole("button");
    await userEvent.click(button);

    // Published tab should be active but show empty state
    await expect
      .element(screen.getByText("components.VersionsDropdown.noVersionsFound"))
      .toBeInTheDocument();
  });

  test("GIVEN VersionsDropdown WHEN no draft versions exist THEN it should show empty state on draft tab", async () => {
    mockWorkflowVersions.mockReturnValue({
      data: {
        data: mockActiveVersions, // Only active versions
        pageInfo: { hasNextPage: false, hasPreviousPage: false },
        errors: [],
      },
      isPending: false,
      error: null,
    } as any);

    const screen = customRender(<VersionsDropdown workflowId="workflow-123" />);

    const button = screen.getByRole("button");
    await userEvent.click(button);

    // Click on Draft tab
    const draftTab = screen.getByText("components.VersionsDropdown.draft");
    await userEvent.click(draftTab);

    await expect
      .element(screen.getByText("components.VersionsDropdown.noVersionsFound"))
      .toBeInTheDocument();
  });

  test("GIVEN VersionsDropdown WHEN viewing current version THEN it should show viewing indicator", async () => {
    const screen = customRender(<VersionsDropdown workflowId="workflow-123" />);

    const button = screen.getByRole("button");
    await userEvent.click(button);

    // Should show "Viewing" indicator for current workflow
    await expect
      .element(screen.getByText("components.VersionsDropdown.viewing"))
      .toBeInTheDocument();
  });

  test("GIVEN VersionsDropdown WHEN workflowId is empty THEN useWorkflowVersions should be disabled", async () => {
    customRender(<VersionsDropdown workflowId="" />);

    expect(mockWorkflowVersions).toHaveBeenCalledWith(
      expect.objectContaining({
        workflowId: "",
      }),
      expect.objectContaining({
        enabled: false,
      }),
    );
  });
});
