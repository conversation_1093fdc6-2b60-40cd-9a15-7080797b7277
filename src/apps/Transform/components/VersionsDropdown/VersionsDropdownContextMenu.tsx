import { DropdownPanel, But<PERSON> } from "@floqastinc/flow-ui_core";
import MoreVert from "@floqastinc/flow-ui_icons/material/MoreVert";
import { match } from "ts-pattern";
import { t } from "@/utils/i18n";

const VIEW = "view";

export const VersionsDropdownContextMenu = ({ onView }: { onView: () => void }) => {
  return (
    <DropdownPanel
      disableFilter
      disableClear
      onChange={(value: string) => {
        match(value).with(VIEW, () => onView());
      }}
    >
      <DropdownPanel.Trigger>
        <Button variant="ghost" color="dark" size="md">
          <MoreVert color="#1D2433" />
        </Button>
      </DropdownPanel.Trigger>
      <DropdownPanel.Content
        align="end"
        size="md"
        style={{
          zIndex: 101,
        }}
      >
        <DropdownPanel.Option value={VIEW}>
          {t("components.VersionsDropdown.view")}
        </DropdownPanel.Option>
      </DropdownPanel.Content>
    </DropdownPanel>
  );
};
