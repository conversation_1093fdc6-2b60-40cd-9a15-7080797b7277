import { Popover, Heading, Tab, Table, TBody, TD, THead, TR } from "@floqastinc/flow-ui_core";
import Layers from "@floqastinc/flow-ui_icons/material/LayersOutlined";
import { Workflow, WorkflowStatus, WorkflowWithVersion } from "@floqastinc/transform-v3";
import { useEffect, useMemo, useState } from "react";
import Skeleton from "react-loading-skeleton";
import { match } from "ts-pattern";
import { useNavigate } from "react-router-dom";
import { uniq } from "es-toolkit";
import * as Styled from "./VersionsDropdown.styles";
import { VersionsDropdownContextMenu } from "./VersionsDropdownContextMenu";
import { useDuplicateWorkflow, useWorkflow, useWorkflowVersions } from "@v3/workflows";
import { t } from "@/utils/i18n";
import { AGENTS, BUILDER, V3 } from "@/constants";
import { usePrincipals } from "@v0/principals";
import { getPrincipalDisplayName } from "@/utils/getPrincipalDisplayName";
import { SpinnerButton } from "@/components/SpinnerButton";

const PUBLISHED = "ACTIVE";
const DRAFT = "DRAFT";

const getVersionButtonContent = (workflowId: string, allVersions: WorkflowWithVersion[]) => {
  const version = allVersions.find((v) => v.id === workflowId);

  if (!version) {
    return null;
  }

  if (version.status === "DRAFT") {
    return t("components.VersionsDropdown.draft");
  }

  return `V${version.version}`;
};

const formatVersionDate = (version: Workflow) => {
  const options: Intl.DateTimeFormatOptions = {
    month: "short",
    day: "numeric",
    year: "numeric",
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  };

  return Intl.DateTimeFormat("en-US", options).format(new Date(version.updatedAt));
};

export const VersionsDropdown = ({ workflowId }: { workflowId: string }) => {
  const [status, setStatus] = useState<WorkflowStatus>("ACTIVE");
  const navigate = useNavigate();
  const duplicateWorkflow = useDuplicateWorkflow();
  const workflow = useWorkflow({ workflowId: workflowId ?? "" });
  const { data: versions, isPending: isVersionsPending } = useWorkflowVersions(
    {
      workflowId,
      first: 20, //@todo: add pagination to UI
    },
    {
      enabled: !!workflowId,
    },
  );

  const sortedVersions = useMemo(() => {
    return versions?.data?.sort(
      (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
    );
  }, [versions]);

  const updatedByIds = useMemo(
    () =>
      uniq(
        sortedVersions?.map((version) => version.updatedBy).filter((x) => x !== undefined) ?? [],
      ),
    [sortedVersions],
  );

  const principals = usePrincipals(
    {
      ids: updatedByIds,
    },
    {
      enabled: updatedByIds.length > 0,
    },
  );

  const updatedByNames = useMemo(() => {
    const updatedByMap = new Map<string, string>();
    principals.data?.forEach((principal) => {
      updatedByMap.set(principal.id, getPrincipalDisplayName(principal) ?? principal.id);
    });
    return updatedByMap;
  }, [principals.data]);

  const handleNewVersionClick = async () => {
    const newWorkflow = await duplicateWorkflow.mutateAsync({
      workflowId: workflowId,
    });

    navigate(`/${BUILDER}/${V3}/${AGENTS}/${newWorkflow.id}`);
  };

  useEffect(() => {
    if (workflow.data?.id) {
      setStatus(workflow.data.status);
    }
  }, [workflow.data?.id, workflow.data?.status]);

  const activeVersions = useMemo(() => {
    return sortedVersions?.filter((version) => version.status === "ACTIVE");
  }, [sortedVersions]);

  const draftVersions = useMemo(() => {
    return sortedVersions?.filter((version) => version.status === "DRAFT");
  }, [sortedVersions]);

  const hasData = useMemo(() => {
    if (status === "ACTIVE") {
      return (activeVersions?.length ?? 0) > 0;
    }
    return (draftVersions?.length ?? 0) > 0;
  }, [activeVersions, draftVersions, status]);

  return (
    <Popover>
      <Popover.Trigger>
        <Styled.VersionSelectorButton variant="outlined" color="dark" size="md">
          <Styled.VersionSelector>
            <Layers height={24} width={24} color="var(--flo-sem-color-icon-tertiary)" />
            <Styled.VersionIdentifier>
              {isVersionsPending ? (
                <Skeleton width={24} />
              ) : (
                getVersionButtonContent(workflowId, versions?.data ?? [])
              )}
            </Styled.VersionIdentifier>
          </Styled.VersionSelector>
        </Styled.VersionSelectorButton>
      </Popover.Trigger>
      <Styled.PopoverContent align="end">
        <Styled.VersionsDropdownContent>
          <Styled.VersionsDropdownHeader>
            <Heading variant="h5">{t("components.VersionsDropdown.versions")}</Heading>
            <SpinnerButton
              variant="filled"
              color="primary"
              size="md"
              onClick={handleNewVersionClick}
              isPending={duplicateWorkflow.isPending}
              disabled={duplicateWorkflow.isPending}
            >
              {t("components.VersionsDropdown.newVersion")}
            </SpinnerButton>
          </Styled.VersionsDropdownHeader>
          <>
            <Styled.StyledTabGroup
              defaultValue={status}
              onValueChange={(tabId: string) => setStatus(tabId as WorkflowStatus)}
            >
              <Tab title={t("components.VersionsDropdown.published")} tabId={PUBLISHED} />
              <Tab title={t("components.VersionsDropdown.drafts")} tabId={DRAFT} />
            </Styled.StyledTabGroup>
            <div>
              {match({
                isLoading: isVersionsPending,
                hasData,
                status,
              })
                .with({ isLoading: true }, () => <Skeleton width="100%" height={40} />)
                .with({ isLoading: false, hasData: true, status: "DRAFT" }, () => (
                  <Table middleBorders outerRoundedBorder topBorder>
                    <THead>
                      <TR>
                        <Styled.StyledTH>
                          {t("components.VersionsDropdown.modified")}
                        </Styled.StyledTH>
                        <Styled.StyledTH>
                          {t("components.VersionsDropdown.modifiedBy")}
                        </Styled.StyledTH>
                        <Styled.StyledTH>
                          {t("components.VersionsDropdown.comments")}
                        </Styled.StyledTH>
                        <Styled.StyledTH></Styled.StyledTH>
                      </TR>
                    </THead>
                    <TBody>
                      {draftVersions?.map((version) => {
                        const isViewingVersion = version.id === workflow.data?.id;
                        return (
                          <TR key={version.id}>
                            <TD>
                              {formatVersionDate(version)}
                              {isViewingVersion && version.status === "DRAFT" && (
                                <Styled.ViewingVersionTag>
                                  {t("components.VersionsDropdown.viewing")}
                                </Styled.ViewingVersionTag>
                              )}
                            </TD>
                            <TD>{updatedByNames.get(version.updatedBy ?? "")}</TD>
                            <TD>{version.description}</TD>
                            <Styled.ActionsTD>
                              <VersionsDropdownContextMenu
                                onView={() => {
                                  navigate(`/${BUILDER}/${V3}/${AGENTS}/${version.id}`);
                                }}
                              />
                            </Styled.ActionsTD>
                          </TR>
                        );
                      })}
                    </TBody>
                  </Table>
                ))
                .with({ isLoading: false, hasData: true, status: "ACTIVE" }, () => (
                  <Table middleBorders outerRoundedBorder topBorder>
                    <THead>
                      <TR>
                        <Styled.StyledTH>{t("components.VersionsDropdown.number")}</Styled.StyledTH>
                        <Styled.StyledTH>
                          {t("components.VersionsDropdown.modified")}
                        </Styled.StyledTH>
                        <Styled.StyledTH>
                          {t("components.VersionsDropdown.modifiedBy")}
                        </Styled.StyledTH>
                        <Styled.StyledTH>
                          {t("components.VersionsDropdown.comments")}
                        </Styled.StyledTH>
                        <Styled.StyledTH></Styled.StyledTH>
                      </TR>
                    </THead>
                    <TBody>
                      {activeVersions?.map((version) => {
                        const isViewingVersion = version.id === workflow.data?.id;
                        return (
                          <TR key={version.id}>
                            <TD>
                              {version.version}
                              {isViewingVersion && (
                                <Styled.ViewingVersionTag>
                                  {t("components.VersionsDropdown.viewing")}
                                </Styled.ViewingVersionTag>
                              )}
                            </TD>
                            <TD>{formatVersionDate(version)}</TD>
                            <TD>{updatedByNames.get(version.updatedBy ?? "")}</TD>
                            <TD>{version.description}</TD>
                            <Styled.ActionsTD>
                              <VersionsDropdownContextMenu
                                onView={() => {
                                  navigate(`/${BUILDER}/${V3}/${AGENTS}/${version.id}`);
                                }}
                              />
                            </Styled.ActionsTD>
                          </TR>
                        );
                      })}
                    </TBody>
                  </Table>
                ))
                .otherwise(() => (
                  <Styled.EmptyStateContainer>
                    {t("components.VersionsDropdown.noVersionsFound")}
                  </Styled.EmptyStateContainer>
                ))}
            </div>
          </>
        </Styled.VersionsDropdownContent>
      </Styled.PopoverContent>
    </Popover>
  );
};
