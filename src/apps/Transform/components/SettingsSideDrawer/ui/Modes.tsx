import { Heading, Toggle, Spinner } from "@floqastinc/flow-ui_core";
import { Workflow, WorkflowSetting } from "@floqastinc/transform-v3";
import { useQuery } from "@tanstack/react-query";
import * as Styled from "../SettingsSideDrawer.styles";
import { t } from "@/utils/i18n";
import v3, { ApiError } from "@/services/v3";

interface ModesProps {
  settings: Workflow["settings"];
  setSettings: (name: keyof Workflow["settings"], value: boolean) => void;
}

interface Setting extends WorkflowSetting {
  name: keyof Workflow["settings"];
}

export function Modes({ settings, setSettings }: ModesProps) {
  const { data: allSettings = [], isLoading } = useQuery<Setting[]>({
    queryKey: ["settings", "list"],
    queryFn: async () => {
      const response = await v3.settings.getSettings();
      if (response.errors.length) {
        throw new ApiError(response.errors);
      }
      return response.data as Setting[];
    },
    retry: false,
  });

  return (
    <Styled.SettingsList>
      <Heading variant="h4">{t("components.CreateAgent.modes")}</Heading>
      {isLoading ? (
        <Spinner />
      ) : (
        allSettings.map(({ name, label, description }) => (
          <Toggle
            key={name}
            checked={settings[name]}
            label={label}
            sublabel={description}
            onChange={(value: boolean) => setSettings(name, value)}
            styleOverrides={{
              label: {
                display: "flex",
                alignItems: "flex-start",
              },
            }}
          />
        ))
      )}
    </Styled.SettingsList>
  );
}
