import { Children, isValidElement } from "react";
import Markdown from "react-markdown";
import { Heading, Skeleton } from "@floqastinc/flow-ui_core";
import * as Styled from "./styles";
import { t } from "@/utils/i18n";

export type SubStep = { heading: string; details: string };

export const DetailStepListSkeleton = () =>
  [1, 2, 3, 4, 5].map((i) => (
    <Styled.DetailStep key={i}>
      <Styled.DetailStepName>
        <Skeleton lines={1} width="50%" />
      </Styled.DetailStepName>
      <Styled.DetailStepDescription>
        <Skeleton lines={4} width="100%" />
      </Styled.DetailStepDescription>
    </Styled.DetailStep>
  ));

const Li = ({
  children,
  ...rest
}: React.DetailedHTMLProps<React.LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>) => {
  type ParagraphProps = { children: React.ReactNode };
  const children_: React.ReactNode[] = [];
  Children.forEach(children, (child) => {
    if (isValidElement(child) && child.type === "p") {
      children_.push((child as React.ReactElement<ParagraphProps>).props.children);
    } else {
      children_.push(child);
    }
  });
  return <li {...rest}>{children_}</li>;
};

type DetailStepListProps = {
  steps: SubStep[];
  isLoading: boolean;
};

export const DetailStepList = ({ steps, isLoading }: DetailStepListProps) => {
  if (isLoading) {
    return <DetailStepListSkeleton />;
  }

  return (
    <>
      <Styled.DetailSteps>
        {steps.map((subStep, i) => (
          <Styled.DetailStep key={subStep.heading}>
            <Styled.DetailStepName>
              <Heading variant="h5" weight="medium">
                {t("components.DetailSteps.subStepHeading", {
                  index: i + 1,
                  heading: subStep.heading,
                })}
              </Heading>
            </Styled.DetailStepName>
            <Styled.DetailStepDescription>
              <Markdown
                components={{
                  li({ node: _node, ...rest }) {
                    return <Li {...rest} />;
                  },
                }}
              >
                {subStep.details}
              </Markdown>
            </Styled.DetailStepDescription>
          </Styled.DetailStep>
        ))}
      </Styled.DetailSteps>
    </>
  );
};
