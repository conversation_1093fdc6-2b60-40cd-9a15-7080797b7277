import { WorkflowInput } from "@floqastinc/transform-v3";
import { useCallback, useState } from "react";
import * as styled from "./index.styled";
import { useFeatureFlags } from "@/components/FeatureFlag";
import {
  InputForm,
  InputFormData,
} from "@Transform/pages/builder/ui/BuilderSectionTabs/InputsTabSection/components/InputForm";
import { InputCard } from "@Transform/pages/builder/ui/BuilderSectionTabs/InputsTabSection/components/InputCard";
import { getInputValue } from "@/utils/inputs";

interface InputsListProps {
  inputs: WorkflowInput[];
  onExistingInputSave: (input: WorkflowInput) => void;
  onOpenFile: (fileId: string) => void;
  onEditFile?: (fileId: string) => void;
}

export const InputsList = ({
  inputs,
  onExistingInputSave,
  onOpenFile,
  onEditFile,
}: InputsListProps) => {
  const { getFlag } = useFeatureFlags();
  const [currentlyEditingInput, setCurrentlyEditingInput] = useState<
    (InputFormData & { id: string }) | null
  >(null);

  const handleExistingInputChange = useCallback(
    (input: Partial<InputFormData>) => {
      if (currentlyEditingInput?.id) {
        setCurrentlyEditingInput((prev) => ({
          ...(prev as InputFormData),
          ...input,
          id: currentlyEditingInput.id,
        }));
      }
    },
    [currentlyEditingInput?.id],
  );

  const handleExistingInputSave = useCallback(
    (input: InputFormData) => {
      if (currentlyEditingInput?.id) {
        onExistingInputSave({
          ...input,
          ...currentlyEditingInput,
          value: {
            kind: currentlyEditingInput.type,
            value: currentlyEditingInput.value ?? undefined,
          },
        });
        setCurrentlyEditingInput(null);
      }
    },
    [currentlyEditingInput, onExistingInputSave],
  );

  return (
    <>
      {(inputs ?? []).map((input) =>
        currentlyEditingInput?.id === input.id ? (
          <InputForm
            key={input.id}
            title={input.name}
            input={currentlyEditingInput}
            mode="edit"
            inputTypeSelectDisabled
            onSave={handleExistingInputSave}
            onChange={handleExistingInputChange}
            onCancel={() => {
              setCurrentlyEditingInput(null);
            }}
          />
        ) : (
          <InputCard
            key={input.id}
            name={input.name}
            type={input.type}
            secondaryText={`${getInputValue(input) ?? ""}`}
            actions={() => {
              return (
                <styled.ButtonContainer>
                  {getFlag("enable-builder-input-editing") ? (
                    <styled.Button
                      variant="ghost"
                      color="dark"
                      size="sm"
                      onClick={() => {
                        onEditFile?.(input.id);
                        setCurrentlyEditingInput({
                          ...input,
                          value: getInputValue(input),
                        });
                      }}
                    >
                      Edit
                    </styled.Button>
                  ) : null}
                  {input.type === "FILE" ? (
                    <styled.Button
                      variant="outlined"
                      color="dark"
                      size="sm"
                      onClick={() => {
                        onOpenFile(input.id);
                      }}
                    >
                      Open
                    </styled.Button>
                  ) : null}
                </styled.ButtonContainer>
              );
            }}
          />
        ),
      )}
    </>
  );
};
