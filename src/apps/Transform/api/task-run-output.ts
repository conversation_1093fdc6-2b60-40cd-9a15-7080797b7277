import { useQuery } from "@tanstack/react-query";
import v3 from "@/services/v3";
import { getFileFromUri } from "@BuilderV3/api/files";

export const useTaskRunPreviewFile = (
  workflowRunId: string,
  taskRunId: string,
  fileName: string,
) => {
  return useQuery({
    queryKey: ["taskRunPreviewFile", workflowRunId, taskRunId],
    queryFn: async () => {
      if (!taskRunId) return null;

      try {
        // First try to get the output file
        const taskRunOutputsRes = await v3.runs.getTaskRunOutputs({
          workflowRunId,
          taskRunId,
        });

        if (taskRunOutputsRes.data.length > 0) {
          const taskRunOutput = taskRunOutputsRes.data[0];
          try {
            const taskRunOutputRes = await v3.runs.getTaskRunOutputValueUri({
              workflowRunId,
              taskRunId,
              taskRunOutputId: taskRunOutput.id,
            });

            if (!taskRunOutputRes.errors.length && taskRunOutputRes.data) {
              const file = await getFileFromUri(taskRunOutputRes.data, { name: fileName });
              return { file, type: "Output" };
            }
          } catch {
            // Output URI fetch failed, try to get the input file
          }
        }

        const taskRunInputsRes = await v3.runs.getTaskRunInputs({
          workflowRunId,
          taskRunId,
        });

        if (taskRunInputsRes.data.length > 0) {
          const taskRunInput = taskRunInputsRes.data[0];
          try {
            const taskRunInputRes = await v3.runs.getTaskRunInputValueUri({
              workflowRunId,
              taskRunId,
              taskRunInputId: taskRunInput.id,
            });

            if (!taskRunInputRes.errors.length && taskRunInputRes.data) {
              const file = await getFileFromUri(taskRunInputRes.data, { name: fileName });
              return { file, type: "Input" };
            }
          } catch {
            // Input URI fetch failed, return null
          }
        }

        return null;
      } catch (error) {
        console.error("Error fetching preview file:", error);
        return null;
      }
    },
    enabled: !!workflowRunId && !!taskRunId,
    staleTime: 1000 * 30, // 30 seconds
    gcTime: 1000 * 60, // 1 minute
  });
};
