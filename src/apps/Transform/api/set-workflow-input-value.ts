import v3 from "@/services/v3";
import {
  ArgumentValue,
  CreateWorkflowInputParams,
  DatetimeArgument,
  FileArgument,
  TextArgument,
} from "@floqastinc/transform-v3";
import axios from "axios";

type FileArgumentWithValue = FileArgument & {
  value: File;
};

/**
 * Sets the value of a workflow input. Based on the type of the input, the appropriate API call is made.
 *
 * @param param0 - The parameters for setting the workflow input value.
 * @returns A promise that resolves when the workflow input value has been set.
 */
export const setWorkflowInputValue = async ({
  workflowId,
  workflowInputId,
  input,
}: {
  workflowId: string;
  workflowInputId: string;
  input: CreateWorkflowInputParams["input"] & {
    value: ArgumentValue | FileArgumentWithValue;
  };
}) => {
  switch (input.type) {
    case "TEXT":
      return v3.workflowInputs.setWorkflowInputText({
        workflowId,
        workflowInputId,
        value: input.value as TextArgument,
      });
    case "DATETIME":
      return v3.workflowInputs.setWorkflowInputDatetime({
        workflowId,
        workflowInputId,
        value: {
          kind: "DATETIME",
          value: new Date((input.value as DatetimeArgument).value),
        },
      });
    case "FILE":
      if (!(input.value as FileArgumentWithValue).value) {
        throw new Error("File input value is required for FILE type inputs");
      }

      const fileValue = (input.value as FileArgumentWithValue).value;

      const setFileReponse = await v3.workflowInputs.setWorkflowInputFile({
        workflowId,
        workflowInputId,
        value: {
          kind: "FILE",
          mimetype: fileValue.type,
          name: fileValue.name,
        },
      });

      if (!setFileReponse.data) {
        throw new Error(
          "Unexpected error: Failed to set workflow input file value, but no errors were generated",
        );
      }

      const { url } = setFileReponse.data;
      const s3Res = await axios.put(url, fileValue, {
        headers: {
          "Content-Type": fileValue.type,
        },
      });

      if (s3Res.status !== 200) {
        throw new Error("Failed to upload file: " + s3Res.statusText);
      }

      break;
    default:
      console.warn(`Unsupported input type for setting value: ${input.type}`);
      break;
  }
};
