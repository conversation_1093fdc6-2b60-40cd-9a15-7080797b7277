export const CtrlE = () => {
  return (
    <svg width="39" height="16" viewBox="0 0 39 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="21" height="16" rx="2" fill="#6B7280" />
      <path
        d="M5.28125 12.125C4.71875 12.125 4.23438 11.9922 3.82813 11.7266C3.42188 11.4609 3.10938 11.0951 2.89063 10.6289C2.67188 10.1628 2.5625 9.63021 2.5625 9.03125C2.5625 8.42187 2.67448 7.88411 2.89844 7.41797C3.125 6.94922 3.4401 6.58333 3.84375 6.32031C4.25 6.05469 4.72396 5.92188 5.26562 5.92188C5.6875 5.92188 6.06771 6 6.40625 6.15625C6.74479 6.3125 7.02214 6.53125 7.23828 6.8125C7.45443 7.09375 7.58854 7.42187 7.64063 7.79687H6.71875C6.64844 7.52344 6.49219 7.28125 6.25 7.07031C6.01042 6.85677 5.6875 6.75 5.28125 6.75C4.92188 6.75 4.60677 6.84375 4.33594 7.03125C4.06771 7.21615 3.85807 7.47786 3.70703 7.81641C3.55859 8.15234 3.48438 8.54687 3.48438 9C3.48438 9.46354 3.55729 9.86719 3.70313 10.2109C3.85156 10.5547 4.0599 10.8216 4.32813 11.0117C4.59896 11.2018 4.91667 11.2969 5.28125 11.2969C5.52083 11.2969 5.73828 11.2552 5.93359 11.1719C6.12891 11.0885 6.29427 10.9688 6.42969 10.8125C6.5651 10.6562 6.66146 10.4687 6.71875 10.25H7.64063C7.58854 10.6042 7.45964 10.9232 7.25391 11.207C7.05078 11.4883 6.78125 11.7122 6.44531 11.8789C6.11198 12.043 5.72396 12.125 5.28125 12.125ZM11.5977 6V6.78125H8.48828V6H11.5977ZM9.39453 4.5625H10.3164V10.2812C10.3164 10.5417 10.3542 10.737 10.4297 10.8672C10.5078 10.9948 10.6068 11.0807 10.7266 11.125C10.849 11.1667 10.9779 11.1875 11.1133 11.1875C11.2148 11.1875 11.2982 11.1823 11.3633 11.1719C11.4284 11.1589 11.4805 11.1484 11.5195 11.1406L11.707 11.9687C11.6445 11.9922 11.5573 12.0156 11.4453 12.0391C11.3333 12.0651 11.1914 12.0781 11.0195 12.0781C10.7591 12.0781 10.5039 12.0221 10.2539 11.9102C10.0065 11.7982 9.80078 11.6276 9.63672 11.3984C9.47526 11.1693 9.39453 10.8802 9.39453 10.5312V4.5625ZM12.9844 12V6H13.875V6.90625H13.9375C14.0469 6.60938 14.2448 6.36849 14.5313 6.18359C14.8177 5.9987 15.1406 5.90625 15.5 5.90625C15.5677 5.90625 15.6523 5.90755 15.7539 5.91016C15.8555 5.91276 15.9323 5.91667 15.9844 5.92188V6.85937C15.9531 6.85156 15.8815 6.83984 15.7695 6.82422C15.6602 6.80599 15.5443 6.79687 15.4219 6.79687C15.1302 6.79687 14.8698 6.85807 14.6406 6.98047C14.4141 7.10026 14.2344 7.26693 14.1016 7.48047C13.9714 7.69141 13.9063 7.93229 13.9063 8.20312V12H12.9844ZM17.999 4V12H17.0771V4H17.999Z"
        fill="white"
      />
      <rect x="23" width="16" height="16" rx="2" fill="#6B7280" />
      <path
        d="M31.1582 12.125C30.5801 12.125 30.0814 11.9974 29.6621 11.7422C29.2454 11.4844 28.9238 11.125 28.6973 10.6641C28.4733 10.2005 28.3613 9.66146 28.3613 9.04688C28.3613 8.43229 28.4733 7.89062 28.6973 7.42187C28.9238 6.95052 29.2389 6.58333 29.6426 6.32031C30.0488 6.05469 30.5228 5.92188 31.0645 5.92188C31.377 5.92188 31.6855 5.97396 31.9902 6.07812C32.2949 6.18229 32.5723 6.35156 32.8223 6.58594C33.0723 6.81771 33.2715 7.125 33.4199 7.50781C33.5684 7.89062 33.6426 8.36198 33.6426 8.92187V9.3125H29.0176V8.51562H32.7051C32.7051 8.17708 32.6374 7.875 32.502 7.60937C32.3691 7.34375 32.179 7.13411 31.9316 6.98047C31.6868 6.82682 31.3978 6.75 31.0645 6.75C30.6973 6.75 30.3796 6.84115 30.1113 7.02344C29.8457 7.20312 29.6413 7.4375 29.498 7.72656C29.3548 8.01562 29.2832 8.32552 29.2832 8.65625V9.1875C29.2832 9.64063 29.3613 10.0247 29.5176 10.3398C29.6764 10.6523 29.8965 10.8906 30.1777 11.0547C30.459 11.2161 30.7858 11.2969 31.1582 11.2969C31.4004 11.2969 31.6191 11.263 31.8145 11.1953C32.0124 11.125 32.1829 11.0208 32.3262 10.8828C32.4694 10.7422 32.5801 10.5677 32.6582 10.3594L33.5488 10.6094C33.4551 10.9115 33.2975 11.1771 33.0762 11.4062C32.8548 11.6328 32.5814 11.8099 32.2559 11.9375C31.9303 12.0625 31.5645 12.125 31.1582 12.125Z"
        fill="white"
      />
    </svg>
  );
};
