export const AddStep = () => (
  <svg width="48" height="26" viewBox="0 0 48 26" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect y="11" width="48" height="2" rx="1" fill="#6B7280" />
    <g filter="url(#filter0_d_8180_75188)">
      <g clipPath="url(#clip0_8180_75188)">
        <rect x="15" y="3" width="18" height="18" rx="9" fill="#6B7280" />
        <mask id="mask0_8180_75188" maskUnits="userSpaceOnUse" x="15" y="3" width="18" height="18">
          <rect x="15" y="3" width="18" height="18" fill="white" />
        </mask>
        <g mask="url(#mask0_8180_75188)">
          <path
            d="M23.25 12.75H18.75V11.25H23.25V6.75H24.75V11.25H29.25V12.75H24.75V17.25H23.25V12.75Z"
            fill="white"
          />
        </g>
      </g>
      <rect x="14.1" y="2.1" width="19.8" height="19.8" rx="9.9" stroke="white" strokeWidth="1.8" />
    </g>
    <defs>
      <filter
        id="filter0_d_8180_75188"
        x="11.4"
        y="0.3"
        width="25.2"
        height="25.2"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="0.9" />
        <feGaussianBlur stdDeviation="0.9" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0" />
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8180_75188" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_8180_75188"
          result="shape"
        />
      </filter>
      <clipPath id="clip0_8180_75188">
        <rect x="15" y="3" width="18" height="18" rx="9" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
