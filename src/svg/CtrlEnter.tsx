export const CtrlEnter = () => {
  return (
    <svg width="55" height="16" viewBox="0 0 55 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="21" height="16" rx="2" fill="#6B7280" />
      <path
        d="M5.28125 12.125C4.71875 12.125 4.23438 11.9922 3.82813 11.7266C3.42188 11.4609 3.10938 11.0951 2.89063 10.6289C2.67188 10.1628 2.5625 9.63021 2.5625 9.03125C2.5625 8.42187 2.67448 7.88411 2.89844 7.41797C3.125 6.94922 3.4401 6.58333 3.84375 6.32031C4.25 6.05469 4.72396 5.92188 5.26562 5.92188C5.6875 5.92188 6.06771 6 6.40625 6.15625C6.74479 6.3125 7.02214 6.53125 7.23828 6.8125C7.45443 7.09375 7.58854 7.42187 7.64063 7.79687H6.71875C6.64844 7.52344 6.49219 7.28125 6.25 7.07031C6.01042 6.85677 5.6875 6.75 5.28125 6.75C4.92188 6.75 4.60677 6.84375 4.33594 7.03125C4.06771 7.21615 3.85807 7.47786 3.70703 7.81641C3.55859 8.15234 3.48438 8.54687 3.48438 9C3.48438 9.46354 3.55729 9.86719 3.70313 10.2109C3.85156 10.5547 4.0599 10.8216 4.32813 11.0117C4.59896 11.2018 4.91667 11.2969 5.28125 11.2969C5.52083 11.2969 5.73828 11.2552 5.93359 11.1719C6.12891 11.0885 6.29427 10.9688 6.42969 10.8125C6.5651 10.6562 6.66146 10.4687 6.71875 10.25H7.64063C7.58854 10.6042 7.45964 10.9232 7.25391 11.207C7.05078 11.4883 6.78125 11.7122 6.44531 11.8789C6.11198 12.043 5.72396 12.125 5.28125 12.125ZM11.5977 6V6.78125H8.48828V6H11.5977ZM9.39453 4.5625H10.3164V10.2812C10.3164 10.5417 10.3542 10.737 10.4297 10.8672C10.5078 10.9948 10.6068 11.0807 10.7266 11.125C10.849 11.1667 10.9779 11.1875 11.1133 11.1875C11.2148 11.1875 11.2982 11.1823 11.3633 11.1719C11.4284 11.1589 11.4805 11.1484 11.5195 11.1406L11.707 11.9687C11.6445 11.9922 11.5573 12.0156 11.4453 12.0391C11.3333 12.0651 11.1914 12.0781 11.0195 12.0781C10.7591 12.0781 10.5039 12.0221 10.2539 11.9102C10.0065 11.7982 9.80078 11.6276 9.63672 11.3984C9.47526 11.1693 9.39453 10.8802 9.39453 10.5312V4.5625ZM12.9844 12V6H13.875V6.90625H13.9375C14.0469 6.60938 14.2448 6.36849 14.5313 6.18359C14.8177 5.9987 15.1406 5.90625 15.5 5.90625C15.5677 5.90625 15.6523 5.90755 15.7539 5.91016C15.8555 5.91276 15.9323 5.91667 15.9844 5.92188V6.85937C15.9531 6.85156 15.8815 6.83984 15.7695 6.82422C15.6602 6.80599 15.5443 6.79687 15.4219 6.79687C15.1302 6.79687 14.8698 6.85807 14.6406 6.98047C14.4141 7.10026 14.2344 7.26693 14.1016 7.48047C13.9714 7.69141 13.9063 7.93229 13.9063 8.20312V12H12.9844ZM17.999 4V12H17.0771V4H17.999Z"
        fill="white"
      />
      <rect x="23" width="32" height="16" rx="2" fill="#6B7280" />
      <path
        d="M28.7275 12.125C28.1494 12.125 27.6507 11.9974 27.2314 11.7422C26.8148 11.4844 26.4932 11.125 26.2666 10.6641C26.0426 10.2005 25.9307 9.66146 25.9307 9.04688C25.9307 8.43229 26.0426 7.89062 26.2666 7.42187C26.4932 6.95052 26.8083 6.58333 27.2119 6.32031C27.6182 6.05469 28.0921 5.92188 28.6338 5.92188C28.9463 5.92188 29.2549 5.97396 29.5596 6.07812C29.8643 6.18229 30.1416 6.35156 30.3916 6.58594C30.6416 6.81771 30.8408 7.125 30.9893 7.50781C31.1377 7.89062 31.2119 8.36198 31.2119 8.92187V9.3125H26.5869V8.51562H30.2744C30.2744 8.17708 30.2067 7.875 30.0713 7.60937C29.9385 7.34375 29.7484 7.13411 29.501 6.98047C29.2562 6.82682 28.9671 6.75 28.6338 6.75C28.2666 6.75 27.9489 6.84115 27.6807 7.02344C27.415 7.20312 27.2106 7.4375 27.0674 7.72656C26.9242 8.01562 26.8525 8.32552 26.8525 8.65625V9.1875C26.8525 9.64063 26.9307 10.0247 27.0869 10.3398C27.2458 10.6523 27.4658 10.8906 27.7471 11.0547C28.0283 11.2161 28.3551 11.2969 28.7275 11.2969C28.9697 11.2969 29.1885 11.263 29.3838 11.1953C29.5817 11.125 29.7523 11.0208 29.8955 10.8828C30.0387 10.7422 30.1494 10.5677 30.2275 10.3594L31.1182 10.6094C31.0244 10.9115 30.8669 11.1771 30.6455 11.4062C30.4242 11.6328 30.1507 11.8099 29.8252 11.9375C29.4997 12.0625 29.1338 12.125 28.7275 12.125ZM33.5361 8.39062V12H32.6143V6H33.5049V6.9375H33.583C33.7236 6.63281 33.9372 6.38802 34.2236 6.20313C34.5101 6.01563 34.8799 5.92188 35.333 5.92188C35.7393 5.92188 36.0947 6.00521 36.3994 6.17187C36.7041 6.33594 36.9411 6.58594 37.1104 6.92187C37.2796 7.25521 37.3643 7.67708 37.3643 8.1875V12H36.4424V8.25C36.4424 7.77865 36.32 7.41146 36.0752 7.14844C35.8304 6.88281 35.4945 6.75 35.0674 6.75C34.7731 6.75 34.5101 6.8138 34.2783 6.94141C34.0492 7.06901 33.8682 7.25521 33.7354 7.5C33.6025 7.74479 33.5361 8.04167 33.5361 8.39062ZM41.6582 6V6.78125H38.5488V6H41.6582ZM39.4551 4.5625H40.377V10.2812C40.377 10.5417 40.4147 10.737 40.4902 10.8672C40.5684 10.9948 40.6673 11.0807 40.7871 11.125C40.9095 11.1667 41.0384 11.1875 41.1738 11.1875C41.2754 11.1875 41.3587 11.1823 41.4238 11.1719C41.4889 11.1589 41.541 11.1484 41.5801 11.1406L41.7676 11.9687C41.7051 11.9922 41.6178 12.0156 41.5059 12.0391C41.3939 12.0651 41.252 12.0781 41.0801 12.0781C40.8197 12.0781 40.5645 12.0221 40.3145 11.9102C40.0671 11.7982 39.8613 11.6276 39.6973 11.3984C39.5358 11.1693 39.4551 10.8802 39.4551 10.5312V4.5625ZM45.4961 12.125C44.918 12.125 44.4193 11.9974 44 11.7422C43.5833 11.4844 43.2617 11.125 43.0352 10.6641C42.8112 10.2005 42.6992 9.66146 42.6992 9.04688C42.6992 8.43229 42.8112 7.89062 43.0352 7.42187C43.2617 6.95052 43.5768 6.58333 43.9805 6.32031C44.3867 6.05469 44.8607 5.92188 45.4023 5.92188C45.7148 5.92188 46.0234 5.97396 46.3281 6.07812C46.6328 6.18229 46.9102 6.35156 47.1602 6.58594C47.4102 6.81771 47.6094 7.125 47.7578 7.50781C47.9063 7.89062 47.9805 8.36198 47.9805 8.92187V9.3125H43.3555V8.51562H47.043C47.043 8.17708 46.9753 7.875 46.8398 7.60937C46.707 7.34375 46.5169 7.13411 46.2695 6.98047C46.0247 6.82682 45.7357 6.75 45.4023 6.75C45.0352 6.75 44.7174 6.84115 44.4492 7.02344C44.1836 7.20312 43.9792 7.4375 43.8359 7.72656C43.6927 8.01562 43.6211 8.32552 43.6211 8.65625V9.1875C43.6211 9.64063 43.6992 10.0247 43.8555 10.3398C44.0143 10.6523 44.2344 10.8906 44.5156 11.0547C44.7969 11.2161 45.1237 11.2969 45.4961 11.2969C45.7383 11.2969 45.957 11.263 46.1523 11.1953C46.3503 11.125 46.5208 11.0208 46.6641 10.8828C46.8073 10.7422 46.918 10.5677 46.9961 10.3594L47.8867 10.6094C47.793 10.9115 47.6354 11.1771 47.4141 11.4062C47.1927 11.6328 46.9193 11.8099 46.5937 11.9375C46.2682 12.0625 45.9023 12.125 45.4961 12.125ZM49.3828 12V6H50.2734V6.90625H50.3359C50.4453 6.60938 50.6432 6.36849 50.9297 6.18359C51.2161 5.9987 51.5391 5.90625 51.8984 5.90625C51.9661 5.90625 52.0508 5.90755 52.1523 5.91016C52.2539 5.91276 52.3307 5.91667 52.3828 5.92188V6.85937C52.3516 6.85156 52.2799 6.83984 52.168 6.82422C52.0586 6.80599 51.9427 6.79687 51.8203 6.79687C51.5286 6.79687 51.2682 6.85807 51.0391 6.98047C50.8125 7.10026 50.6328 7.26693 50.5 7.48047C50.3698 7.69141 50.3047 7.93229 50.3047 8.20312V12H49.3828Z"
        fill="white"
      />
    </svg>
  );
};
