export const UploadFile = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="248"
      height="130"
      viewBox="0 0 248 132"
      fill="none"
    >
      <rect x="4" width="24" height="10" rx="2" fill="#E9E9E9" />
      <rect x="4" y="12" width="24" height="10" rx="2" fill="#E9E9E9" />
      <rect x="4" y="24" width="24" height="10" rx="2" fill="#E9E9E9" />
      <rect x="4" y="36" width="24" height="10" rx="2" fill="#E9E9E9" />
      <rect x="4" y="48" width="24" height="10" rx="2" fill="#E9E9E9" />
      <rect x="4" y="60" width="24" height="10" rx="2" fill="#E9E9E9" />
      <rect x="4" y="72" width="24" height="10" rx="2" fill="#E9E9E9" />
      <rect x="30" width="53" height="10" rx="2" fill="#E9E9E9" />
      <rect x="30" y="12" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="30" y="24" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="30" y="36" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="30" y="48" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="30" y="60" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="30" y="72" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="85" width="53" height="10" rx="2" fill="#E9E9E9" />
      <rect x="85" y="12" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="85" y="24" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="85" y="36" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="85" y="48" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="85" y="60" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="85" y="72" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="140" width="53" height="10" rx="2" fill="#E9E9E9" />
      <rect x="140" y="12" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="140" y="24" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="140" y="36" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="140" y="48" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="140" y="60" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="140" y="72" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="195" width="53" height="10" rx="2" fill="#E9E9E9" />
      <rect x="195" y="12" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="195" y="24" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="195" y="36" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="195" y="48" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="195" y="60" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="195" y="72" width="53" height="10" rx="2" fill="#F1F1F1" />
      <rect x="4" width="244" height="82" fill="url(#paint0_linear_8979_11437)" />
      <g filter="url(#filter0_d_8979_11437)">
        <rect x="16" y="60" width="48" height="48" rx="24" fill="white" />
        <rect x="16.5" y="60.5" width="47" height="47" rx="23.5" stroke="#E1E6EF" />
        <mask id="mask0_8979_11437" maskUnits="userSpaceOnUse" x="28" y="72" width="24" height="24">
          <rect x="28" y="72" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_8979_11437)">
          <path
            d="M39.25 87.4423H31.5V90.6923C31.5 91.1894 31.677 91.615 32.031 91.969C32.385 92.323 32.8106 92.5 33.3077 92.5H39.25V87.4423ZM40.75 87.4423V92.5H46.6923C47.1894 92.5 47.615 92.323 47.969 91.969C48.323 91.615 48.5 91.1894 48.5 90.6923V87.4423H40.75ZM39.25 85.9423V80.8845H31.5V85.9423H39.25ZM40.75 85.9423H48.5V80.8845H40.75V85.9423ZM31.5 79.3848H48.5V77.3077C48.5 76.8106 48.323 76.385 47.969 76.031C47.615 75.677 47.1894 75.5 46.6923 75.5H33.3077C32.8106 75.5 32.385 75.677 32.031 76.031C31.677 76.385 31.5 76.8106 31.5 77.3077V79.3848Z"
            fill="#1C895F"
          />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_d_8979_11437"
          x="0"
          y="52"
          width="80"
          height="80"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="8" />
          <feGaussianBlur stdDeviation="8" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8979_11437" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_8979_11437"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_8979_11437"
          x1="126"
          y1="0"
          x2="126"
          y2="82"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" stopOpacity="0" />
          <stop offset="1" stopColor="white" stopOpacity="0.8" />
        </linearGradient>
      </defs>
    </svg>
  );
};
