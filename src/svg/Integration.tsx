export const Integration = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="244"
      height="130"
      viewBox="0 0 248 130"
      fill="none"
    >
      <rect x="4" y="0.75" width="8" height="8" rx="2" fill="#E9E9E9" />
      <rect x="4" y="12.75" width="8" height="8" rx="2" fill="#E9E9E9" />
      <rect x="4" y="24.75" width="8" height="8" rx="2" fill="#E9E9E9" />
      <rect x="4" y="36.75" width="8" height="8" rx="2" fill="#E9E9E9" />
      <rect x="4" y="48.75" width="8" height="8" rx="2" fill="#E9E9E9" />
      <rect x="4" y="60.75" width="8" height="8" rx="2" fill="#E9E9E9" />
      <rect x="4" y="72.75" width="8" height="8" rx="2" fill="#E9E9E9" />
      <rect x="16" y="1" width="57" height="8" rx="2" fill="#F1F1F1" />
      <rect x="32" y="13" width="43" height="8" rx="2" fill="#F1F1F1" />
      <rect x="32" y="25" width="18" height="8" rx="2" fill="#F1F1F1" />
      <rect x="52" y="25" width="28" height="8" rx="2" fill="#F1F1F1" />
      <rect x="30" y="49" width="9" height="8" rx="2" fill="#F1F1F1" />
      <rect x="41" y="49" width="9" height="8" rx="2" fill="#F1F1F1" />
      <rect x="30" y="61" width="32" height="8" rx="2" fill="#F1F1F1" />
      <rect x="64" y="61" width="32" height="8" rx="2" fill="#F1F1F1" />
      <rect x="30" y="73" width="53" height="8" rx="2" fill="#F1F1F1" />
      <rect x="75" y="1" width="12" height="8" rx="2" fill="#F1F1F1" />
      <rect x="89" y="1" width="38" height="8" rx="2" fill="#F1F1F1" />
      <rect x="85" y="13" width="53" height="8" rx="2" fill="#F1F1F1" />
      <rect x="82" y="25" width="34" height="8" rx="2" fill="#F1F1F1" />
      <rect x="52" y="49" width="40" height="8" rx="2" fill="#F1F1F1" />
      <rect x="110" y="61" width="22" height="8" rx="2" fill="#F1F1F1" />
      <rect x="85" y="73" width="60" height="8" rx="2" fill="#F1F1F1" />
      <rect x="137" y="1" width="35" height="8" rx="2" fill="#F1F1F1" />
      <rect x="140" y="13" width="53" height="8" rx="2" fill="#F1F1F1" />
      <rect x="118" y="25" width="67" height="8" rx="2" fill="#F1F1F1" />
      <rect x="98" y="49" width="80" height="8" rx="2" fill="#F1F1F1" />
      <rect x="138" y="61" width="60" height="8" rx="2" fill="#F1F1F1" />
      <rect x="147" y="73" width="15" height="8" rx="2" fill="#F1F1F1" />
      <rect x="174" y="1" width="14" height="8" rx="2" fill="#F1F1F1" />
      <rect x="190" y="1" width="33" height="8" rx="2" fill="#F1F1F1" />
      <rect x="195" y="12" width="53" height="8" rx="2" fill="#F1F1F1" />
      <rect x="187" y="25" width="24" height="8" rx="2" fill="#F1F1F1" />
      <rect x="213" y="25" width="10" height="8" rx="2" fill="#F1F1F1" />
      <rect x="180" y="49" width="25" height="8" rx="2" fill="#F1F1F1" />
      <rect x="207" y="49" width="8" height="8" rx="2" fill="#F1F1F1" />
      <rect x="203" y="61" width="26" height="8" rx="2" fill="#F1F1F1" />
      <rect x="164" y="73" width="55" height="8" rx="2" fill="#F1F1F1" />
      <rect x="4" y="1" width="244" height="82" fill="url(#paint0_linear_8982_25393)" />
      <g filter="url(#filter0_d_8982_25393)">
        <rect x="16" y="57.25" width="48" height="48" rx="24" fill="white" />
        <rect x="16.5" y="57.75" width="47" height="47" rx="23.5" stroke="#E1E6EF" />
        <mask
          id="mask0_8982_25393"
          style={{ maskType: "alpha" }}
          maskUnits="userSpaceOnUse"
          x="28"
          y="69"
          width="24"
          height="25"
        >
          <rect x="28" y="69.25" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_8982_25393)">
          <path
            d="M39.9996 91.1634C39.0331 91.1634 38.2162 90.8296 37.5489 90.1621C36.8815 89.4948 36.5479 88.6779 36.5479 87.7114C36.5479 86.8832 36.803 86.1592 37.3134 85.5394C37.8235 84.9194 38.469 84.5216 39.2499 84.3461V81.9999H33.4999V78.2019H31.0479V71.7979H37.4519V78.2019H34.9999V80.4999H44.9999V78.1154C44.219 77.9397 43.5735 77.5419 43.0634 76.9221C42.553 76.3021 42.2979 75.578 42.2979 74.7499C42.2979 73.7834 42.6316 72.9664 43.2991 72.2991C43.9664 71.6316 44.7834 71.2979 45.7499 71.2979C46.7165 71.2979 47.5335 71.6316 48.2009 72.2991C48.8682 72.9664 49.2019 73.7834 49.2019 74.7499C49.2019 75.578 48.9467 76.3021 48.4364 76.9221C47.926 77.5419 47.2805 77.9397 46.4999 78.1154V81.9999H40.7499V84.3461C41.5307 84.5216 42.1762 84.9194 42.6864 85.5394C43.1967 86.1592 43.4519 86.8832 43.4519 87.7114C43.4519 88.6779 43.1181 89.4948 42.4506 90.1621C41.7833 90.8296 40.9663 91.1634 39.9996 91.1634Z"
            fill="#1C895F"
          />
        </g>
      </g>
      <g filter="url(#filter1_d_8982_25393)">
        <rect x="68" y="57.25" width="48" height="48" rx="24" fill="white" />
        <rect x="68.5" y="57.75" width="47" height="47" rx="23.5" stroke="#E1E6EF" />
        <mask
          id="mask1_8982_25393"
          style={{ maskType: "alpha" }}
          maskUnits="userSpaceOnUse"
          x="80"
          y="69"
          width="24"
          height="25"
        >
          <rect x="80" y="69.25" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask1_8982_25393)">
          <path
            d="M94.2693 88.75V87.25H96.7402C97.0942 87.25 97.3927 87.1302 97.6355 86.8905C97.8785 86.6507 98 86.3602 98 86.0192V83.9615C98 83.3667 98.1769 82.8333 98.5308 82.3615C98.8846 81.8897 99.3455 81.5615 99.9135 81.377V81.123C99.3455 80.9385 98.8846 80.6103 98.5308 80.1385C98.1769 79.6667 98 79.1333 98 78.5385V76.4807C98 76.1397 97.8785 75.8493 97.6355 75.6095C97.3927 75.3698 97.0942 75.25 96.7402 75.25H94.2693V73.75H96.7402C97.5096 73.75 98.1618 74.016 98.697 74.548C99.2323 75.0802 99.5 75.7244 99.5 76.4807V78.5385C99.5 78.8923 99.6247 79.1892 99.874 79.429C100.123 79.6687 100.428 79.7885 100.788 79.7885H101.5V82.7115H100.788C100.428 82.7115 100.123 82.8313 99.874 83.071C99.6247 83.3108 99.5 83.6077 99.5 83.9615V86.0192C99.5 86.7756 99.2323 87.4198 98.697 87.952C98.1618 88.484 97.5096 88.75 96.7402 88.75H94.2693ZM87.2598 88.75C86.4969 88.75 85.8463 88.484 85.3077 87.952C84.7692 87.4198 84.5 86.7756 84.5 86.0192V83.9615C84.5 83.6077 84.3753 83.3108 84.126 83.071C83.8767 82.8313 83.5718 82.7115 83.2115 82.7115H82.5V79.7885H83.2115C83.5718 79.7885 83.8767 79.6687 84.126 79.429C84.3753 79.1892 84.5 78.8923 84.5 78.5385V76.4807C84.5 75.7244 84.7692 75.0802 85.3077 74.548C85.8463 74.016 86.4969 73.75 87.2598 73.75H89.7402V75.25H87.2598C86.9123 75.25 86.6154 75.3698 86.3693 75.6095C86.1231 75.8493 86 76.1397 86 76.4807V78.5385C86 79.1333 85.8247 79.6667 85.474 80.1385C85.1233 80.6103 84.6608 80.9385 84.0865 81.123V81.377C84.6608 81.5615 85.1233 81.8897 85.474 82.3615C85.8247 82.8333 86 83.3667 86 83.9615V86.0192C86 86.3602 86.1231 86.6507 86.3693 86.8905C86.6154 87.1302 86.9123 87.25 87.2598 87.25H89.7402V88.75H87.2598Z"
            fill="#6B7280"
          />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_d_8982_25393"
          x="0"
          y="49.25"
          width="80"
          height="80"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="8" />
          <feGaussianBlur stdDeviation="8" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8982_25393" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_8982_25393"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_d_8982_25393"
          x="52"
          y="49.25"
          width="80"
          height="80"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="8" />
          <feGaussianBlur stdDeviation="8" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8982_25393" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_8982_25393"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_8982_25393"
          x1="126"
          y1="1"
          x2="126"
          y2="83"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" stopOpacity="0" />
          <stop offset="1" stopColor="white" stopOpacity="0.8" />
        </linearGradient>
      </defs>
    </svg>
  );
};
