import {
  Authorization<PERSON>ontext,
  Author<PERSON><PERSON><PERSON><PERSON>,
  FetcherAccessor,
  useAuthModule,
  userAccountRoleHasPolicy,
} from "@floqastinc/auth-module-client";

const USER_ACTION_KEYS = Object.freeze({
  TRANSFORM_WORKFLOW_WRITE: "transform:workflows:write",
  TRANSFORM_WORKFLOW_FULL: "transform:workflows:full",

  TRANSFORM_RUN_READ: "transform:runs:read",
  TRANSFORM_RUN_WRITE: "transform:runs:write",
  TRANSFORM_RUN_FULL: "transform:runs:full",

  TRANSFORM_PRINCIPAL_FULL: "transform:principals:full",

  CONNECTIONS_FULL: "connections:full",
});

const authorizationManager = new AuthorizationManager();

// We just want to allow all actions in dev mode
const isDev = () => {
  return process.env.REACT_APP_NODE_ENV === "development";
};

// We don't have access to the real user role/policies
// so we just want to allow all actions in impost sessions
const isImpostSession = (context: AuthorizationContext) => {
  return !!context?.principal?.isImpostingUser;
};

const isAdminOrManager = (context: AuthorizationContext) => {
  return context?.principal?.role === "ADMIN" || context?.principal?.role === "MANAGER";
};

// Allow admins and managers with a feature flag to access the builder
const shouldAllowBuilderAdmin = (context: AuthorizationContext) => {
  return context?.tlcModules?.transformBuilder && isAdminOrManager(context);
};

const createValidator = (
  policy: string,
  additionalCheck?: (context: AuthorizationContext) => boolean,
) => {
  // A bit odd, but the `any` method seems to try to call every validator
  // even if the first one returns true. This is a workaround to prevent
  // the policy validator from being called in dev or impost sessions
  // since the userAccountRoleHasPolicy is async and more expensive.
  return (context: AuthorizationContext, { fetchers }: { fetchers: FetcherAccessor }) => {
    // Comment this conditional to test showing/hiding elements
    if (isDev() || isImpostSession(context) || (additionalCheck && additionalCheck(context))) {
      return true;
    }
    return userAccountRoleHasPolicy(policy)(context, { fetchers });
  };
};

authorizationManager
  .for(USER_ACTION_KEYS.TRANSFORM_WORKFLOW_WRITE)
  .useValidator(
    createValidator(USER_ACTION_KEYS.TRANSFORM_WORKFLOW_WRITE, shouldAllowBuilderAdmin),
  );

authorizationManager
  .for(USER_ACTION_KEYS.TRANSFORM_WORKFLOW_FULL)
  .useValidator(createValidator(USER_ACTION_KEYS.TRANSFORM_WORKFLOW_FULL, shouldAllowBuilderAdmin));

authorizationManager
  .for(USER_ACTION_KEYS.TRANSFORM_RUN_READ)
  .useValidator(createValidator(USER_ACTION_KEYS.TRANSFORM_RUN_READ));

authorizationManager
  .for(USER_ACTION_KEYS.TRANSFORM_RUN_WRITE)
  .useValidator(createValidator(USER_ACTION_KEYS.TRANSFORM_RUN_WRITE));

authorizationManager
  .for(USER_ACTION_KEYS.TRANSFORM_RUN_FULL)
  .useValidator(createValidator(USER_ACTION_KEYS.TRANSFORM_RUN_FULL));

authorizationManager
  .for(USER_ACTION_KEYS.TRANSFORM_PRINCIPAL_FULL)
  .useValidator(createValidator(USER_ACTION_KEYS.TRANSFORM_PRINCIPAL_FULL));

authorizationManager
  .for(USER_ACTION_KEYS.CONNECTIONS_FULL)
  .useValidator(createValidator(USER_ACTION_KEYS.CONNECTIONS_FULL));

export { authorizationManager, useAuthModule, USER_ACTION_KEYS };
