import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { renderHook } from "vitest-browser-react";
import { useCreateExampleWithJemDuplication } from "./useCreateExampleWithJemDuplication";
import { useCreateExample } from "@v3/examples";
import duplicateJemTemplate from "@Transform/api/duplicate-jem-template";
import { v3 } from "@/services/v3";

vi.mock("@/services/v3", () => ({
  v3: {
    tasks: {
      getTask: vi.fn(),
    },
  },
}));

vi.mock("@Transform/api/duplicate-jem-template", () => ({
  default: vi.fn(),
}));

vi.mock("@v3/examples", () => ({
  useCreateExample: vi.fn(),
}));

const mockUseCreateExample = vi.mocked(useCreateExample);
const mockDuplicateJemTemplate = vi.mocked(duplicateJemTemplate);
const mockGetTask = vi.mocked(v3.tasks.getTask);

const queryClient = new QueryClient();

const createWrapper = () => {
  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
  return TestWrapper;
};

describe("useCreateExampleWithJemDuplication", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    queryClient.clear();
  });

  it("should call useCreateExample with provided args", () => {
    // Arrange
    const args = {
      onSuccess: vi.fn(),
      onError: vi.fn(),
    };

    mockUseCreateExample.mockReturnValue({
      mutate: vi.fn(),
      isLoading: false,
      error: null,
    } as any);

    // Act
    renderHook(() => useCreateExampleWithJemDuplication(args), {
      wrapper: createWrapper(),
    });

    // Assert
    expect(mockUseCreateExample).toHaveBeenCalledWith(
      expect.objectContaining({
        onSuccess: args.onSuccess,
        onError: args.onError,
        onMutate: expect.any(Function),
      }),
    );
  });

  it("should duplicate JEM template when copying from existing example set with JEM_TEMPLATE_FETCH strategy", async () => {
    // Arrange
    const args = { onSuccess: vi.fn() };
    let onMutateCallback: ((variables: any) => unknown) | undefined;

    mockUseCreateExample.mockImplementation((config) => {
      onMutateCallback = config?.onMutate;
      return {
        mutate: vi.fn(),
        isLoading: false,
        error: null,
      } as any;
    });

    mockGetTask.mockResolvedValue({
      data: {
        id: "task-1",
        strategy: {
          kind: "JEM_TEMPLATE_FETCH",
        },
      },
    } as any);

    mockDuplicateJemTemplate.mockResolvedValue(undefined);

    // Act
    renderHook(() => useCreateExampleWithJemDuplication(args), {
      wrapper: createWrapper(),
    });

    const mutateArgs = {
      workflowId: "workflow-1",
      taskId: "task-1",
      example: {
        copyFromExampleSetId: "example-set-1",
        name: "Test Example",
      },
    };

    if (onMutateCallback) {
      await onMutateCallback(mutateArgs);
    }

    // Assert
    expect(mockGetTask).toHaveBeenCalledWith({
      workflowId: "workflow-1",
      taskId: "task-1",
    });
    expect(mockDuplicateJemTemplate).toHaveBeenCalledWith("task-1", "example-set-1", "workflow-1");
  });

  it("should not duplicate JEM template when copyFromExampleSetId is undefined", async () => {
    // Arrange
    const args = { onSuccess: vi.fn() };
    let onMutateCallback: ((variables: any) => unknown) | undefined;

    mockUseCreateExample.mockImplementation((config) => {
      onMutateCallback = config?.onMutate;
      return {
        mutate: vi.fn(),
        isLoading: false,
        error: null,
      } as any;
    });

    // Act
    renderHook(() => useCreateExampleWithJemDuplication(args), {
      wrapper: createWrapper(),
    });

    const mutateArgs = {
      workflowId: "workflow-1",
      taskId: "task-1",
      example: {
        name: "Test Example",
        // copyFromExampleSetId is undefined
      },
    };

    if (onMutateCallback) {
      await onMutateCallback(mutateArgs);
    }

    // Assert
    expect(mockGetTask).not.toHaveBeenCalled();
    expect(mockDuplicateJemTemplate).not.toHaveBeenCalled();
  });

  it("should not duplicate JEM template when task strategy is not JEM_TEMPLATE_FETCH", async () => {
    // Arrange
    const args = { onSuccess: vi.fn() };
    let onMutateCallback: ((variables: any) => unknown) | undefined;

    mockUseCreateExample.mockImplementation((config) => {
      onMutateCallback = config?.onMutate;
      return {
        mutate: vi.fn(),
        isLoading: false,
        error: null,
      } as any;
    });

    mockGetTask.mockResolvedValue({
      data: {
        id: "task-1",
        strategy: {
          kind: "SCRIPT",
        },
      },
    } as any);

    // Act
    renderHook(() => useCreateExampleWithJemDuplication(args), {
      wrapper: createWrapper(),
    });

    const mutateArgs = {
      workflowId: "workflow-1",
      taskId: "task-1",
      example: {
        copyFromExampleSetId: "example-set-1",
        name: "Test Example",
      },
    };

    if (onMutateCallback) {
      await onMutateCallback(mutateArgs);
    }

    // Assert
    expect(mockGetTask).toHaveBeenCalledWith({
      workflowId: "workflow-1",
      taskId: "task-1",
    });
    expect(mockDuplicateJemTemplate).not.toHaveBeenCalled();
  });

  it("should handle missing task data gracefully", async () => {
    // Arrange
    const args = { onSuccess: vi.fn() };
    let onMutateCallback: ((variables: any) => unknown) | undefined;

    mockUseCreateExample.mockImplementation((config) => {
      onMutateCallback = config?.onMutate;
      return {
        mutate: vi.fn(),
        isLoading: false,
        error: null,
      } as any;
    });

    mockGetTask.mockResolvedValue({
      data: null,
    } as any);

    // Act
    renderHook(() => useCreateExampleWithJemDuplication(args), {
      wrapper: createWrapper(),
    });

    const mutateArgs = {
      workflowId: "workflow-1",
      taskId: "task-1",
      example: {
        copyFromExampleSetId: "example-set-1",
        name: "Test Example",
      },
    };

    if (onMutateCallback) {
      await onMutateCallback(mutateArgs);
    }

    // Assert
    expect(mockGetTask).toHaveBeenCalledWith({
      workflowId: "workflow-1",
      taskId: "task-1",
    });
    expect(mockDuplicateJemTemplate).not.toHaveBeenCalled();
  });

  it("should handle missing task strategy gracefully", async () => {
    // Arrange
    const args = { onSuccess: vi.fn() };
    let onMutateCallback: ((variables: any) => unknown) | undefined;

    mockUseCreateExample.mockImplementation((config) => {
      onMutateCallback = config?.onMutate;
      return {
        mutate: vi.fn(),
        isLoading: false,
        error: null,
      } as any;
    });

    mockGetTask.mockResolvedValue({
      data: {
        id: "task-1",
        // strategy is undefined
      },
    } as any);

    // Act
    renderHook(() => useCreateExampleWithJemDuplication(args), {
      wrapper: createWrapper(),
    });

    const mutateArgs = {
      workflowId: "workflow-1",
      taskId: "task-1",
      example: {
        copyFromExampleSetId: "example-set-1",
        name: "Test Example",
      },
    };

    if (onMutateCallback) {
      await onMutateCallback(mutateArgs);
    }

    // Assert
    expect(mockGetTask).toHaveBeenCalledWith({
      workflowId: "workflow-1",
      taskId: "task-1",
    });
    expect(mockDuplicateJemTemplate).not.toHaveBeenCalled();
  });
});
