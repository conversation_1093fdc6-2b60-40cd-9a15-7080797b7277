import { FileArgument } from "@floqastinc/transform-v3";
import { TaskSourceSchema } from "@floqastinc/transform-v3/lib/v3/schemas";
import { useQueries, useQuery, UseQueryResult } from "@tanstack/react-query";
import { useAtom, useSetAtom } from "jotai";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { getFileFromUri } from "../apps/BuilderV3/api/files";
import { fileAtom, isFileLoadingAtom } from "@Transform/pages/builder/store";
import { getTaskInputsQuery } from "@BuilderV3/api/task-inputs";
import {
  getExampleOutputFileUriQuery,
  getExampleOutputsQuery,
} from "@BuilderV3/api/example-outputs";
import { getExampleInputFileUriQuery, getExampleInputsQuery } from "@BuilderV3/api/example-inputs";
import { v3 } from "@/services/v3";

type FileInfo = {
  id: string;
  type: "Input" | "Output" | "PreviousOutput";
  name: string;
  displayName: string;
  taskInputId?: string;
  taskOutputId?: string;
  stepNumber?: number;
};

type UseFilePreviewForCurrentTaskProps = {
  workflowId: string;
  taskId: string;
  exampleSetId: string;
};

export const useFilePreviewForCurrentTask = ({
  workflowId,
  taskId,
  exampleSetId,
}: UseFilePreviewForCurrentTaskProps) => {
  const [file, setFile] = useAtom(fileAtom);
  const setIsFileLoading = useSetAtom(isFileLoadingAtom);
  const [selectedFile, setSelectedFile] = useState<FileInfo | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isFileReady, setIsFileReady] = useState(false);

  // Add a flag to track if all initial queries have completed
  const [hasCompletedInitialLoad, setHasCompletedInitialLoad] = useState(false);

  // File cache to prevent duplicate fetches - keyed by file ID + context
  const fileCacheRef = useRef<Map<string, File>>(new Map());
  const activeFileIdRef = useRef<string | null>(null);

  // Create a cache key that includes version context
  const createCacheKey = useCallback((fileId: string, exampleSetId: string, taskId: string) => {
    return `${fileId}-${exampleSetId}-${taskId}`;
  }, []);

  // Function to clear specific cache entries or entire cache
  const clearFileCache = useCallback((fileId?: string) => {
    if (fileId) {
      // Clear specific file across all contexts
      const keysToDelete = Array.from(fileCacheRef.current.keys()).filter((key) =>
        key.startsWith(`${fileId}-`),
      );
      keysToDelete.forEach((key) => fileCacheRef.current.delete(key));
    } else {
      // Clear entire cache
      fileCacheRef.current.clear();
      activeFileIdRef.current = null;
    }
  }, []);

  // Function to force refresh current file
  const forceRefreshCurrentFile = useCallback(() => {
    if (selectedFile?.id) {
      clearFileCache(selectedFile.id);
      // Clear current active file to force refetch
      activeFileIdRef.current = null;
    }
  }, [selectedFile?.id, clearFileCache]);

  // Reset state when workflow/task/example changes
  useEffect(() => {
    setSelectedFile(null);
    setError(null);
    setIsFileReady(false);
    setHasCompletedInitialLoad(false);
    setFile(null);
    setIsFileLoading(false);
    // Clear the file cache when context changes
    fileCacheRef.current.clear();
    activeFileIdRef.current = null;
  }, [workflowId, taskId, exampleSetId, setFile, setIsFileLoading]);

  const isTaskQueryEnabled = Boolean(workflowId && taskId);
  const isExampleQueryEnabled = Boolean(workflowId && taskId && exampleSetId);
  const isExampleInputUriQueryEnabled = Boolean(
    isExampleQueryEnabled &&
      selectedFile?.id &&
      (selectedFile?.type === "Input" || selectedFile?.type === "PreviousOutput"),
  );
  const isExampleOutputUriQueryEnabled = Boolean(
    isExampleQueryEnabled && selectedFile?.id && selectedFile?.type === "Output",
  );

  // Use the task query enabled flag for the task inputs query
  const taskInputsQuery = useQuery({
    ...getTaskInputsQuery({ workflowId, taskId }),
    enabled: isTaskQueryEnabled,
  });

  // Add query for tasks to get previous task outputs
  const tasksQuery = useQuery({
    queryKey: ["tasks", workflowId],
    queryFn: async () => {
      const result = await v3.tasks.getTasks({ workflowId });
      return result.data || [];
    },
    enabled: isTaskQueryEnabled,
  });

  const [exampleInputsQuery, exampleOutputsQuery, exampleInputFileUri, exampleOutputFileUri] =
    useQueries({
      queries: [
        {
          ...getExampleInputsQuery({ workflowId, taskId, exampleSetId }),
          enabled: isExampleQueryEnabled,
        },
        {
          ...getExampleOutputsQuery({ workflowId, taskId, exampleSetId }),
          enabled: isExampleQueryEnabled,
        },
        {
          ...getExampleInputFileUriQuery({
            workflowId,
            taskId,
            exampleSetId,
            exampleInputId: selectedFile?.id as string,
          }),
          enabled: isExampleInputUriQueryEnabled,
        },
        {
          ...getExampleOutputFileUriQuery({
            workflowId,
            taskId,
            exampleSetId,
            exampleOutputId: selectedFile?.id as string,
          }),
          enabled: isExampleOutputUriQueryEnabled,
        },
      ],
    });

  const inputFiles = useMemo(
    () =>
      exampleInputsQuery.data
        ?.filter((input) => {
          const taskInput = taskInputsQuery.data?.find((ti) => ti.id === input.taskInputId);
          // Only include files that are NOT from task sources (previous outputs)
          return (
            input.value?.kind === "FILE" &&
            taskInput &&
            !TaskSourceSchema.safeParse(taskInput.source).success
          );
        })
        .map((input) => {
          const fileValue = input.value as FileArgument;
          const taskInput = taskInputsQuery.data?.find((ti) => ti.id === input.taskInputId);
          const displayName = taskInput?.name || fileValue.name || `Input ${input.id}`;
          return {
            id: input.id,
            type: "Input" as const,
            name: fileValue.name || `Input ${input.id}`,
            displayName,
            taskInputId: input.taskInputId,
          };
        }) || [],
    [exampleInputsQuery.data, taskInputsQuery.data],
  );

  const previousTaskOutputFiles = useMemo(() => {
    if (!exampleInputsQuery.data || !taskInputsQuery.data || !tasksQuery.data) {
      return [];
    }

    return exampleInputsQuery.data
      .filter((input) => {
        const taskInput = taskInputsQuery.data?.find((ti) => ti.id === input.taskInputId);
        return (
          taskInput &&
          TaskSourceSchema.safeParse(taskInput.source).success &&
          input.value?.kind === "FILE"
        );
      })
      .map((input) => {
        const taskInput = taskInputsQuery.data?.find((ti) => ti.id === input.taskInputId);
        const sourceTaskId = TaskSourceSchema.safeParse(taskInput?.source).success
          ? taskInput?.source?.taskId
          : "";
        const sourceTask = tasksQuery.data?.find((task) => task.id === sourceTaskId);
        const stepIndex = tasksQuery.data?.findIndex((task) => task.id === sourceTaskId);

        return {
          id: input.id,
          type: "PreviousOutput" as const,
          name: sourceTask?.name ?? `Previous Output ${input.id}`,
          displayName: sourceTask?.name ?? `Previous Output ${input.id}`,
          taskInputId: input.taskInputId,
          stepNumber: stepIndex,
        };
      });
  }, [exampleInputsQuery.data, taskInputsQuery.data, tasksQuery.data]);

  const outputFiles = useMemo(
    () =>
      exampleOutputsQuery.data
        ?.filter((output) => output.value?.kind === "FILE")
        .map((output) => {
          const currentTask = tasksQuery.data?.find((task) => task.id === taskId);
          const taskName = currentTask?.name || "Output";

          return {
            id: output.id,
            type: "Output" as const,
            name: taskName,
            displayName: taskName,
            taskOutputId: output.taskOutputId,
          };
        }) || [],
    [exampleOutputsQuery.data, tasksQuery.data, taskId],
  );

  const files = useMemo(
    () => ({
      outputs: outputFiles || [],
      inputs: inputFiles || [],
      previousOutputs: previousTaskOutputFiles || [],
    }),
    [outputFiles, inputFiles, previousTaskOutputFiles],
  );

  const allFiles = useMemo(() => {
    return [...outputFiles, ...previousTaskOutputFiles, ...inputFiles];
  }, [outputFiles, previousTaskOutputFiles, inputFiles]);

  // Only include queries that are enabled
  const queryStates: UseQueryResult[] = [];
  if (isTaskQueryEnabled) {
    queryStates.push(taskInputsQuery, tasksQuery);
  }
  if (isExampleQueryEnabled) {
    queryStates.push(exampleInputsQuery);
    queryStates.push(exampleOutputsQuery);
  }
  if (isExampleInputUriQueryEnabled) {
    queryStates.push(exampleInputFileUri);
  }
  if (isExampleOutputUriQueryEnabled) {
    queryStates.push(exampleOutputFileUri);
  }

  const isLoading =
    queryStates.some((query) => query.isPending || query.isFetching) || !isFileReady;

  // Check if all initial queries have completed at least once
  useEffect(() => {
    const allQueriesCompleted =
      (!isTaskQueryEnabled || (taskInputsQuery.isFetched && tasksQuery.isFetched)) &&
      (!isExampleQueryEnabled || (exampleInputsQuery.isFetched && exampleOutputsQuery.isFetched));

    if (allQueriesCompleted && !hasCompletedInitialLoad) {
      setHasCompletedInitialLoad(true);
    }
  }, [
    taskInputsQuery.isFetched,
    tasksQuery.isFetched,
    exampleInputsQuery.isFetched,
    exampleOutputsQuery.isFetched,
    isTaskQueryEnabled,
    isExampleQueryEnabled,
    hasCompletedInitialLoad,
  ]);

  // Extract fileUri using useMemo to avoid unnecessary re-renders
  const fileUri = useMemo(() => {
    if (!selectedFile?.id) return null;

    if (selectedFile.type === "Input" || selectedFile.type === "PreviousOutput") {
      return exampleInputFileUri.data?.data?.url || null;
    } else if (selectedFile.type === "Output") {
      return exampleOutputFileUri.data?.data?.url || null;
    }
    return null;
  }, [
    selectedFile?.id,
    selectedFile?.type,
    exampleInputFileUri.data?.data?.url,
    exampleOutputFileUri.data?.data?.url,
  ]);

  useEffect(() => {
    const currentFileId = selectedFile?.id;

    if (fileUri && currentFileId) {
      // Create cache key with context
      const cacheKey = createCacheKey(currentFileId, exampleSetId, taskId);
      const cachedFile = fileCacheRef.current.get(cacheKey);

      if (cachedFile) {
        setFile(cachedFile);
        setIsFileReady(true);
        setIsFileLoading(false);
        return;
      }

      // Prevent duplicate requests for the same cache key
      if (activeFileIdRef.current === cacheKey) {
        return;
      }

      let isMounted = true;
      setIsFileReady(false);
      setIsFileLoading(true);
      activeFileIdRef.current = cacheKey;

      getFileFromUri(fileUri)
        .then((fetchedFile) => {
          if (isMounted && activeFileIdRef.current === cacheKey) {
            // Cache the file with composite key
            fileCacheRef.current.set(cacheKey, fetchedFile);
            setFile(fetchedFile);
            setIsFileReady(true);
            // Clear the active file key on successful completion
            activeFileIdRef.current = null;
          }
        })
        .catch((err) => {
          if (isMounted && activeFileIdRef.current === cacheKey) {
            setError(err instanceof Error ? err : new Error("Failed to fetch file"));
            setIsFileReady(true);
            // Clear the active file key on error
            activeFileIdRef.current = null;
          }
        })
        .finally(() => {
          if (isMounted) {
            setIsFileLoading(false);
          }
        });

      return () => {
        isMounted = false;
      };
    } else if (selectedFile && !fileUri) {
      // Don't clear file immediately when URI is not available
      setIsFileReady(true);
    } else {
      // Only clear file if no file is selected
      if (!selectedFile) {
        setFile(null);
        activeFileIdRef.current = null;
      }
      setIsFileReady(true);
    }
  }, [fileUri, selectedFile, setFile, setIsFileLoading, createCacheKey, exampleSetId, taskId]);

  const selectFileById = useCallback(
    (fileId: string) => {
      const fileToSelect = allFiles.find((file: FileInfo) => file.id === fileId);
      if (fileToSelect) {
        setSelectedFile(fileToSelect);
      }
    },
    [allFiles],
  );

  return {
    file,
    selectedFile,
    setSelectedFile,
    error,
    isLoading,
    type: selectedFile?.type,
    files,
    inputFiles,
    previousTaskOutputFiles,
    outputFiles,
    allFiles,
    selectFileById,
    hasCompletedInitialLoad,
    clearFileCache,
    forceRefreshCurrentFile,
  };
};
