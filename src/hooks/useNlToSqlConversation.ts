import { useState, useCallback, useMemo, useRef, useEffect } from "react";
import {
  useStartConversation,
  useConversation,
  useAddFeedback,
  useEndConversation,
  ConversationResponse,
  ConversationMessage,
  ConversationData,
  isSuccessResponse,
  isErrorResponse,
} from "@/api/shared/nl-to-sql-conversation";

// Export the type for use in other components
export type { ConversationMessage } from "@/api/shared/nl-to-sql-conversation";

interface UseNlToSqlConversationOptions {
  enabled?: boolean;
  workflowId: string;
  taskId: string;
  exampleSetId: string;
  initialConversation?: ConversationData;
}

export function useNlToSqlConversation({
  enabled = true,
  workflowId,
  taskId,
  exampleSetId,
  initialConversation,
}: UseNlToSqlConversationOptions) {
  const [conversationId, setConversationId] = useState<string | undefined>(
    initialConversation?.conversationId,
  );
  const [originalQuery, setOriginalQuery] = useState<string | undefined>(
    initialConversation?.originalQuery,
  );
  const [currentMessages, setCurrentMessages] = useState<ConversationMessage[]>(
    initialConversation?.messages || [],
  );
  const isMountedRef = useRef(true);

  const startConversation = useStartConversation(workflowId, taskId, exampleSetId);
  const addFeedback = useAddFeedback(workflowId, taskId, exampleSetId, conversationId);
  const endConversation = useEndConversation(workflowId, taskId, exampleSetId, conversationId);
  const conversation = useConversation(
    workflowId,
    taskId,
    exampleSetId,
    conversationId,
    enabled && !!conversationId,
  );

  // Initialize with initial conversation data if provided
  useEffect(() => {
    if (initialConversation && isMountedRef.current) {
      setConversationId(initialConversation.conversationId);
      setOriginalQuery(initialConversation.originalQuery);
      setCurrentMessages(initialConversation.messages);
    }
  }, [initialConversation]);

  // Track mounting state
  useEffect(() => {
    // Component is mounted
    isMountedRef.current = true;

    return () => {
      // Component is unmounting
      isMountedRef.current = false;
    };
  }, []);

  // Use current messages instead of fetching separately
  const conversationMessages = useMemo(() => {
    return currentMessages;
  }, [currentMessages]);

  // Send a message: start or continue conversation
  const sendMessage = useCallback(
    async (message: string): Promise<ConversationResponse | undefined> => {
      try {
        // Always optimistically add user message for instant feedback
        const userMessage: ConversationMessage = {
          id: `temp-${Date.now()}`, // Temporary ID for optimistic update
          role: "USER",
          content: message,
          timestamp: new Date().toISOString(), // Temporary timestamp
        };
        setCurrentMessages((prevMessages) => [...prevMessages, userMessage]);

        if (!conversationId) {
          // Start new conversation
          setOriginalQuery(message);
          const res = await startConversation.mutateAsync({ naturalLanguageQuery: message });

          if (res && isSuccessResponse(res) && isMountedRef.current) {
            setConversationId(res.data.conversationId);

            // Replace messages with the complete conversation from the response
            if (res.data.messages) {
              setCurrentMessages(res.data.messages);
            }
          }
          return res;
        } else {
          // Add feedback to existing conversation
          const res = await addFeedback.mutateAsync({ feedbackText: message });

          // Check if the response has errors (specifically CONVERSATION_NOT_FOUND)
          if (
            res &&
            isErrorResponse(res) &&
            res.errors.some((e) => e.code === "CONVERSATION_NOT_FOUND")
          ) {
            // Fallback: start a new conversation instead
            setOriginalQuery(message);
            const fallbackRes = await startConversation.mutateAsync({
              naturalLanguageQuery: message,
            });

            if (fallbackRes && isSuccessResponse(fallbackRes) && isMountedRef.current) {
              setConversationId(fallbackRes.data.conversationId);

              if (fallbackRes.data.messages) {
                // Replace with complete conversation from the response
                setCurrentMessages(fallbackRes.data.messages);
              }
            }

            return fallbackRes;
          }

          // Update messages from feedback response (normal success case)
          if (res && isSuccessResponse(res) && res.data.messages) {
            // Find the assistant's response (the last message should be the assistant's response)
            const serverMessages = res.data.messages;
            const assistantResponse = serverMessages[serverMessages.length - 1];

            if (assistantResponse && assistantResponse.role === "ASSISTANT") {
              // Replace the optimistic user message with the server version and append assistant response
              setCurrentMessages((prevMessages) => {
                const messagesWithoutOptimistic = prevMessages.slice(0, -1); // Remove optimistic user message
                const serverUserMessage = serverMessages[serverMessages.length - 2]; // Get server user message
                return [...messagesWithoutOptimistic, serverUserMessage, assistantResponse];
              });
            } else {
              // Fallback: replace with complete conversation if we can't identify assistant response
              setCurrentMessages(serverMessages);
            }
          }

          return res;
        }
      } catch (error: unknown) {
        // If there's an error, remove the optimistically added user message
        setCurrentMessages((prevMessages) => prevMessages.slice(0, -1));
        const errorMessage = error instanceof Error ? error.message : "Failed to send message";
        throw new Error(errorMessage);
      }
    },
    [conversationId, startConversation, addFeedback],
  );

  // Reset conversation
  const resetConversation = useCallback(() => {
    setConversationId(undefined);
    setOriginalQuery(undefined);
    setCurrentMessages([]);
  }, []);

  // End conversation (API call)
  const endCurrentConversation = useCallback(async () => {
    if (conversationId) {
      try {
        await endConversation.mutateAsync();
        if (isMountedRef.current) {
          resetConversation();
        }
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : "Failed to end conversation";
        throw new Error(errorMessage);
      }
    }
  }, [conversationId, endConversation, resetConversation]);

  return {
    conversationId,
    originalQuery,
    conversation: conversation?.data,
    conversationMessages,
    isLoading:
      (startConversation?.isPending ?? false) ||
      (addFeedback?.isPending ?? false) ||
      (conversation?.isLoading ?? false),
    isError:
      (startConversation?.isError ?? false) ||
      (addFeedback?.isError ?? false) ||
      (conversation?.isError ?? false),
    error: startConversation?.error || addFeedback?.error || conversation?.error,
    sendMessage,
    resetConversation,
    endCurrentConversation,
    isStarting: startConversation?.isPending ?? false,
    isAddingFeedback: addFeedback?.isPending ?? false,
    isFetchingConversation: conversation?.isLoading ?? false,
    isEnding: endConversation?.isPending ?? false,
  };
}
