import { describe, expect, test, vi, beforeEach, afterEach } from "vitest";
import { renderHook } from "vitest-browser-react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { createElement } from "react";
import { useNlToSqlConversation } from "./useNlToSqlConversation";
import type {
  ConversationResponse,
  ConversationMessage,
} from "@/api/shared/nl-to-sql-conversation";

// Mock the API hooks
vi.mock("@/api/shared/nl-to-sql-conversation", () => ({
  useStartConversation: vi.fn(),
  useConversation: vi.fn(),
  useAddFeedback: vi.fn(),
  useEndConversation: vi.fn(),
  isSuccessResponse: vi.fn(),
  isErrorResponse: vi.fn(),
  isConversationData: vi.fn(),
}));

// Mock conversation data
const mockConversationMessage: ConversationMessage = {
  id: "msg-1",
  role: "USER",
  content: "Show me all users",
  timestamp: "2023-10-01T10:00:00Z",
};

const mockConversationResponse: ConversationResponse = {
  data: {
    conversationId: "conv-123",
    messages: [mockConversationMessage],
    currentSql: "SELECT * FROM users",
    currentDescription: "Query to get all users",
    originalQuery: "Show me all users",
    createdAt: "2023-10-01T10:00:00Z",
    updatedAt: "2023-10-01T10:00:00Z",
  },
  errors: [],
};

// Test parameters
const testParams = {
  workflowId: "workflow-123",
  taskId: "task-456",
  exampleSetId: "example-789",
};

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return function Wrapper({ children }: { children: React.ReactNode }) {
    return createElement(QueryClientProvider, { client: queryClient }, children);
  };
};

describe("useNlToSqlConversation", () => {
  let mockStartConversation: any;
  let mockAddFeedback: any;
  let mockEndConversation: any;
  let mockUseConversation: any;

  beforeEach(async () => {
    vi.clearAllMocks();

    const {
      useStartConversation,
      useAddFeedback,
      useEndConversation,
      useConversation,
      isSuccessResponse,
      isErrorResponse,
    } = await import("@/api/shared/nl-to-sql-conversation");

    mockStartConversation = {
      mutateAsync: vi.fn(),
      isPending: false,
      isError: false,
      error: null,
      mutate: vi.fn(),
      reset: vi.fn(),
      isIdle: true,
      isSuccess: false,
      data: undefined,
      failureCount: 0,
      failureReason: null,
      status: "idle" as const,
      variables: undefined,
      context: undefined,
    };

    mockAddFeedback = {
      mutateAsync: vi.fn(),
      isPending: false,
      isError: false,
      error: null,
      mutate: vi.fn(),
      reset: vi.fn(),
      isIdle: true,
      isSuccess: false,
      data: undefined,
      failureCount: 0,
      failureReason: null,
      status: "idle" as const,
      variables: undefined,
      context: undefined,
    };

    mockEndConversation = {
      mutateAsync: vi.fn(),
      isPending: false,
      isError: false,
      error: null,
      mutate: vi.fn(),
      reset: vi.fn(),
      isIdle: true,
      isSuccess: false,
      data: undefined,
      failureCount: 0,
      failureReason: null,
      status: "idle" as const,
      variables: undefined,
      context: undefined,
    };

    mockUseConversation = {
      data: undefined,
      isLoading: false,
      isError: false,
      error: null,
      isSuccess: false,
      isIdle: true,
      status: "idle" as const,
      dataUpdatedAt: 0,
      errorUpdatedAt: 0,
      failureCount: 0,
      failureReason: null,
      fetchStatus: "idle" as const,
      isInitialLoading: false,
      isPaused: false,
      isPlaceholderData: false,
      isPreviousData: false,
      isRefetchError: false,
      isStale: true,
      refetch: vi.fn(),
      remove: vi.fn(),
    };

    vi.mocked(useStartConversation).mockReturnValue(mockStartConversation);
    vi.mocked(useAddFeedback).mockReturnValue(mockAddFeedback);
    vi.mocked(useEndConversation).mockReturnValue(mockEndConversation);
    vi.mocked(useConversation).mockReturnValue(mockUseConversation);

    // Mock type guards to return appropriate values
    vi.mocked(isSuccessResponse).mockImplementation((response) => {
      return response.errors.length === 0 && response.data && "conversationId" in response.data;
    });
    vi.mocked(isErrorResponse).mockImplementation((response) => {
      return response.errors.length > 0;
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe("Initial state", () => {
    test("should initialize with correct default values", () => {
      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      expect(result.current.conversationId).toBeUndefined();
      expect(result.current.originalQuery).toBeUndefined();
      expect(result.current.conversation).toBeUndefined();
      expect(result.current.conversationMessages).toEqual([]);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isError).toBe(false);
      expect(result.current.error).toBeNull();
    });

    test("should respect enabled option", () => {
      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            enabled: false,
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      expect(result.current.conversationId).toBeUndefined();
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe("Starting a conversation", () => {
    test("should start a new conversation successfully", async () => {
      mockStartConversation.mutateAsync.mockResolvedValue(mockConversationResponse);

      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      await result.current.sendMessage("Show me all users");

      expect(mockStartConversation.mutateAsync).toHaveBeenCalledWith({
        naturalLanguageQuery: "Show me all users",
      });
    });

    test("should handle start conversation error", async () => {
      const error = new Error("Failed to start conversation");
      mockStartConversation.mutateAsync.mockRejectedValue(error);

      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      await expect(result.current.sendMessage("Show me all users")).rejects.toThrow(
        "Failed to start conversation",
      );
    });
  });

  describe("Conversation management", () => {
    test("should reset conversation state", () => {
      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      result.current.resetConversation();

      expect(result.current.conversationId).toBeUndefined();
      expect(result.current.originalQuery).toBeUndefined();
    });

    test("should not end conversation when no conversation exists", async () => {
      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      await result.current.endCurrentConversation();

      expect(mockEndConversation.mutateAsync).not.toHaveBeenCalled();
    });
  });

  describe("Loading and error states", () => {
    test("should reflect loading state from start conversation", () => {
      mockStartConversation.isPending = true;

      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      expect(result.current.isLoading).toBe(true);
      expect(result.current.isStarting).toBe(true);
    });

    test("should reflect loading state from add feedback", () => {
      mockAddFeedback.isPending = true;

      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      expect(result.current.isLoading).toBe(true);
      expect(result.current.isAddingFeedback).toBe(true);
    });

    test("should reflect loading state from conversation fetch", () => {
      mockUseConversation.isLoading = true;

      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      expect(result.current.isLoading).toBe(true);
      expect(result.current.isFetchingConversation).toBe(true);
    });

    test("should reflect error state from start conversation", () => {
      const error = new Error("Start conversation failed");
      mockStartConversation.isError = true;
      mockStartConversation.error = error;

      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      expect(result.current.isError).toBe(true);
      expect(result.current.error).toBe(error);
    });
  });

  describe("Conversation messages", () => {
    test("should return empty array when no conversation data", () => {
      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      expect(result.current.conversationMessages).toEqual([]);
    });

    test("should return conversation messages when available", async () => {
      // Mock successful start conversation that sets messages
      mockStartConversation.mutateAsync.mockResolvedValue(mockConversationResponse);

      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      // Send a message to trigger setting messages in local state
      await result.current.sendMessage("Show me all users");

      // Wait for the next render cycle for state updates
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(result.current.conversationMessages).toEqual([mockConversationMessage]);
    });
  });

  describe("sendMessage functionality", () => {
    test("should handle empty message", async () => {
      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      await result.current.sendMessage("");

      expect(mockStartConversation.mutateAsync).toHaveBeenCalledWith({
        naturalLanguageQuery: "",
      });
    });

    test("should handle response without conversationId", async () => {
      const responseWithoutId = {
        data: {},
        errors: [],
      };
      mockStartConversation.mutateAsync.mockResolvedValue(responseWithoutId);

      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      await result.current.sendMessage("Show me all users");

      expect(result.current.conversationId).toBeUndefined();
    });

    test("should immediately add user message to conversationMessages when sending", async () => {
      // Mock a delayed response to test optimistic update
      let resolveStartConversation!: (value: any) => void;
      const startConversationPromise = new Promise((resolve) => {
        resolveStartConversation = resolve;
      });
      mockStartConversation.mutateAsync.mockReturnValue(startConversationPromise);

      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      // Start sending the message (don't await yet)
      const sendPromise = result.current.sendMessage("Show me all users");

      // Wait for the next render cycle to see the optimistic update
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Check that the user message appears immediately in conversationMessages
      expect(result.current.conversationMessages).toHaveLength(1);
      expect(result.current.conversationMessages[0]).toMatchObject({
        role: "USER",
        content: "Show me all users",
      });
      expect(result.current.conversationMessages[0].id).toMatch(/^temp-\d+$/);
      expect(result.current.conversationMessages[0].timestamp).toBeTruthy();

      // Complete the API call
      resolveStartConversation(mockConversationResponse);
      await sendPromise;

      // Wait for the next render cycle for final state update
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Verify that the messages are now replaced with server response
      expect(result.current.conversationMessages).toEqual([mockConversationMessage]);
    });

    test("should remove optimistic user message on API error", async () => {
      const error = new Error("API Error");
      mockStartConversation.mutateAsync.mockRejectedValue(error);

      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      // Verify messages are empty initially
      expect(result.current.conversationMessages).toHaveLength(0);

      // Send message and expect it to fail
      await expect(result.current.sendMessage("Show me all users")).rejects.toThrow("API Error");

      // Wait for the next render cycle
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Verify that the optimistic message was removed after error
      expect(result.current.conversationMessages).toHaveLength(0);
    });

    test("should append assistant response while keeping optimistic user message when adding feedback", async () => {
      // First, simulate an existing conversation
      const existingConversationResponse = {
        data: {
          conversationId: "conv-123",
          messages: [mockConversationMessage],
          currentSql: "SELECT * FROM users",
          currentDescription: "Query to get all users",
          originalQuery: "Show me all users",
          createdAt: "2023-10-01T10:00:00Z",
          updatedAt: "2023-10-01T10:00:00Z",
        },
        errors: [],
      };

      // Mock successful start conversation to establish conversationId
      mockStartConversation.mutateAsync.mockResolvedValue(existingConversationResponse);

      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      // Start a conversation to get conversationId
      await result.current.sendMessage("Show me all users");
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Verify we have the initial conversation
      expect(result.current.conversationId).toBe("conv-123");
      expect(result.current.conversationMessages).toHaveLength(1);

      // Now simulate adding feedback to the existing conversation
      const feedbackMessage: ConversationMessage = {
        id: "msg-2",
        role: "USER",
        content: "Add WHERE clause for active users",
        timestamp: "2023-10-01T10:05:00Z",
      };

      const assistantResponse: ConversationMessage = {
        id: "msg-3",
        role: "ASSISTANT",
        content: "I'll add a WHERE clause to filter for active users.",
        timestamp: "2023-10-01T10:05:30Z",
        metadata: {
          sqlQuery: "SELECT * FROM users WHERE status = 'active'",
          sqlDescription: "Query to get active users",
        },
      };

      const feedbackResponse = {
        data: {
          conversationId: "conv-123",
          messages: [mockConversationMessage, feedbackMessage, assistantResponse],
          currentSql: "SELECT * FROM users WHERE status = 'active'",
          currentDescription: "Query to get active users",
          originalQuery: "Show me all users",
          createdAt: "2023-10-01T10:00:00Z",
          updatedAt: "2023-10-01T10:05:30Z",
        },
        errors: [],
      };

      // Mock successful feedback response
      mockAddFeedback.mutateAsync.mockResolvedValue(feedbackResponse);

      // Send feedback message
      await result.current.sendMessage("Add WHERE clause for active users");
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Verify we have exactly 3 messages: original user, server user message, and assistant response
      expect(result.current.conversationMessages).toHaveLength(3);
      expect(result.current.conversationMessages[0]).toEqual(mockConversationMessage);
      expect(result.current.conversationMessages[1]).toEqual(feedbackMessage); // Server version
      expect(result.current.conversationMessages[2]).toEqual(assistantResponse);

      // Verify we have the correct number of user messages (original + feedback)
      const userMessages = result.current.conversationMessages.filter((msg) => msg.role === "USER");
      expect(userMessages).toHaveLength(2);
    });

    test("should initialize with initial conversation data", () => {
      const initialConversation = {
        conversationId: "conv-456",
        messages: [
          {
            id: "msg-1",
            role: "USER" as const,
            content: "Show me all users",
            timestamp: "2023-10-01T10:00:00Z",
          },
          {
            id: "msg-2",
            role: "ASSISTANT" as const,
            content: "Here's a query to get all users: SELECT * FROM users",
            timestamp: "2023-10-01T10:00:30Z",
            metadata: {
              sqlQuery: "SELECT * FROM users",
              sqlDescription: "Query to get all users",
            },
          },
        ],
        currentSql: "SELECT * FROM users",
        currentDescription: "Query to get all users",
        originalQuery: "Show me all users",
        createdAt: "2023-10-01T10:00:00Z",
        updatedAt: "2023-10-01T10:00:30Z",
      };

      const { result } = renderHook(
        () =>
          useNlToSqlConversation({
            workflowId: testParams.workflowId,
            taskId: testParams.taskId,
            exampleSetId: testParams.exampleSetId,
            initialConversation,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      // Verify the hook is initialized with the conversation data
      expect(result.current.conversationId).toBe("conv-456");
      expect(result.current.originalQuery).toBe("Show me all users");
      expect(result.current.conversationMessages).toHaveLength(2);
      expect(result.current.conversationMessages[0]).toEqual(initialConversation.messages[0]);
      expect(result.current.conversationMessages[1]).toEqual(initialConversation.messages[1]);
    });
  });
});
