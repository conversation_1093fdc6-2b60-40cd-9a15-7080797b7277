import { useEffect, useMemo, useState } from "react";
import { getFileFromUri } from "@BuilderV3/api/files";

/**
 * Hook to fetch a file from a given URL and manage its state.
 * @param fileUrl - The URL of the file to fetch.
 * @returns An object containing the file and loading state.
 */
export const useFile = (fileUrl: string | undefined) => {
  const [file, setFile] = useState<File | null>(null);
  const [isFileLoading, setIsFileLoading] = useState(false);

  useEffect(() => {
    if (typeof fileUrl !== "string") {
      setFile(null);
      return;
    }
    setIsFileLoading(true);

    getFileFromUri(fileUrl)
      .then((fileData) => {
        setFile(fileData);
      })
      .catch((error) => {
        console.error("Error fetching file from URI:", error);
        setFile(null);
      })
      .finally(() => {
        setIsFileLoading(false);
      });
  }, [fileUrl, setFile, setIsFileLoading]);

  return useMemo(
    () => ({
      file,
      isFileLoading,
    }),
    [file, isFileLoading],
  );
};
