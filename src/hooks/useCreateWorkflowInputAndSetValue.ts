import v3 from "@/services/v3";
import { CreateWorkflowInputParams, WorkflowInput } from "@floqastinc/transform-v3";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { setWorkflowInputValue } from "@Transform/api/set-workflow-input-value";

type mutateInput = CreateWorkflowInputParams & {
  input: CreateWorkflowInputParams["input"] & {
    value: string | File;
  };
};

/**
 * Custom hook to create a workflow input and set its value.
 */
export const useCreateWorkflowInputAndSetValue = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ workflowId, input }: mutateInput) => {
      const inputResponse = await v3.workflowInputs.createWorkflowInput({
        workflowId,
        input,
      });

      if (inputResponse.errors?.length) {
        throw new Error(`Failed to create workflow input: ${inputResponse.errors.join(", ")}`);
      }

      if (!inputResponse?.data?.id) {
        throw new Error("Unexpected error: no data returned from createWorkflowInput");
      }

      try {
        await setWorkflowInputValue({
          workflowId,
          workflowInputId: inputResponse.data.id,
          input: {
            ...input,
            value: {
              kind: input.type,
              value: input.value,
            },
          },
        });
      } catch (error) {
        // Rollback input creation if setting value fails
        const { errors: deleteWorkflowInputErrors } = await v3.workflowInputs.deleteWorkflowInput({
          workflowId,
          workflowInputId: inputResponse.data.id,
        });

        if (deleteWorkflowInputErrors.length) {
          throw new Error(
            `Failed to rollback input creation: ${deleteWorkflowInputErrors.join(", ")}`,
          );
        }

        throw error; // Re-throw the original error
      }

      return inputResponse.data;
    },
    onMutate(input) {
      const queryData = queryClient.getQueryData([`/workflows/${input.workflowId}/inputs`]) as {
        data: WorkflowInput[];
      };

      if (queryData?.data) {
        queryClient.setQueryData([`/workflows/${input.workflowId}/inputs`], {
          data: [
            ...queryData.data,
            {
              id: "",
              name: input.input.name,
              description: input.input.description,
              type: input.input.type,
              value: {
                kind: input.input.type,
                value: input.input.value,
              },
            },
          ],
        });
      }
      return { previousInputs: queryData?.data };
    },
    onError: async (error, { workflowId }, context) => {
      if (context?.previousInputs) {
        queryClient.setQueryData([`/workflows/${workflowId}/inputs`], {
          data: context.previousInputs,
        });
      }
      console.error("Error updating input:", error);
    },
    onSettled: (_data, _error, { workflowId }) => {
      queryClient.invalidateQueries({ queryKey: [`/workflows/${workflowId}/inputs`] });
    },
  });
};
