import { useNavigate } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { renderHook } from "vitest-browser-react";
import { useEditAgent } from "./useEditAgent";
import { useFeatureFlags } from "@/components/FeatureFlag";
import { AGENTS, BUILDER, STEPS, V3 } from "@/constants";
import { useDuplicateWorkflow } from "@v3/workflows";

vi.mock("react-router-dom", () => ({
  useNavigate: vi.fn(),
}));

vi.mock("@/components/FeatureFlag", () => ({
  useFeatureFlags: vi.fn(),
}));

vi.mock("@v3/workflows", () => ({
  useDuplicateWorkflow: vi.fn(),
}));

const mockNavigate = vi.fn();
const mockGetFlag = vi.fn();
const mockDuplicateWorkflow = {
  mutateAsync: vi.fn(),
};

vi.mocked(useNavigate).mockReturnValue(mockNavigate);
vi.mocked(useFeatureFlags).mockReturnValue({
  getFlag: mockGetFlag,
  flags: {},
  setFlag: vi.fn(),
  removeFlag: vi.fn(),
});
vi.mocked(useDuplicateWorkflow).mockReturnValue(mockDuplicateWorkflow as any);

const queryClient = new QueryClient();

const createWrapper = () => {
  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
  return TestWrapper;
};

describe("useEditAgent", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    queryClient.clear();
  });

  it("should navigate directly when workflow versioning is disabled", async () => {
    // Arrange
    mockGetFlag.mockReturnValue(false);

    const { result } = renderHook(() => useEditAgent(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current("agent-id");

    // Assert
    expect(mockNavigate).toHaveBeenCalledWith(`/builder/v3/${AGENTS}/agent-id`);
    expect(mockDuplicateWorkflow.mutateAsync).not.toHaveBeenCalled();
  });

  it("should navigate directly when workflow versioning is disabled with step id", async () => {
    // Arrange
    mockGetFlag.mockReturnValue(false);

    const { result } = renderHook(() => useEditAgent(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current("agent-id", "step-id");

    // Assert
    expect(mockNavigate).toHaveBeenCalledWith(`/builder/v3/${AGENTS}/agent-id/${STEPS}/step-id`);
    expect(mockDuplicateWorkflow.mutateAsync).not.toHaveBeenCalled();
  });

  it("should duplicate workflow and navigate when versioning is enabled", async () => {
    // Arrange
    mockGetFlag.mockReturnValue(true);
    mockDuplicateWorkflow.mutateAsync.mockResolvedValue({
      id: "duplicated-workflow-id",
    });

    const { result } = renderHook(() => useEditAgent(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current("agent-id");

    // Assert
    expect(mockDuplicateWorkflow.mutateAsync).toHaveBeenCalledWith({
      workflowId: "agent-id",
    });
    expect(mockNavigate).toHaveBeenCalledWith(`/${BUILDER}/${V3}/${AGENTS}/duplicated-workflow-id`);
  });

  it("should duplicate workflow and navigate with step id when versioning is enabled", async () => {
    // Arrange
    mockGetFlag.mockReturnValue(true);
    mockDuplicateWorkflow.mutateAsync.mockResolvedValue({
      id: "duplicated-workflow-id",
    });

    const { result } = renderHook(() => useEditAgent(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current("agent-id", "step-id");

    // Assert
    expect(mockDuplicateWorkflow.mutateAsync).toHaveBeenCalledWith({
      workflowId: "agent-id",
    });
    expect(mockNavigate).toHaveBeenCalledWith(
      `/${BUILDER}/${V3}/${AGENTS}/duplicated-workflow-id/${STEPS}/step-id`,
    );
  });

  it("should handle duplicate workflow errors gracefully", async () => {
    // Arrange
    mockGetFlag.mockReturnValue(true);
    const error = new Error("Duplicate workflow failed");
    mockDuplicateWorkflow.mutateAsync.mockRejectedValue(error);

    const { result } = renderHook(() => useEditAgent(), {
      wrapper: createWrapper(),
    });

    // Act & Assert
    await expect(result.current("agent-id")).rejects.toThrow("Duplicate workflow failed");
    expect(mockNavigate).not.toHaveBeenCalled();
  });

  it("should return a function that can be called multiple times", async () => {
    // Arrange
    mockGetFlag.mockReturnValue(false);

    const { result } = renderHook(() => useEditAgent(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current("agent-1");
    await result.current("agent-2");

    // Assert
    expect(mockNavigate).toHaveBeenCalledTimes(2);
    expect(mockNavigate).toHaveBeenNthCalledWith(1, `/builder/v3/${AGENTS}/agent-1`);
    expect(mockNavigate).toHaveBeenNthCalledWith(2, `/builder/v3/${AGENTS}/agent-2`);
  });

  it("should check the correct feature flag", async () => {
    // Arrange
    mockGetFlag.mockReturnValue(false);

    const { result } = renderHook(() => useEditAgent(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current("agent-id");

    // Assert
    expect(mockGetFlag).toHaveBeenCalledWith("enable-workflow-versioning");
  });

  it("should work with undefined step id", async () => {
    // Arrange
    mockGetFlag.mockReturnValue(true);
    mockDuplicateWorkflow.mutateAsync.mockResolvedValue({
      id: "duplicated-workflow-id",
    });

    const { result } = renderHook(() => useEditAgent(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current("agent-id", undefined);

    // Assert
    expect(mockDuplicateWorkflow.mutateAsync).toHaveBeenCalledWith({
      workflowId: "agent-id",
    });
    expect(mockNavigate).toHaveBeenCalledWith(`/${BUILDER}/${V3}/${AGENTS}/duplicated-workflow-id`);
  });
});
