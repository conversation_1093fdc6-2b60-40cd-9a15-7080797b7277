import { useNavigate } from "react-router-dom";
import { CreateExampleParams } from "@floqastinc/transform-v3";
import { useRef } from "react";
import { useCreateExampleWithJemDuplication } from "./useCreateExampleWithJemDuplication";
import { useFeatureFlags } from "@/components/FeatureFlag";
import { AGENTS, BUILDER, EXAMPLES, STEPS, V3 } from "@/constants";
import { useDuplicateWorkflow } from "@v3/workflows";
import { v3 } from "@/services/v3";

/**
 * This hook is used to edit an agent step.
 * It duplicates the workflow if the workflow versioning feature flag is enabled.
 * It then creates a new example set with the same task ID.
 * It then navigates to the new example set.
 */
export const useEditAgentStep = () => {
  const { getFlag } = useFeatureFlags();
  const isWorkflowVersioningEnabled = getFlag("enable-workflow-versioning");
  const navigate = useNavigate();
  const duplicateWorkflow = useDuplicateWorkflow();
  const workflowIdRef = useRef<string | null>(null);
  const taskIdRef = useRef<string | null>(null);

  const createExample = useCreateExampleWithJemDuplication({
    onSuccess: (newExample) => {
      navigate(
        `/${BUILDER}/${V3}/${AGENTS}/${workflowIdRef.current}/${STEPS}/${taskIdRef.current}/${EXAMPLES}/${newExample.id}`,
      );
    },
  });

  return {
    ...createExample,
    mutate: async (args: CreateExampleParams) => {
      const { workflowId, taskId, example } = args;

      taskIdRef.current = taskId;
      workflowIdRef.current = workflowId;

      const { data: workflow } = await v3.workflows.getWorkflow({ workflowId });

      if (isWorkflowVersioningEnabled && workflow?.status === "ACTIVE") {
        const duplicatedWorklow = await duplicateWorkflow.mutateAsync({ workflowId });
        workflowIdRef.current = duplicatedWorklow.id;
      }

      const newMutationArgs: CreateExampleParams = {
        workflowId: workflowIdRef.current,
        taskId,
        example,
      };

      await createExample.mutateAsync(newMutationArgs);
    },
  };
};
