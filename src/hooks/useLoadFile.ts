import { useAtom } from "jotai";
import { useCallback, useState } from "react";
import { getFileFromUri } from "@BuilderV3/api/files";
import { fileAtom, isFileLoadingAtom } from "@Transform/pages/builder/store";

export const useLoadFile = () => {
  const [file, setFile] = useAtom(fileAtom);
  const [isFileLoading, setIsFileLoading] = useAtom(isFileLoadingAtom);
  const [error, setError] = useState<Error | null>(null);

  const loadFile = useCallback(
    async (fileUrl: string) => {
      setIsFileLoading(true);
      try {
        const response = await getFileFromUri(fileUrl);
        setFile(response);
      } catch (err) {
        setError(err as Error);
      } finally {
        setIsFileLoading(false);
      }
    },
    [setFile, setIsFileLoading],
  );

  return { file, error, loadFile, isFileLoading };
};
