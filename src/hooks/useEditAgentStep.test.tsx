import { useNavigate } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { renderHook } from "vitest-browser-react";
import { useCreateExampleWithJemDuplication } from "./useCreateExampleWithJemDuplication";
import { useEditAgentStep } from "./useEditAgentStep";
import { useFeatureFlags } from "@/components/FeatureFlag";
// Constants not used in simplified tests
import { useDuplicateWorkflow } from "@v3/workflows";
import { v3 } from "@/services/v3";

vi.mock("react-router-dom", () => ({
  useNavigate: vi.fn(),
}));

vi.mock("@/components/FeatureFlag", () => ({
  useFeatureFlags: vi.fn(),
}));

vi.mock("@v3/workflows", () => ({
  useDuplicateWorkflow: vi.fn(),
}));

vi.mock("@/services/v3", () => ({
  v3: {
    workflows: {
      getWorkflow: vi.fn(),
    },
  },
}));

vi.mock("./useCreateExampleWithJemDuplication", () => ({
  useCreateExampleWithJemDuplication: vi.fn(),
}));

const mockNavigate = vi.fn();
const mockGetFlag = vi.fn();
const mockDuplicateWorkflow = {
  mutateAsync: vi.fn(),
};
const mockGetWorkflow = vi.mocked(v3.workflows.getWorkflow);
const mockUseCreateExampleWithJemDuplication = vi.mocked(useCreateExampleWithJemDuplication);
const mockCreateExample = {
  mutateAsync: vi.fn(),
  isLoading: false,
  error: null,
  data: null,
  isSuccess: false,
  isError: false,
  reset: vi.fn(),
};

vi.mocked(useNavigate).mockReturnValue(mockNavigate);
vi.mocked(useFeatureFlags).mockReturnValue({
  getFlag: mockGetFlag,
  flags: {},
  setFlag: vi.fn(),
  removeFlag: vi.fn(),
});
vi.mocked(useDuplicateWorkflow).mockReturnValue(mockDuplicateWorkflow as any);

const queryClient = new QueryClient();

const createWrapper = () => {
  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
  return TestWrapper;
};

describe("useEditAgentStep", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    queryClient.clear();
    mockUseCreateExampleWithJemDuplication.mockReturnValue(mockCreateExample as any);
  });

  it("should return createExample properties with custom mutate function", () => {
    // Arrange
    mockGetFlag.mockReturnValue(false);

    const { result } = renderHook(() => useEditAgentStep(), {
      wrapper: createWrapper(),
    });

    // Assert
    expect(result.current).toEqual(
      expect.objectContaining({
        mutate: expect.any(Function),
        isLoading: false,
        error: null,
        data: null,
        isSuccess: false,
        isError: false,
        reset: expect.any(Function),
      }),
    );
  });

  it("should call createExample with original workflowId when versioning is disabled", async () => {
    // Arrange
    mockGetFlag.mockReturnValue(false);
    mockGetWorkflow.mockResolvedValue({
      data: {
        id: "workflow-1",
        status: "ACTIVE",
        name: "Test Workflow",
        entityId: "entity-1",
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
        version: 1,
        tags: [],
      },
    } as any);

    const { result } = renderHook(() => useEditAgentStep(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current.mutate({
      workflowId: "workflow-1",
      taskId: "task-1",
      example: {
        name: "Test Example",
        status: "DRAFT",
      },
    });

    // Assert
    expect(mockGetWorkflow).toHaveBeenCalledWith({ workflowId: "workflow-1" });
    expect(mockDuplicateWorkflow.mutateAsync).not.toHaveBeenCalled();
    expect(mockCreateExample.mutateAsync).toHaveBeenCalledWith({
      workflowId: "workflow-1",
      taskId: "task-1",
      example: {
        name: "Test Example",
        status: "DRAFT",
      },
    });
  });

  it("should call createExample with duplicated workflowId when versioning is enabled and workflow is active", async () => {
    // Arrange
    mockGetFlag.mockReturnValue(true);
    mockGetWorkflow.mockResolvedValue({
      data: {
        id: "workflow-1",
        status: "ACTIVE",
        name: "Test Workflow",
        entityId: "entity-1",
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
        version: 1,
        tags: [],
      },
    } as any);
    mockDuplicateWorkflow.mutateAsync.mockResolvedValue({
      id: "duplicated-workflow-1",
    });

    const { result } = renderHook(() => useEditAgentStep(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current.mutate({
      workflowId: "workflow-1",
      taskId: "task-1",
      example: {
        name: "Test Example",
        status: "DRAFT",
      },
    });

    // Assert
    expect(mockGetWorkflow).toHaveBeenCalledWith({ workflowId: "workflow-1" });
    expect(mockDuplicateWorkflow.mutateAsync).toHaveBeenCalledWith({ workflowId: "workflow-1" });
    expect(mockCreateExample.mutateAsync).toHaveBeenCalledWith({
      workflowId: "duplicated-workflow-1",
      taskId: "task-1",
      example: {
        name: "Test Example",
        status: "DRAFT",
      },
    });
  });

  it("should not duplicate workflow when versioning is enabled but workflow is not active", async () => {
    // Arrange
    mockGetFlag.mockReturnValue(true);
    mockGetWorkflow.mockResolvedValue({
      data: {
        id: "workflow-1",
        status: "DRAFT",
        name: "Test Workflow",
        entityId: "entity-1",
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
        version: 1,
        tags: [],
      },
    } as any);

    const { result } = renderHook(() => useEditAgentStep(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current.mutate({
      workflowId: "workflow-1",
      taskId: "task-1",
      example: {
        name: "Test Example",
        status: "DRAFT",
      },
    });

    // Assert
    expect(mockGetWorkflow).toHaveBeenCalledWith({ workflowId: "workflow-1" });
    expect(mockDuplicateWorkflow.mutateAsync).not.toHaveBeenCalled();
    expect(mockCreateExample.mutateAsync).toHaveBeenCalledWith({
      workflowId: "workflow-1",
      taskId: "task-1",
      example: {
        name: "Test Example",
        status: "DRAFT",
      },
    });
  });

  it("should handle null workflow data gracefully", async () => {
    // Arrange
    mockGetFlag.mockReturnValue(true);
    mockGetWorkflow.mockResolvedValue({
      data: null,
    } as any);

    const { result } = renderHook(() => useEditAgentStep(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current.mutate({
      workflowId: "workflow-1",
      taskId: "task-1",
      example: {
        name: "Test Example",
        status: "DRAFT",
      },
    });

    // Assert
    expect(mockGetWorkflow).toHaveBeenCalledWith({ workflowId: "workflow-1" });
    expect(mockDuplicateWorkflow.mutateAsync).not.toHaveBeenCalled();
    expect(mockCreateExample.mutateAsync).toHaveBeenCalledWith({
      workflowId: "workflow-1",
      taskId: "task-1",
      example: {
        name: "Test Example",
        status: "DRAFT",
      },
    });
  });

  it("should configure useCreateExampleWithJemDuplication with onSuccess callback", () => {
    // Arrange
    mockGetFlag.mockReturnValue(false);

    renderHook(() => useEditAgentStep(), {
      wrapper: createWrapper(),
    });

    // Assert
    expect(mockUseCreateExampleWithJemDuplication).toHaveBeenCalledWith({
      onSuccess: expect.any(Function),
    });
  });

  it("should call createExample with duplicated workflow ID", async () => {
    // Arrange
    mockGetFlag.mockReturnValue(true);
    mockGetWorkflow.mockResolvedValue({
      data: {
        id: "workflow-1",
        status: "ACTIVE",
        name: "Test Workflow",
        entityId: "entity-1",
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
        version: 1,
        tags: [],
      },
    } as any);
    mockDuplicateWorkflow.mutateAsync.mockResolvedValue({
      id: "duplicated-workflow-1",
    });

    const { result } = renderHook(() => useEditAgentStep(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current.mutate({
      workflowId: "workflow-1",
      taskId: "task-1",
      example: {
        name: "Test Example",
        status: "DRAFT",
      },
    });

    // Assert
    expect(mockCreateExample.mutateAsync).toHaveBeenCalledWith({
      workflowId: "duplicated-workflow-1",
      taskId: "task-1",
      example: {
        name: "Test Example",
        status: "DRAFT",
      },
    });
  });

  it("should check the correct feature flag", () => {
    // Arrange
    mockGetFlag.mockReturnValue(false);

    renderHook(() => useEditAgentStep(), {
      wrapper: createWrapper(),
    });

    // Assert
    expect(mockGetFlag).toHaveBeenCalledWith("enable-workflow-versioning");
  });

  it("should call createExample with latest workflowId and taskId", async () => {
    // Arrange
    mockGetFlag.mockReturnValue(false);
    mockGetWorkflow.mockResolvedValue({
      data: {
        id: "workflow-1",
        status: "DRAFT",
        name: "Test Workflow",
        entityId: "entity-1",
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
        version: 1,
        tags: [],
      },
    } as any);

    const { result } = renderHook(() => useEditAgentStep(), {
      wrapper: createWrapper(),
    });

    // Act - First call
    await result.current.mutate({
      workflowId: "workflow-1",
      taskId: "task-1",
      example: {
        name: "Test Example 1",
        status: "DRAFT",
      },
    });

    // Act - Second call with different IDs
    await result.current.mutate({
      workflowId: "workflow-2",
      taskId: "task-2",
      example: {
        name: "Test Example 2",
        status: "DRAFT",
      },
    });

    // Assert - Should be called with the latest workflow and task IDs
    expect(mockCreateExample.mutateAsync).toHaveBeenLastCalledWith({
      workflowId: "workflow-2",
      taskId: "task-2",
      example: {
        name: "Test Example 2",
        status: "DRAFT",
      },
    });
  });

  it("should handle workflow duplication errors", async () => {
    // Arrange
    mockGetFlag.mockReturnValue(true);
    mockGetWorkflow.mockResolvedValue({
      data: {
        id: "workflow-1",
        status: "ACTIVE",
        name: "Test Workflow",
        entityId: "entity-1",
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
        version: 1,
        tags: [],
      },
    } as any);
    mockDuplicateWorkflow.mutateAsync.mockRejectedValue(new Error("Duplication failed"));

    const { result } = renderHook(() => useEditAgentStep(), {
      wrapper: createWrapper(),
    });

    // Act & Assert
    await expect(
      result.current.mutate({
        workflowId: "workflow-1",
        taskId: "task-1",
        example: {
          name: "Test Example",
          status: "DRAFT",
        },
      }),
    ).rejects.toThrow("Duplication failed");

    expect(mockCreateExample.mutateAsync).not.toHaveBeenCalled();
  });
});
