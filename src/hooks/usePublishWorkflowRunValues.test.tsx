import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { renderHook } from "vitest-browser-react";
import { usePublishWorkflowRunValues } from "./usePublishWorkflowRunValues";
import { v3 } from "@/services/v3";

vi.mock("@/services/v3", () => ({
  v3: {
    runs: {
      getTaskRuns: vi.fn(),
      getTaskRunOutputs: vi.fn(),
      getTaskRunInputs: vi.fn(),
    },
    tasks: {
      getTask: vi.fn(),
      updateTask: vi.fn(),
    },
    workflows: {
      replaceTaskOutputValueFromWorkflowRun: vi.fn(),
      replaceTaskInputValueFromWorkflowRun: vi.fn(),
    },
  },
}));

const mockGetTaskRuns = vi.mocked(v3.runs.getTaskRuns);
const mockGetTaskRunOutputs = vi.mocked(v3.runs.getTaskRunOutputs);
const mockGetTaskRunInputs = vi.mocked(v3.runs.getTaskRunInputs);
const mockGetTask = vi.mocked(v3.tasks.getTask);
const mockUpdateTask = vi.mocked(v3.tasks.updateTask);
const mockReplaceTaskOutputValue = vi.mocked(v3.workflows.replaceTaskOutputValueFromWorkflowRun);
const mockReplaceTaskInputValue = vi.mocked(v3.workflows.replaceTaskInputValueFromWorkflowRun);

const queryClient = new QueryClient();

const createWrapper = () => {
  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
  return TestWrapper;
};

describe("usePublishWorkflowRunValues", () => {
  // Shared mock objects
  const mockEmptyOutputsResponse = { data: [], errors: [] };
  const mockEmptyInputsResponse = { data: [], errors: [] };
  const mockEmptyUpdateResponse = { errors: [] };
  const mockEmptyReplaceResponse = { errors: [] };

  const mockCompletedTaskRun = {
    id: "task-run-1",
    taskId: "task-1",
    status: "COMPLETED",
  };

  const mockFailedTaskRun = {
    id: "task-run-1",
    taskId: "task-1",
    status: "FAILED",
  };

  const mockScriptTaskDraft = {
    data: {
      id: "task-1",
      strategy: {
        kind: "SCRIPT",
        buildStatus: "DRAFT",
      },
    },
    errors: [],
  };

  const mockScriptTaskCompiled = {
    data: {
      id: "task-1",
      strategy: {
        kind: "SCRIPT",
        buildStatus: "COMPILED",
      },
    },
    errors: [],
  };

  const mockNonScriptTask = {
    data: {
      id: "task-1",
      strategy: {
        kind: "JEM_TEMPLATE_FETCH",
      },
    },
    errors: [],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    queryClient.clear();
  });

  it("should publish values for successful task runs", async () => {
    // Arrange
    mockGetTaskRuns.mockResolvedValue({
      data: [
        {
          id: "task-run-1",
          taskId: "task-1",
          status: "COMPLETED",
        },
        {
          id: "task-run-2",
          taskId: "task-2",
          status: "FAILED",
        },
      ],
    } as any);

    mockGetTaskRunOutputs.mockResolvedValue({
      data: [
        {
          taskOutputId: "output-1",
          valueId: "value-1",
        },
      ],
      errors: [],
    } as any);

    mockGetTaskRunInputs.mockResolvedValue({
      data: [
        {
          taskInputId: "input-1",
          valueId: "value-2",
        },
      ],
      errors: [],
    } as any);

    mockGetTask.mockResolvedValue(mockScriptTaskDraft as any);

    mockReplaceTaskOutputValue.mockResolvedValue(mockEmptyReplaceResponse as any);
    mockReplaceTaskInputValue.mockResolvedValue(mockEmptyReplaceResponse as any);
    mockUpdateTask.mockResolvedValue(mockEmptyUpdateResponse as any);

    const { result } = renderHook(() => usePublishWorkflowRunValues(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current.mutateAsync({
      workflowRunId: "workflow-run-1",
      workflowId: "workflow-1",
    });

    // Assert
    expect(mockGetTaskRuns).toHaveBeenCalledWith({ workflowRunId: "workflow-run-1" });
    expect(mockGetTaskRunOutputs).toHaveBeenCalledWith({
      workflowRunId: "workflow-run-1",
      taskRunId: "task-run-1",
    });
    expect(mockGetTaskRunInputs).toHaveBeenCalledWith({
      workflowRunId: "workflow-run-1",
      taskRunId: "task-run-1",
    });
    expect(mockReplaceTaskOutputValue).toHaveBeenCalledWith({
      workflowId: "workflow-1",
      taskId: "task-1",
      taskOutputId: "output-1",
      body: {
        fromWorkflowRun: "workflow-run-1",
        fromValueId: "value-1",
      },
    });
    expect(mockReplaceTaskInputValue).toHaveBeenCalledWith({
      workflowId: "workflow-1",
      taskId: "task-1",
      taskInputId: "input-1",
      body: {
        fromWorkflowRun: "workflow-run-1",
        fromValueId: "value-2",
      },
    });
  });

  it("should update task status to COMPILED when task run is completed", async () => {
    // Arrange
    mockGetTaskRuns.mockResolvedValue({
      data: [mockCompletedTaskRun],
    } as any);

    mockGetTaskRunOutputs.mockResolvedValue(mockEmptyOutputsResponse);
    mockGetTaskRunInputs.mockResolvedValue(mockEmptyInputsResponse);

    mockGetTask.mockResolvedValue(mockScriptTaskDraft as any);

    mockUpdateTask.mockResolvedValue(mockEmptyUpdateResponse);

    const { result } = renderHook(() => usePublishWorkflowRunValues(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current.mutateAsync({
      workflowRunId: "workflow-run-1",
      workflowId: "workflow-1",
    });

    // Assert
    expect(mockUpdateTask).toHaveBeenCalledWith({
      workflowId: "workflow-1",
      taskId: "task-1",
      task: {
        strategy: {
          kind: "SCRIPT",
          buildStatus: "COMPILED",
        },
      },
    });
  });

  it("should update task status to FAILED when task run is failed", async () => {
    // Arrange
    mockGetTaskRuns.mockResolvedValue({
      data: [mockFailedTaskRun],
    } as any);

    mockGetTaskRunOutputs.mockResolvedValue(mockEmptyOutputsResponse);
    mockGetTaskRunInputs.mockResolvedValue(mockEmptyInputsResponse);

    mockGetTask.mockResolvedValue(mockScriptTaskDraft as any);

    mockUpdateTask.mockResolvedValue(mockEmptyUpdateResponse);

    const { result } = renderHook(() => usePublishWorkflowRunValues(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current.mutateAsync({
      workflowRunId: "workflow-run-1",
      workflowId: "workflow-1",
    });

    // Assert
    expect(mockUpdateTask).toHaveBeenCalledWith({
      workflowId: "workflow-1",
      taskId: "task-1",
      task: {
        strategy: {
          kind: "SCRIPT",
          buildStatus: "FAILED",
        },
      },
    });
  });

  it("should not update task status if task is not a script task", async () => {
    // Arrange
    mockGetTaskRuns.mockResolvedValue({
      data: [mockCompletedTaskRun],
    } as any);

    mockGetTaskRunOutputs.mockResolvedValue(mockEmptyOutputsResponse);
    mockGetTaskRunInputs.mockResolvedValue(mockEmptyInputsResponse);

    mockGetTask.mockResolvedValue(mockNonScriptTask as any);

    const { result } = renderHook(() => usePublishWorkflowRunValues(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current.mutateAsync({
      workflowRunId: "workflow-run-1",
      workflowId: "workflow-1",
    });

    // Assert
    expect(mockUpdateTask).not.toHaveBeenCalled();
  });

  it("should not update task status if build status is already correct", async () => {
    // Arrange
    mockGetTaskRuns.mockResolvedValue({
      data: [mockCompletedTaskRun],
    } as any);

    mockGetTaskRunOutputs.mockResolvedValue(mockEmptyOutputsResponse);
    mockGetTaskRunInputs.mockResolvedValue(mockEmptyInputsResponse);

    mockGetTask.mockResolvedValue(mockScriptTaskCompiled as any);

    const { result } = renderHook(() => usePublishWorkflowRunValues(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current.mutateAsync({
      workflowRunId: "workflow-run-1",
      workflowId: "workflow-1",
    });

    // Assert
    expect(mockUpdateTask).not.toHaveBeenCalled();
  });

  it("should handle missing task data gracefully", async () => {
    // Arrange
    mockGetTaskRuns.mockResolvedValue({
      data: [
        {
          id: "task-run-1",
          taskId: "task-1",
          status: "COMPLETED",
        },
      ],
    } as any);

    mockGetTaskRunOutputs.mockResolvedValue(mockEmptyOutputsResponse);
    mockGetTaskRunInputs.mockResolvedValue(mockEmptyInputsResponse);

    mockGetTask.mockResolvedValue({ data: undefined, errors: [] });

    const { result } = renderHook(() => usePublishWorkflowRunValues(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current.mutateAsync({
      workflowRunId: "workflow-run-1",
      workflowId: "workflow-1",
    });

    // Assert
    expect(mockUpdateTask).not.toHaveBeenCalled();
  });

  it("should handle empty task runs", async () => {
    // Arrange
    mockGetTaskRuns.mockResolvedValue({
      data: [],
    } as any);

    const { result } = renderHook(() => usePublishWorkflowRunValues(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current.mutateAsync({
      workflowRunId: "workflow-run-1",
      workflowId: "workflow-1",
    });

    // Assert
    expect(mockGetTaskRunOutputs).not.toHaveBeenCalled();
    expect(mockGetTaskRunInputs).not.toHaveBeenCalled();
    expect(mockReplaceTaskOutputValue).not.toHaveBeenCalled();
    expect(mockReplaceTaskInputValue).not.toHaveBeenCalled();
  });

  it("should handle API errors gracefully", async () => {
    // Arrange
    mockGetTaskRuns.mockResolvedValue({
      data: [
        {
          id: "task-run-1",
          taskId: "task-1",
          status: "COMPLETED",
        },
      ],
    } as any);

    mockGetTaskRunOutputs.mockResolvedValue({
      data: [
        {
          taskOutputId: "output-1",
          valueId: "value-1",
        },
      ],
    } as any);

    mockGetTaskRunInputs.mockResolvedValue(mockEmptyInputsResponse);

    mockReplaceTaskOutputValue.mockRejectedValue(new Error("API Error"));
    mockGetTask.mockResolvedValue({
      data: {
        id: "task-1",
        strategy: {
          kind: "SCRIPT",
          buildStatus: "DRAFT",
        },
      },
    } as any);

    const { result } = renderHook(() => usePublishWorkflowRunValues(), {
      wrapper: createWrapper(),
    });

    // Act - Should not throw error
    await result.current.mutateAsync({
      workflowRunId: "workflow-run-1",
      workflowId: "workflow-1",
    });

    // Assert
    expect(mockReplaceTaskOutputValue).toHaveBeenCalled();
    expect(mockUpdateTask).toHaveBeenCalled(); // Should still update task status
  });

  it("should only publish values for outputs and inputs with valueId", async () => {
    // Arrange
    mockGetTaskRuns.mockResolvedValue({
      data: [
        {
          id: "task-run-1",
          taskId: "task-1",
          status: "COMPLETED",
        },
      ],
    } as any);

    mockGetTaskRunOutputs.mockResolvedValue({
      data: [
        {
          taskOutputId: "output-1",
          valueId: "value-1",
        },
        {
          taskOutputId: "output-2",
          valueId: null,
        },
      ],
    } as any);

    mockGetTaskRunInputs.mockResolvedValue({
      data: [
        {
          taskInputId: "input-1",
          valueId: "value-2",
        },
        {
          taskInputId: "input-2",
          valueId: undefined,
        },
      ],
    } as any);

    mockGetTask.mockResolvedValue({
      data: {
        id: "task-1",
        strategy: {
          kind: "SCRIPT",
          buildStatus: "DRAFT",
        },
      },
    } as any);

    mockReplaceTaskOutputValue.mockResolvedValue(mockEmptyReplaceResponse);
    mockReplaceTaskInputValue.mockResolvedValue(mockEmptyReplaceResponse);

    const { result } = renderHook(() => usePublishWorkflowRunValues(), {
      wrapper: createWrapper(),
    });

    // Act
    await result.current.mutateAsync({
      workflowRunId: "workflow-run-1",
      workflowId: "workflow-1",
    });

    // Assert
    expect(mockReplaceTaskOutputValue).toHaveBeenCalledTimes(1);
    expect(mockReplaceTaskOutputValue).toHaveBeenCalledWith({
      workflowId: "workflow-1",
      taskId: "task-1",
      taskOutputId: "output-1",
      body: {
        fromWorkflowRun: "workflow-run-1",
        fromValueId: "value-1",
      },
    });

    expect(mockReplaceTaskInputValue).toHaveBeenCalledTimes(1);
    expect(mockReplaceTaskInputValue).toHaveBeenCalledWith({
      workflowId: "workflow-1",
      taskId: "task-1",
      taskInputId: "input-1",
      body: {
        fromWorkflowRun: "workflow-run-1",
        fromValueId: "value-2",
      },
    });
  });
});
