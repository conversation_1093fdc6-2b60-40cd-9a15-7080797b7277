import { useMutation } from "@tanstack/react-query";
import { TaskRun, TaskRunInput, TaskRunOutput } from "@floqastinc/transform-v3";
import { v3 } from "@/services/v3";

const publishTaskRunValues = async (
  taskRuns: TaskRun[],
  workflowRunId: string,
  workflowId: string,
) => {
  // Parallelize getting outputs and inputs for all tasks
  const [taskOutputData, taskInputData] = await Promise.all([
    // Get all outputs in parallel
    Promise.all(
      taskRuns.map(async (taskRun) => {
        const { data: outputs } = await v3.runs.getTaskRunOutputs({
          workflowRunId,
          taskRunId: taskRun.id,
        });
        return { taskId: taskRun.taskId, outputs };
      }),
    ),
    // Get all inputs in parallel
    Promise.all(
      taskRuns.map(async (taskRun) => {
        const { data: inputs } = await v3.runs.getTaskRunInputs({
          workflowRunId,
          taskRunId: taskRun.id,
        });
        return { taskId: taskRun.taskId, inputs };
      }),
    ),
  ]);

  // Convert arrays back to maps for easier lookup
  const taskOutputMap = new Map<string, TaskRunOutput[]>();
  taskOutputData.forEach(({ taskId, outputs }) => {
    taskOutputMap.set(taskId, outputs);
  });

  const taskInputMap = new Map<string, TaskRunInput[]>();
  taskInputData.forEach(({ taskId, inputs }) => {
    taskInputMap.set(taskId, inputs);
  });

  // Prepare all replacement operations
  const outputReplacements: Promise<any>[] = [];
  const inputReplacements: Promise<any>[] = [];

  // Collect all output replacement operations
  for (const [taskId, outputs] of Array.from(taskOutputMap.entries())) {
    for (const output of outputs) {
      if (output.valueId) {
        outputReplacements.push(
          v3.workflows
            .replaceTaskOutputValueFromWorkflowRun({
              workflowId,
              taskId,
              taskOutputId: output.taskOutputId,
              body: {
                fromWorkflowRun: workflowRunId,
                fromValueId: output.valueId,
              },
            })
            .catch((error) => {
              console.error("Error replacing task output value", error);
              return { error, taskId, outputId: output.taskOutputId };
            }),
        );
      }
    }
  }

  // Collect all input replacement operations
  for (const [taskId, inputs] of Array.from(taskInputMap.entries())) {
    for (const input of inputs) {
      if (input.valueId) {
        inputReplacements.push(
          v3.workflows
            .replaceTaskInputValueFromWorkflowRun({
              workflowId,
              taskId,
              taskInputId: input.taskInputId,
              body: {
                fromWorkflowRun: workflowRunId,
                fromValueId: input.valueId,
              },
            })
            .catch((error) => {
              console.error("Error replacing task input value", error);
              return { error, taskId, inputId: input.taskInputId };
            }),
        );
      }
    }
  }

  // Execute all replacement operations in parallel
  const [outputSettledResults, inputSettledResults] = await Promise.all([
    Promise.allSettled(outputReplacements),
    Promise.allSettled(inputReplacements),
  ]);

  // Log any errors for debugging
  const outputErrors = outputSettledResults.filter((result) => result.status === "rejected");
  const inputErrors = inputSettledResults.filter((result) => result.status === "rejected");

  if (outputErrors.length > 0) {
    console.error(`${outputErrors.length} output replacement operations failed`);
  }

  if (inputErrors.length > 0) {
    console.error(`${inputErrors.length} input replacement operations failed`);
  }
};

export const updateTaskStatusBasedOnTaskRun = async (taskRun: TaskRun, workflowId: string) => {
  const { data: task } = await v3.tasks.getTask({ workflowId, taskId: taskRun.taskId });

  if (!task || task.strategy?.kind !== "SCRIPT") {
    console.error("Task is not a script task", {
      taskId: task?.id,
      workflowId,
    });
    return;
  }

  if (taskRun.status === "COMPLETED" && task.strategy?.buildStatus !== "COMPILED") {
    await v3.tasks.updateTask({
      workflowId,
      taskId: task.id,
      task: {
        strategy: {
          kind: "SCRIPT",
          buildStatus: "COMPILED",
        },
      },
    });
  }

  if (taskRun.status === "FAILED" && task.strategy?.buildStatus !== "FAILED") {
    await v3.tasks.updateTask({
      workflowId,
      taskId: task.id,
      task: {
        strategy: {
          kind: "SCRIPT",
          buildStatus: "FAILED",
        },
      },
    });
  }
};

const markTaskAsFailed = async (taskId: string, workflowId: string) => {
  const { data: task } = await v3.tasks.getTask({ workflowId, taskId });

  if (!task || task.strategy?.kind !== "SCRIPT") {
    console.error("Task is not a script task", {
      taskId: task?.id,
      workflowId,
    });
    return;
  }

  await v3.tasks.updateTask({
    workflowId,
    taskId: task.id,
    task: {
      strategy: {
        kind: "SCRIPT",
        buildStatus: "FAILED", // @todo: should this be a new status
      },
    },
  });
};

export const markTasksAsFailed = async (taskIds: string[], workflowId: string) => {
  await Promise.all(
    taskIds.map(async (taskId) => {
      await markTaskAsFailed(taskId, workflowId);
    }),
  );
};

export const usePublishWorkflowRunValues = () => {
  return useMutation({
    mutationFn: async ({
      workflowRunId,
      workflowId,
    }: {
      workflowRunId: string;
      workflowId: string;
    }) => {
      const taskRuns = await v3.runs.getTaskRuns({ workflowRunId });

      const successfulTaskRuns = taskRuns.data.filter((taskRun) => taskRun?.status === "COMPLETED");

      if (successfulTaskRuns.length > 0) {
        await publishTaskRunValues(successfulTaskRuns, workflowRunId, workflowId);
      }

      await Promise.all(
        taskRuns.data.map(async (taskRun) => {
          await updateTaskStatusBasedOnTaskRun(taskRun, workflowId);
        }),
      );
    },
  });
};
