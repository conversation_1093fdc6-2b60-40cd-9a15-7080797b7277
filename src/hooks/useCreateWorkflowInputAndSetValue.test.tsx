import v3 from "@/services/v3";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { setWorkflowInputValue } from "@Transform/api/set-workflow-input-value";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { renderHook } from "vitest-browser-react";
import { useCreateWorkflowInputAndSetValue } from "./useCreateWorkflowInputAndSetValue";
import { DataType } from "@floqastinc/transform-v3";

vi.mock("@/services/v3", () => ({
  default: {
    workflowInputs: {
      createWorkflowInput: vi.fn(),
      deleteWorkflowInput: vi.fn(),
    },
  },
}));
vi.mock("@Transform/api/set-workflow-input-value", () => ({
  setWorkflowInputValue: vi.fn(),
}));

const mockInput = {
  workflowId: "workflow-1",
  input: {
    name: "Test Input",
    description: "desc",
    type: "TEXT" as DataType,
    value: "test-value",
  },
};

const queryClient = new QueryClient();
vi.spyOn(queryClient, "setQueryData");

const createWrapper = () => {
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe("useCreateWorkflowInputAndSetValue", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    queryClient.clear();
  });

  it("successfully creates workflow input and sets value", async () => {
    // Arrange
    queryClient.setQueryData(["/workflows/workflow-1/inputs"], {
      data: [],
    });

    vi.mocked(v3.workflowInputs.createWorkflowInput).mockResolvedValue({
      data: {
        id: "input-1",
        workflowId: "workflow-1",
        name: "Test Input",
        description: "desc",
        type: "TEXT",
      },
      errors: [],
    });
    vi.mocked(setWorkflowInputValue).mockResolvedValue({} as any);

    const { result } = renderHook(() => useCreateWorkflowInputAndSetValue(), {
      wrapper: createWrapper(),
    });

    // Act
    const res = await result.current.mutateAsync(mockInput);

    await vi.waitFor(() => result.current.isSuccess);

    // Assert
    expect(res).toEqual({
      id: "input-1",
      workflowId: "workflow-1",
      name: "Test Input",
      description: "desc",
      type: "TEXT",
    });

    expect(v3.workflowInputs.createWorkflowInput).toHaveBeenCalled();
    expect(setWorkflowInputValue).toHaveBeenCalled();
  });

  it("throws if createWorkflowInput returns errors", async () => {
    // Arrange
    (v3.workflowInputs.createWorkflowInput as any).mockResolvedValue({
      errors: ["Some error"],
    });

    const { result } = renderHook(() => useCreateWorkflowInputAndSetValue(), {
      wrapper: createWrapper(),
    });

    // Act & Assert
    await expect(result.current.mutateAsync(mockInput)).rejects.toThrow(
      "Failed to create workflow input: Some error",
    );
  });

  it("throws if createWorkflowInput returns no data", async () => {
    // Arrange
    (v3.workflowInputs.createWorkflowInput as any).mockResolvedValue({
      errors: [],
      data: null,
    });

    const { result } = renderHook(() => useCreateWorkflowInputAndSetValue(), {
      wrapper: createWrapper(),
    });

    // Act & Assert
    await expect(result.current.mutateAsync(mockInput)).rejects.toThrow(
      "Unexpected error: no data returned from createWorkflowInput",
    );
  });

  it("rolls back if setWorkflowInputValue fails and rollback succeeds", async () => {
    // Arrange
    (v3.workflowInputs.createWorkflowInput as any).mockResolvedValue({
      data: { id: "input-2" },
      errors: [],
    });
    (setWorkflowInputValue as any).mockRejectedValue(new Error("Set value failed"));
    (v3.workflowInputs.deleteWorkflowInput as any).mockResolvedValue({ errors: [] });

    const { result } = renderHook(() => useCreateWorkflowInputAndSetValue(), {
      wrapper: createWrapper(),
    });

    // Act & Assert
    await expect(result.current.mutateAsync(mockInput)).rejects.toThrow("Set value failed");
    expect(v3.workflowInputs.deleteWorkflowInput).toHaveBeenCalledWith({
      workflowId: mockInput.workflowId,
      workflowInputId: "input-2",
    });
  });

  it("throws if rollback (deleteWorkflowInput) fails", async () => {
    // Arrange
    (v3.workflowInputs.createWorkflowInput as any).mockResolvedValue({
      data: { id: "input-3" },
      errors: [],
    });
    (setWorkflowInputValue as any).mockRejectedValue(new Error("Set value failed"));
    (v3.workflowInputs.deleteWorkflowInput as any).mockResolvedValue({ errors: ["Delete failed"] });

    const { result } = renderHook(() => useCreateWorkflowInputAndSetValue(), {
      wrapper: createWrapper(),
    });

    // Act & Assert
    await expect(result.current.mutateAsync(mockInput)).rejects.toThrow(
      "Failed to rollback input creation: Delete failed",
    );
  });

  it("restores previous inputs on error", async () => {
    // Arrange
    (v3.workflowInputs.createWorkflowInput as any).mockRejectedValue(new Error("Create failed"));

    const prevInputs = [
      {
        id: "old",
        name: "Old",
        description: "",
        type: "string",
        value: { kind: "string", value: "old" },
      },
    ];
    queryClient.setQueryData(["/workflows/workflow-1/inputs"], {
      data: prevInputs,
    });

    const { result } = renderHook(() => useCreateWorkflowInputAndSetValue(), {
      wrapper: createWrapper(),
    });

    // Act
    try {
      await result.current.mutateAsync(mockInput);
    } catch {}

    // Assert
    expect((queryClient.getQueryData(["/workflows/workflow-1/inputs"]) as any)?.data).toEqual(
      prevInputs,
    );
  });
});
