import { useCallback } from "react";
import { NavigateOptions, useNavigate } from "react-router-dom";
import { AGENTS, RUNNER, V3 } from "@/constants";
import { safeGetWorkflowLatestVersion } from "@/api/bff/workflowVersions";
import { getBuilderUrlForWorkflow } from "@/utils/urls";
import { useFeatureFlags } from "@/components/FeatureFlag";

export type NavigationTarget = "builder" | "runner";

export type NavigateToLatestVersionOptions = {
  target: NavigationTarget;
  run?: boolean;
  navigateOptions?: NavigateOptions;
};

export const useNavigateToLatestVersion = () => {
  const navigate = useNavigate();
  const { getFlag } = useFeatureFlags();

  const navigateToLatestVersion = useCallback(
    async (workflowId: string, options: NavigateToLatestVersionOptions): Promise<void> => {
      const isEnabled = getFlag("enable-workflow-versioning");

      const latestVersionId = isEnabled
        ? await safeGetWorkflowLatestVersion(workflowId)
        : workflowId;

      // Build the appropriate URL based on target
      let url: string;
      if (options.target === "builder") {
        url = getBuilderUrlForWorkflow(latestVersionId);
      } else {
        // Runner target
        const searchParams = new URLSearchParams();
        if (options.run) {
          searchParams.set("run", "true");
        }
        url = `/${RUNNER}/${V3}/${AGENTS}/${latestVersionId}?${searchParams.toString()}`;
      }

      navigate(url, options.navigateOptions);
    },
    [navigate, getFlag],
  );

  const navigateToLatestVersionBuilder = useCallback(
    async (workflowId: string): Promise<void> => {
      return navigateToLatestVersion(workflowId, { target: "builder" });
    },
    [navigateToLatestVersion],
  );

  const navigateToLatestVersionRunner = useCallback(
    async (
      workflowId: string,
      options?: {
        run?: boolean;
        navigateOptions?: NavigateOptions;
      },
    ): Promise<void> => {
      return navigateToLatestVersion(workflowId, {
        target: "runner",
        run: options?.run,
        navigateOptions: options?.navigateOptions,
      });
    },
    [navigateToLatestVersion],
  );

  return {
    navigateToLatestVersion,
    navigateToLatestVersionBuilder,
    navigateToLatestVersionRunner,
  };
};
