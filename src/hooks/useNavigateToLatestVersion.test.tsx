import { NavigateOptions, useNavigate } from "react-router-dom";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { renderHook } from "vitest-browser-react";
import { useNavigateToLatestVersion } from "./useNavigateToLatestVersion";
import { useFeatureFlags } from "@/components/FeatureFlag";
import { safeGetWorkflowLatestVersion } from "@/api/bff/workflowVersions";
import { getBuilderUrlForWorkflow } from "@/utils/urls";

// Mock all dependencies
vi.mock("react-router-dom", () => ({
  useNavigate: vi.fn(),
}));

vi.mock("@/components/FeatureFlag", () => ({
  useFeatureFlags: vi.fn(),
}));

vi.mock("@/api/bff/workflowVersions", () => ({
  safeGetWorkflowLatestVersion: vi.fn(),
}));

vi.mock("@/utils/urls", () => ({
  getBuilderUrlForWorkflow: vi.fn(),
}));

vi.mock("@/constants", () => ({
  AGENTS: "agents",
  RUNNER: "runner",
  V3: "v3",
}));

// Mock implementations
const mockNavigate = vi.fn();
const mockGetFlag = vi.fn();
const mockSafeGetWorkflowLatestVersion = vi.mocked(safeGetWorkflowLatestVersion);
const mockGetBuilderUrlForWorkflow = vi.mocked(getBuilderUrlForWorkflow);

describe("useNavigateToLatestVersion", () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mock returns
    vi.mocked(useNavigate).mockReturnValue(mockNavigate);
    vi.mocked(useFeatureFlags).mockReturnValue({ getFlag: mockGetFlag } as any);
    mockGetBuilderUrlForWorkflow.mockReturnValue("/builder/v3/agents/test-workflow-id");
  });

  describe("navigateToLatestVersion", () => {
    it("GIVEN versioning is disabled, WHEN navigating to builder, THEN should use original workflowId", async () => {
      // Arrange
      mockGetFlag.mockReturnValue(false);

      const { result } = renderHook(() => useNavigateToLatestVersion());

      // Act
      await result.current.navigateToLatestVersion("test-workflow-id", { target: "builder" });

      // Assert
      expect(mockGetFlag).toHaveBeenCalledWith("enable-workflow-versioning");
      expect(mockSafeGetWorkflowLatestVersion).not.toHaveBeenCalled();
      expect(mockGetBuilderUrlForWorkflow).toHaveBeenCalledWith("test-workflow-id");
      expect(mockNavigate).toHaveBeenCalledWith("/builder/v3/agents/test-workflow-id", undefined);
    });

    it("GIVEN versioning is enabled, WHEN navigating to builder, THEN should use latest version", async () => {
      // Arrange
      mockGetFlag.mockReturnValue(true);
      mockSafeGetWorkflowLatestVersion.mockResolvedValue("latest-version-id");
      mockGetBuilderUrlForWorkflow.mockReturnValue("/builder/v3/agents/latest-version-id");

      const { result } = renderHook(() => useNavigateToLatestVersion());

      // Act
      await result.current.navigateToLatestVersion("test-workflow-id", { target: "builder" });

      // Assert
      expect(mockGetFlag).toHaveBeenCalledWith("enable-workflow-versioning");
      expect(mockSafeGetWorkflowLatestVersion).toHaveBeenCalledWith("test-workflow-id");
      expect(mockGetBuilderUrlForWorkflow).toHaveBeenCalledWith("latest-version-id");
      expect(mockNavigate).toHaveBeenCalledWith("/builder/v3/agents/latest-version-id", undefined);
    });

    it("GIVEN versioning is disabled, WHEN navigating to runner, THEN should use original workflowId", async () => {
      // Arrange
      mockGetFlag.mockReturnValue(false);

      const { result } = renderHook(() => useNavigateToLatestVersion());

      // Act
      await result.current.navigateToLatestVersion("test-workflow-id", { target: "runner" });

      // Assert
      expect(mockGetFlag).toHaveBeenCalledWith("enable-workflow-versioning");
      expect(mockSafeGetWorkflowLatestVersion).not.toHaveBeenCalled();
      expect(mockNavigate).toHaveBeenCalledWith("/runner/v3/agents/test-workflow-id?", undefined);
    });

    it("GIVEN versioning is enabled, WHEN navigating to runner, THEN should use latest version", async () => {
      // Arrange
      mockGetFlag.mockReturnValue(true);
      mockSafeGetWorkflowLatestVersion.mockResolvedValue("latest-version-id");

      const { result } = renderHook(() => useNavigateToLatestVersion());

      // Act
      await result.current.navigateToLatestVersion("test-workflow-id", { target: "runner" });

      // Assert
      expect(mockGetFlag).toHaveBeenCalledWith("enable-workflow-versioning");
      expect(mockSafeGetWorkflowLatestVersion).toHaveBeenCalledWith("test-workflow-id");
      expect(mockNavigate).toHaveBeenCalledWith("/runner/v3/agents/latest-version-id?", undefined);
    });

    it("GIVEN run option is true, WHEN navigating to runner, THEN should include run query param", async () => {
      // Arrange
      mockGetFlag.mockReturnValue(false);

      const { result } = renderHook(() => useNavigateToLatestVersion());

      // Act
      await result.current.navigateToLatestVersion("test-workflow-id", {
        target: "runner",
        run: true,
      });

      // Assert
      expect(mockNavigate).toHaveBeenCalledWith(
        "/runner/v3/agents/test-workflow-id?run=true",
        undefined,
      );
    });

    it("GIVEN navigate options are provided, WHEN navigating, THEN should pass options through", async () => {
      // Arrange
      mockGetFlag.mockReturnValue(false);
      const navigateOptions: NavigateOptions = { replace: true, state: { from: "test" } };

      const { result } = renderHook(() => useNavigateToLatestVersion());

      // Act
      await result.current.navigateToLatestVersion("test-workflow-id", {
        target: "builder",
        navigateOptions,
      });

      // Assert
      expect(mockNavigate).toHaveBeenCalledWith(
        "/builder/v3/agents/test-workflow-id",
        navigateOptions,
      );
    });
  });

  describe("navigateToLatestVersionBuilder", () => {
    it("GIVEN versioning is enabled, WHEN using builder convenience method, THEN should navigate to latest version", async () => {
      // Arrange
      mockGetFlag.mockReturnValue(true);
      mockSafeGetWorkflowLatestVersion.mockResolvedValue("latest-version-id");
      mockGetBuilderUrlForWorkflow.mockReturnValue("/builder/v3/agents/latest-version-id");

      const { result } = renderHook(() => useNavigateToLatestVersion());

      // Act
      await result.current.navigateToLatestVersionBuilder("test-workflow-id");

      // Assert
      expect(mockSafeGetWorkflowLatestVersion).toHaveBeenCalledWith("test-workflow-id");
      expect(mockGetBuilderUrlForWorkflow).toHaveBeenCalledWith("latest-version-id");
      expect(mockNavigate).toHaveBeenCalledWith("/builder/v3/agents/latest-version-id", undefined);
    });
  });

  describe("navigateToLatestVersionRunner", () => {
    it("GIVEN no options provided, WHEN using runner convenience method, THEN should navigate with empty query", async () => {
      // Arrange
      mockGetFlag.mockReturnValue(false);

      const { result } = renderHook(() => useNavigateToLatestVersion());

      // Act
      await result.current.navigateToLatestVersionRunner("test-workflow-id");

      // Assert
      expect(mockNavigate).toHaveBeenCalledWith("/runner/v3/agents/test-workflow-id?", undefined);
    });

    it("GIVEN run option is true, WHEN using runner convenience method, THEN should include run query param", async () => {
      // Arrange
      mockGetFlag.mockReturnValue(false);

      const { result } = renderHook(() => useNavigateToLatestVersion());

      // Act
      await result.current.navigateToLatestVersionRunner("test-workflow-id", { run: true });

      // Assert
      expect(mockNavigate).toHaveBeenCalledWith(
        "/runner/v3/agents/test-workflow-id?run=true",
        undefined,
      );
    });

    it("GIVEN navigate options are provided, WHEN using runner convenience method, THEN should pass options through", async () => {
      // Arrange
      mockGetFlag.mockReturnValue(false);
      const navigateOptions: NavigateOptions = { replace: true };

      const { result } = renderHook(() => useNavigateToLatestVersion());

      // Act
      await result.current.navigateToLatestVersionRunner("test-workflow-id", {
        run: true,
        navigateOptions,
      });

      // Assert
      expect(mockNavigate).toHaveBeenCalledWith(
        "/runner/v3/agents/test-workflow-id?run=true",
        navigateOptions,
      );
    });

    it("GIVEN versioning is enabled, WHEN using runner convenience method, THEN should use latest version", async () => {
      // Arrange
      mockGetFlag.mockReturnValue(true);
      mockSafeGetWorkflowLatestVersion.mockResolvedValue("latest-version-id");

      const { result } = renderHook(() => useNavigateToLatestVersion());

      // Act
      await result.current.navigateToLatestVersionRunner("test-workflow-id", { run: true });

      // Assert
      expect(mockSafeGetWorkflowLatestVersion).toHaveBeenCalledWith("test-workflow-id");
      expect(mockNavigate).toHaveBeenCalledWith(
        "/runner/v3/agents/latest-version-id?run=true",
        undefined,
      );
    });
  });

  describe("URL construction", () => {
    it("GIVEN no query params, WHEN constructing runner URL, THEN should have empty query string", async () => {
      // Arrange
      mockGetFlag.mockReturnValue(false);

      const { result } = renderHook(() => useNavigateToLatestVersion());

      // Act
      await result.current.navigateToLatestVersion("test-workflow-id", { target: "runner" });

      // Assert
      expect(mockSafeGetWorkflowLatestVersion).not.toHaveBeenCalled();
      expect(mockNavigate).toHaveBeenCalledWith(`/runner/v3/agents/test-workflow-id?`, undefined);
    });

    it("GIVEN run query param, WHEN constructing runner URL, THEN should include run in query string", async () => {
      // Arrange
      mockGetFlag.mockReturnValue(false);

      const { result } = renderHook(() => useNavigateToLatestVersion());

      // Act
      await result.current.navigateToLatestVersion("test-workflow-id", {
        target: "runner",
        run: true,
      });

      // Assert
      expect(mockNavigate).toHaveBeenCalledWith(
        `/runner/v3/agents/test-workflow-id?run=true`,
        undefined,
      );
    });
  });
});
