import { describe, it, expect } from "vitest";
import { getBuilderUrlForWorkflow, getBuilderUrlForWorkflowTask } from "../urls";

describe("getBuilderUrlForWorkflow", () => {
  it("should return the correct url", () => {
    expect(getBuilderUrlForWorkflow("123")).toBe("/builder/v3/agents/123");
  });
});

describe("getBuilderUrlForWorkflowTask", () => {
  it("should return the correct url with workflow and task ids", () => {
    expect(getBuilderUrlForWorkflowTask("workflow123", "task456")).toBe(
      "/builder/v3/agents/workflow123/steps/task456",
    );
  });

  it("should handle different workflow and task id formats", () => {
    expect(getBuilderUrlForWorkflowTask("abc-def-123", "xyz-789")).toBe(
      "/builder/v3/agents/abc-def-123/steps/xyz-789",
    );
  });

  it("should handle numeric-like string ids", () => {
    expect(getBuilderUrlForWorkflowTask("999", "888")).toBe("/builder/v3/agents/999/steps/888");
  });
});
