import { describe, it, expect } from "vitest";
import { Principal } from "@floqastinc/transform-v0";
import { getPrincipalDisplayName } from "../getPrincipalDisplayName";

describe("getPrincipalDisplayName", () => {
  const MOCK_USER_LOGIN: Principal = {
    kind: "USER_LOGIN",
    id: "123",
    name: "<PERSON>",
    teamId: "123",
    role: "ADMIN",
    externalId: "123",
    avatarUrl: "https://example.com/avatar.png",
  };

  const MOCK_FLOQAST: Principal = {
    kind: "FLOQAST",
    id: "123",
    name: "<PERSON>",
    teamId: "123",
    role: "ADMIN",
    externalId: "123",
  };

  const MOCK_API_KEY: Principal = {
    kind: "API_KEY",
    id: "123",
    friendlyName: "Key",
    teamId: "123",
    role: "ADMIN",
  };

  it("should return the correct display name for a user login", () => {
    expect(getPrincipalDisplayName(MOCK_USER_LOGIN)).toBe("John Doe");
  });

  it("should return the correct display name for a floqast", () => {
    expect(getPrincipalDisplayName(MOCK_FLOQAST)).toBe("John Doe");
  });

  it("should return the correct display name for an api key with a friendly name", () => {
    expect(getPrincipalDisplayName(MOCK_API_KEY)).toBe("Key");
  });

  it("should return the correct display name for an api key without a friendly name", () => {
    expect(
      getPrincipalDisplayName({
        ...MOCK_API_KEY,
        friendlyName: undefined,
      }),
    ).toBe("Integration");
  });
});
