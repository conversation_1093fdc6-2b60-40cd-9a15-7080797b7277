import { ExampleInput, WorkflowInput } from "@floqastinc/transform-v3";
import { match } from "ts-pattern";
import { formatDate } from "./date";

export const getInputValue = (input: ExampleInput | WorkflowInput) => {
  return match(input.value)
    .with({ kind: "TEXT" }, (value) => value.value)
    .with({ kind: "NUMBER" }, (value) => value.value)
    .with({ kind: "DATETIME" }, (value) => formatDate(value.value))
    .with({ kind: "FILE" }, (_value) => "")
    .with(undefined, () => "")
    .exhaustive();
};
