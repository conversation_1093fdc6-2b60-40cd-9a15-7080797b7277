export const LocalStorage = {
  /**
   * @param {string} key
   * @returns {unknown | null | undefined}
   */
  get(key) {
    try {
      const item = window.localStorage.getItem(key);

      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error(`Failed to parse local data: ${error}`);
      return undefined;
    }
  },
  /**
   * @param {string[]} keyArr
   * @returns {Record<string, unknown>}
   */
  getAll(keyArr) {
    return keyArr.reduce((acc, key) => {
      acc[key] = this.get(key);
      return acc;
    }, {});
  },
  /**
   * Find all keys in local storage that start with a given prefix
   * @param {string} prefix
   * @returns {Record<string, unknown>}
   */
  findAllByPrefix(prefix) {
    const keys = Object.keys(window.localStorage);
    const filteredKeys = keys.filter((key) => key.startsWith(prefix));
    return this.getAll(filteredKeys);
  },
  set(key, value) {
    try {
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Failed to set local data: ${error}`);
    }
  },
  setAll(keyValueObj) {
    Object.entries(keyValueObj).forEach(([key, value]) => {
      this.set(key, value);
    });
  },
  remove(key) {
    window.localStorage.removeItem(key);
  },
  removeAll(keys) {
    keys.forEach((key) => window.localStorage.removeItem(key));
  },
  removeIfSet(key) {
    if (window.localStorage.getItem(key)) window.localStorage.removeItem(key);
  },
};
