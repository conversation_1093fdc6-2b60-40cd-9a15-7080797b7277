/*
 * Sets the auth state on app invocation and holds onto an object in a singleton instance
 */

class AuthState {
  constructor() {
    this.authState = {};
  }

  set(key, value) {
    this.authState[key] = value;
  }

  get(key) {
    return this.authState[key];
  }
}

// this is explicitly a singleton, only a single instance of this class should exist
export default Object.freeze(new AuthState());
