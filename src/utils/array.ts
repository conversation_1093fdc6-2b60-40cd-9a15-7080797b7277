export const bisect = <T>(arr: Array<T>, predicate: (item: T) => boolean): [Array<T>, Array<T>] => {
  const index = arr.findIndex(predicate);
  if (index === -1) {
    return [arr, []];
  }
  const left = arr.slice(0, index);
  const right = arr.slice(index);
  return [left, right];
};

export const bucket = <T>(arr: Array<T>, predicate: (item: T) => boolean): [Array<T>, Array<T>] => {
  const left: Array<T> = [];
  const right: Array<T> = [];

  arr.forEach((item) => {
    if (predicate(item)) {
      left.push(item);
    } else {
      right.push(item);
    }
  });

  return [left, right];
};

export const predicateMap = <T>(
  arr: Array<T>,
  predicate: (item: T) => boolean,
  { mapLeft, mapRight }: { mapLeft: (item: T) => T; mapRight: (item: T) => T },
): Array<T> => {
  return arr.map((item) => {
    if (predicate(item)) {
      return mapLeft(item);
    } else {
      return mapRight(item);
    }
  });
};
