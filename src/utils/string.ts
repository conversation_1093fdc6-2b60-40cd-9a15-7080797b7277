/**
 * NOTE: Taken from es-toolkit
 * Converts the first character of string to upper case and the remaining to lower case.
 *
 * @template T - Literal type of the string.
 * @param {T} str - The string to be converted to uppercase.
 * @returns {Capitalize<T>} - The capitalized string.
 *
 * @example
 * const result = capitalize('fred') // returns 'Fred'
 * const result2 = capitalize('FRED') // returns 'Fred'
 */

export function capitalize<T extends string>(str: T): Capitalize<T> {
  return (str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()) as Capitalize<T>;
}

type Capitalize<T extends string> = T extends `${infer F}${infer R}`
  ? `${Uppercase<F>}${Lowercase<R>}`
  : T;

export const APP_CONSTANTS = {
  SUCCESS: "success",
  ERROR: "error",
  PENDING: "pending",
};

export const TEAMS_PAGE_STRINGS = {
  TOAST_MESSAGES: {
    CREATE_SUCCESS: "Team created successfully.",
    CREATE_ERROR: "We were unable to create the team.",
    DELETE_SUCCESS: "Team deleted successfully.",
    DELETE_ERROR: "We were unable to delete the team.",
    UPDATE_SUCCESS: "Team updated successfully.",
    UPDATE_ERROR: "We were unable to update the team.",
  },

  TOAST_TITLES: {
    CREATED: "Team created!",
    DELETED: "Team deleted!",
    ERROR: "Something went wrong!",
    UPDATED: "Team updated!",
  },

  MODAL_TITLES: {
    CREATE_TEAM: "Create Team",
    EDIT_TEAM: "Edit Team",
  },

  HEADER_TEXTS: {
    TEAMS_HEADER: "Teams",
    SWAGGER_UI: "API Swagger UI",
  },

  BUTTON_TEXTS: {
    CREATE_TEAM: "Create Team",
    DELETE_TEAM: "Delete Team",
    EDIT_TEAM: "Edit Team",
  },

  ADDITIONAL_FUNCTIONALITY_TEXT: "Additional functionality can be accessed via the",

  PAGE_ERRORS: {
    ERROR_LOADING_TEAMS: "Error loading teams",
    NO_TEAMS: "No teams found.",
  },

  PAGE_SUCCESS: {
    LOADING: "Loading...",
    LOAD_MORE: "Load More",
  },

  TABLE_HEADERS: {
    TEAM_ID: "Team ID",
    NAME: "Name",
    EXTERNAL_ID: "External ID",
    ACTIONS: "",
  },

  DROPDOWN_PANEL_TEXTS: {
    DELETE_TEAM: "Delete Team",
    EDIT_TEAM: "Edit Team",
    MANAGE_TEAM: "Manage Team",
  },
};

export const BUILDER_PAGE_STRINGS = {
  USE_DOWNLOAD_BUTTON: "You can download the updated file by clicking on the Download File button.",
};
