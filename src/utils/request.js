import authState from "./authState";

export function getLambdaEndpoint(lambda, appTld = false) {
  const apiRoot = window.location.origin;
  const domain = apiRoot.split("//")[1];
  const system = lambda.split("_")[0];
  const path = lambda.split("_")[1];

  let lambdaEndpoint;

  if (process.env.NODE_ENV === "development") {
    const portNumber =
      process.env.REACT_APP_RUNTIME_MODE === "standalone"
        ? process.env.REACT_APP_PORT
        : process.env.REACT_APP_LAMBDA_PORT_NBR;

    lambdaEndpoint = `http://localhost:8080/${path}`;
  } else if (process.env.REACT_APP_GATEWAY_HOSTNAME) {
    lambdaEndpoint = `https://${process.env.REACT_APP_GATEWAY_HOSTNAME}/${system}/${path}`;
  } else {
    lambdaEndpoint = `https://${system}.${domain}/${path}`;
  }

  return lambdaEndpoint;
}

// TODO: temp(?) shim for adding headers to fetch for SDK

const fetchOptions = (options) => {
  const headers = {
    // Authorization: `Bearer ${authState.get("jwt")}`,
    ...options.headers,
  };

  const customOptions = {
    ...options,
    credentials: "include",
    headers,
  };

  return customOptions;
};

export const fetchStuff = (url, options) => window.fetch(url, fetchOptions(options));
