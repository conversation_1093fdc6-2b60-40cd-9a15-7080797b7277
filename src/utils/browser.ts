import axios from "axios";

export function parseCookies() {
  const semiColonSeparatedCookies = document.cookie;

  if (!semiColonSeparatedCookies) {
    return {};
  }

  const parsedCookies: Record<string, string> = {};
  semiColonSeparatedCookies
    .split(";")
    .map((item) => item.trim())
    .filter(Boolean)
    .forEach((nameValuePair) => {
      const parts = nameValuePair.trim().split("=");
      if (parts && parts.length === 2) {
        const name = parts[0];
        const value = parts[1];
        parsedCookies[name] = value;
      }
    });

  return parsedCookies;
}

export function downloadFile(file: File, options: { fileName?: string } = {}) {
  const { fileName = file.name } = options;
  const url = URL.createObjectURL(file);
  const anchorLink = document.createElement("a");
  anchorLink.href = url;
  anchorLink.download = fileName ?? file.name;
  anchorLink.style.display = "none";

  anchorLink.click();

  // Equivalent to document.body.removeChild, but doesn't require
  //  appending to body.
  anchorLink.remove();
  URL.revokeObjectURL(url);
}

export function downloadFileFromUri(uri: string, options: { fileName?: string } = {}) {
  const { fileName = uri.split("/").pop() } = options;
  const anchorLink = document.createElement("a");
  anchorLink.href = uri;
  anchorLink.download = fileName ?? "";
  anchorLink.click();
  anchorLink.remove();
}

export const uploadFile = async (file: File, url: string) => {
  const s3Res = await axios.put(url, file, {
    headers: {
      "Content-Type": file.type,
    },
  });

  if (s3Res.status !== 200) {
    throw new Error(`Failed to upload file: ${s3Res.statusText}`);
  }
};
