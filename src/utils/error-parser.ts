import { StrategyKind } from "@Transform/pages/runner/ui/StepsList/types";

const genericErrorMessage = "An error occurred";
const genericActionableSteps = [
  "Try the operation again",
  "Reupload and verify the file",
  "Contact support if the issue persists",
];

interface ParsedError {
  userMessage: string;
  actionableSteps: string[];
  technicalDetails?: string;
}

interface ErrorDetails {
  message?: string;
  error?: {
    error?:
      | {
          title?: string;
          detail?: string;
          status?: number;
          code?: string;
        }
      | Array<{
          title?: string;
          detail?: string;
          status?: number;
          code?: string;
        }>;
    clientErrors?: Array<{
      message?: string;
      label?: string;
    }>;
  };
}

interface StructuredError {
  event?: string;
  details?: ErrorDetails;
}

/**
 * Creates a user-friendly message from error details
 */
function formatErrorMessage(title?: string, detail?: string): string {
  if (title && detail) return `${title}: ${detail}`;
  return title || detail || genericErrorMessage;
}

/**
 * Parses JEM export error messages and extracts actionable information
 */
function parseJemExportError(errorText: string): ParsedError {
  const jsonContent = errorText;

  let parsedError: StructuredError;
  try {
    parsedError = JSON.parse(jsonContent);
  } catch (error) {
    console.error("Error parsing error text", error);
    return parseRawErrorText(errorText);
  }

  // Extract actionable steps from client errors
  const clientErrors = parsedError.details?.error?.clientErrors;
  const actionableSteps = Array.isArray(clientErrors)
    ? clientErrors
        .map((err) => {
          if (!err?.label && !err?.message) return null;
          return formatErrorMessage(err?.label, err?.message);
        })
        .filter((step): step is string => typeof step === "string")
    : genericActionableSteps;

  // Extract technical details from error object
  const errorDetails = parsedError.details?.error?.error;

  let technicalDetails: string;

  if (Array.isArray(errorDetails)) {
    technicalDetails = errorDetails
      .map((err) => {
        if (typeof err === "string") return err;
        return formatErrorMessage(err?.title, err?.detail);
      })
      .join(", ");
  } else if (errorDetails && typeof errorDetails === "object") {
    technicalDetails = formatErrorMessage(errorDetails.title, errorDetails.detail);
  } else {
    technicalDetails = genericErrorMessage;
  }

  return {
    userMessage: parsedError.details?.message || technicalDetails || genericErrorMessage,
    actionableSteps: actionableSteps.length > 0 ? actionableSteps : genericActionableSteps,
    technicalDetails,
  };
}

/**
 * Parses complex error messages from task run logs and extracts actionable information
 */
export function parseTaskRunError(errorText: string, strategy: StrategyKind): ParsedError {
  try {
    // Remove "Error: " prefix if present
    const cleanedErrorText = errorText.startsWith("Error: ") ? errorText.substring(7) : errorText;

    switch (strategy.kind) {
      case "JEM_EXPORT":
        return parseJemExportError(cleanedErrorText);
      case "SCRIPT":
      default:
        return parseRawErrorText(cleanedErrorText);
    }
  } catch (error) {
    console.error("Error parsing error text", error);
    return parseRawErrorText(errorText);
  }
}

/**
 * Fallback parser for raw error text
 */
function parseRawErrorText(errorText: string): ParsedError {
  return {
    userMessage: errorText || genericErrorMessage,
    actionableSteps: genericActionableSteps,
    technicalDetails: errorText || genericErrorMessage,
  };
}

/**
 * Formats the parsed error for display in UI components
 */
export function formatErrorForDisplay(parsedError: ParsedError): {
  title: string;
  message: string;
  steps: string;
} {
  return {
    title: "Error Details",
    message: parsedError.userMessage,
    steps: parsedError.actionableSteps.map((step, index) => `${index + 1}. ${step}`).join("\n"),
  };
}
