import v0 from "../services/v0";

/** If this user is not authenticated, this is the default route they will be redirected to. */
export const defaultAnonRoute = "/auth/signin";

/** If this user is authenticated, this is the default route they will be redirected to. */
export const defaultAuthRoute = "/";

/**
 * Asynchronously determines the authentication status of the client and triggers corresponding callbacks.
 * This function attempts to fetch the current auth principal. If a principal is successfully fetched, it invokes
 * the `onAuthenticated` callback with that principal. If no principal is fetched or an error occurs,
 * the `onAnonymous` callback is invoked instead.
 *
 * @param {Object} opts - The options object for callback functions.
 * @param {Function} [opts.onAuthenticated] - Callback function to execute if a user is authenticated. Receives the Principal as a parameter.
 * @param {Function} [opts.onAnonymous] - Callback function to execute if a user is not authenticated or an error occurs.
 * @returns {Promise<void>} A promise that resolves when the function has completed its execution.
 */
export async function gate(opts) {
  try {
    const me = await v0.principals.getCurrentPrincipal();
    if (me.data) {
      opts?.onAuthenticated?.(me.data);
    } else {
      opts?.onAnonymous?.();
    }
  } catch {
    opts?.onAnonymous?.();
  }
}

export async function handleLogin() {
  window.location.href = defaultAuthRoute;
}
