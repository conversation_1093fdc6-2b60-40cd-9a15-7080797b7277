import React, { createContext, useContext, ReactNode, useEffect } from "react";

import { Principal, User } from "@floqastinc/transform-v0";
import v0 from "@/services/v0";

const PrincipalContext = createContext<Principal | undefined>(undefined);

const CurrentUserContext = createContext<User | undefined>(undefined);

export const PrincipalProvider: React.FC<{
  children: ReactNode;
  principal: Principal;
}> = ({ principal, children }) => {
  return (
    <PrincipalContext.Provider value={principal}>
      <CurrentUserProvider principal={principal}>{children}</CurrentUserProvider>
    </PrincipalContext.Provider>
  );
};

const CurrentUserProvider: React.FC<{
  children: ReactNode;
  principal: Principal;
}> = ({ principal, children }) => {
  const [currentUser, setCurrentUser] = React.useState<User>();
  useEffect(() => {
    if (principal.kind === "USER_LOGIN") {
      v0.users.getCurrentUser().then((res) => {
        if (res.data) setCurrentUser(res.data);
        if (res.errors.length) console.error(res.errors);
      });
    }
  }, [principal.kind]);

  return <CurrentUserContext.Provider value={currentUser}>{children}</CurrentUserContext.Provider>;
};

/**
 * Hook to access the current auth {@link Principal}.
 */
export const usePrincipal = (): Principal => {
  const context = useContext(PrincipalContext);
  if (!context) {
    throw new Error("usePrincipal must be used within a UserProvider");
  }
  return context;
};

/**
 * Hook to access the current authenticated {@link User} or
 * undefined if the current principal is not a user.
 */
export const useCurrentUser = (): User | undefined => {
  return useContext(CurrentUserContext);
};
