import { ReactNode } from "react";
import { AuthProvider } from "@floqastinc/auth-module-client";
import { authorizationManager } from "../authorization";
import { getJwt } from "../utils/jwt";
import { usePrincipal } from "./PrincipalProvider";

type AuthorizationWrapperPropTypes = {
  children: ReactNode;
  tlcModules: Record<string, boolean> | undefined;
};

export const AuthorizationWrapper = ({ children, tlcModules }: AuthorizationWrapperPropTypes) => {
  const principal = usePrincipal();

  return (
    <AuthProvider
      authorizationManager={authorizationManager}
      fetcherContext={{
        jwt: getJwt(),
        csrfToken: "",
      }}
      authorizationContext={{
        principal,
        tlcModules,
      }}
    >
      {children}
    </AuthProvider>
  );
};
