import React from "react";

/**
 * Workaround for the issue of `p` tags in <li /> tags having `block` display
 * and causing a newline break within the li tag.
 *
 * This unwraps the `p` children and discards the `p` tag.
 *
 * NOTE: This cannot be done in the Markdown component via `components={{ p: (...) => ... }}`
 *  since other line breaks in the formatting relies on the `p` tag. It can only be done by
 *  modifying the `li` tag to look for nested `p` tags.
 */
export const Li = ({
  children,
  ...rest
}: React.DetailedHTMLProps<React.LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>) => {
  type ParagraphProps = {
    children: React.ReactNode;
  };
  const children_: React.ReactNode[] = [];
  React.Children.forEach(children, (child) => {
    if (React.isValidElement(child) && child.type === "p") {
      children_.push((child as React.ReactElement<ParagraphProps>).props.children);
    } else {
      children_.push(child);
    }
  });

  return <li {...rest}>{children_}</li>;
};
