import React from "react";
import { forwardRef, useCallback, useImperativeHandle, useRef } from "react";
import styled from "styled-components";

const StyledInput = styled.textarea`
  font-family: var(--flo-sem-font-family-body);

  padding: 11px 8px;

  border-radius: 6px;
  font-weight: var(--flo-base-font-weight-4);
  font-size: var(--flo-base-font-size-3);
  font-style: normal;
  color: var(--flo-base-color-neutral-600);
  width: 100%;
  height: 100%;

  box-sizing: border-box;

  border: 1px solid var(--flo-base-color-neutral-300);
  background-color: var(--flo-sem-color-white);
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);

  &:focus {
    border: 1px solid var(--flo-base-color-blue-700);
    outline: 2px solid var(--flo-base-color-blue-300);
  }

  &::placeholder {
    color: var(--flo-base-color-neutral-400);
  }
`;

/**
 * This is an `<input>` element styled to match the general theme of FlowUI.
 * Use this instead of `<Input type="text">` when you want to match `<NumericInput>`.
 */
export const Input = StyledInput;

/**
 * This is an `<input>` element styled to match the general theme of FlowUI.
 * It only allows numeric input and will strip out any non-numeric characters.
 * Use this instead of `<Input type="currency">`.
 */
export const NumericInput = forwardRef<
  HTMLTextAreaElement,
  React.TextareaHTMLAttributes<HTMLTextAreaElement>
>((props, ref) => {
  const internalRef = useRef<HTMLTextAreaElement>(null);
  const lastKnownGoodNumber = useRef("");
  const s = useRef<number | null>(null);
  const e = useRef<number | null>(null);
  const d = useRef<"forward" | "backward" | "none" | null>(null);

  const { onInput, onSelect, ...rest } = props;

  const handleInput = (event: React.FormEvent<HTMLTextAreaElement>) => {
    const n = event.currentTarget.value;
    const numericValue = n.replace(/\D/g, "");
    if (n !== numericValue) {
      event.currentTarget.value = lastKnownGoodNumber.current;

      if (typeof s.current === "number" && typeof e.current === "number") {
        event.currentTarget.setSelectionRange(s.current, e.current, d.current ?? undefined);
      }
    } else {
      lastKnownGoodNumber.current = numericValue;
    }

    onInput?.(event);
  };

  const handleSelect = useCallback((event: React.FormEvent<HTMLTextAreaElement>) => {
    s.current = event.currentTarget.selectionStart;
    e.current = event.currentTarget.selectionEnd;
    d.current = event.currentTarget.selectionDirection;

    onSelect?.(event);
  }, []);

  useImperativeHandle(ref, () => internalRef.current as HTMLTextAreaElement, []);

  return <StyledInput {...rest} ref={internalRef} onInput={handleInput} onSelect={handleSelect} />;
});
