import React, { createContext, useContext, ReactNode } from "react";
import { useQuery } from "@tanstack/react-query";
import { Principal } from "@floqastinc/transform-v0";
import v0 from "../services/v0";
import { OverlayLoading } from "./Loading";

const PrincipalContext = createContext<Principal | null | undefined>(undefined);

export const PrincipalProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { data: principal, isLoading } = useQuery<Principal>({
    queryKey: ["principals", "current"],
    queryFn: async () => {
      const { data } = await v0.principals.getCurrentPrincipal();
      if (!data) {
        throw new Error("Failed to fetch principal");
      }
      return data;
    },
  });

  if (isLoading) {
    return <OverlayLoading />;
  }

  if (!principal) {
    return null;
  }

  return <PrincipalContext.Provider value={principal}>{children}</PrincipalContext.Provider>;
};

// eslint-disable-next-line react-refresh/only-export-components
export const usePrincipal = (): Principal => {
  const principal = useContext(PrincipalContext);

  if (principal === undefined) {
    throw new Error("usePrincipal must be used within a PrincipalProvider");
  }

  if (principal === null) {
    throw new Error("Principal is not available yet");
  }

  return principal;
};
