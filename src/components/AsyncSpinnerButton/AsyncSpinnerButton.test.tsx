import { describe, test, expect, beforeEach, vi } from "vitest";
import { userEvent } from "@vitest/browser/context";
import { AsyncSpinnerButton } from "./AsyncSpinnerButton";
import { customRender } from "@/utils/testing";

describe("AsyncSpinnerButton", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Clear console.error mock if it exists
    vi.restoreAllMocks();
  });

  test("GIVEN AsyncSpinnerButton WHEN async operation is pending THEN button should be disabled", async () => {
    let resolvePromise: () => void;
    const mockOnClick = vi.fn().mockImplementation(() => {
      return new Promise<void>((resolve) => {
        resolvePromise = resolve;
      });
    });

    const screen = customRender(
      <AsyncSpinnerButton onClick={mockOnClick}>Click me</AsyncSpinnerButton>,
    );

    const button = screen.getByRole("button");

    // Click to start async operation
    await userEvent.click(button);

    // Button should be disabled while pending
    expect(button).toBeDisabled();

    // Resolve the promise
    resolvePromise!();

    // Wait for state to update
    await vi.waitFor(() => {
      expect(button).not.toBeDisabled();
    });
  });

  test("GIVEN AsyncSpinnerButton WHEN clicked multiple times rapidly THEN onClick should only be called once", async () => {
    let resolvePromise: () => void;
    const mockOnClick = vi.fn().mockImplementation(() => {
      return new Promise<void>((resolve) => {
        resolvePromise = resolve;
      });
    });

    const screen = customRender(
      <AsyncSpinnerButton onClick={mockOnClick}>Click me</AsyncSpinnerButton>,
    );

    const button = screen.getByRole("button");

    // Click once to start async operation
    await userEvent.click(button);

    // At this point, button should be disabled and subsequent clicks won't work
    expect(button).toBeDisabled();
    expect(mockOnClick).toHaveBeenCalledTimes(1);

    // Resolve the promise
    resolvePromise!();

    // Wait for the button to be re-enabled
    await vi.waitFor(() => {
      expect(button).not.toBeDisabled();
    });
  });

  test("GIVEN AsyncSpinnerButton with onError callback WHEN onClick throws error THEN onError should be called", async () => {
    const testError = new Error("Test error");
    const mockOnClick = vi.fn().mockRejectedValue(testError);
    const mockOnError = vi.fn();

    const screen = customRender(
      <AsyncSpinnerButton onClick={mockOnClick} onError={mockOnError}>
        Click me
      </AsyncSpinnerButton>,
    );

    const button = screen.getByRole("button");
    await userEvent.click(button);

    await vi.waitFor(() => {
      expect(mockOnError).toHaveBeenCalledWith(testError);
    });
  });

  test("GIVEN AsyncSpinnerButton without onError callback WHEN onClick throws error THEN error should be logged to console", async () => {
    const testError = new Error("Test error");
    const mockOnClick = vi.fn().mockRejectedValue(testError);
    const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

    const screen = customRender(
      <AsyncSpinnerButton onClick={mockOnClick}>Click me</AsyncSpinnerButton>,
    );

    const button = screen.getByRole("button");
    await userEvent.click(button);

    await vi.waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith("AsyncSpinnerButton error:", testError);
    });

    consoleSpy.mockRestore();
  });

  test("GIVEN AsyncSpinnerButton WHEN async operation completes successfully THEN button should be re-enabled", async () => {
    const mockOnClick = vi.fn().mockResolvedValue(undefined);

    const screen = customRender(
      <AsyncSpinnerButton onClick={mockOnClick}>Click me</AsyncSpinnerButton>,
    );

    const button = screen.getByRole("button");

    await userEvent.click(button);

    await vi.waitFor(() => {
      expect(button).not.toBeDisabled();
    });
  });

  test("GIVEN AsyncSpinnerButton WHEN async operation fails THEN button should be re-enabled", async () => {
    const mockOnClick = vi.fn().mockRejectedValue(new Error("Test error"));
    const mockOnError = vi.fn();

    const screen = customRender(
      <AsyncSpinnerButton onClick={mockOnClick} onError={mockOnError}>
        Click me
      </AsyncSpinnerButton>,
    );

    const button = screen.getByRole("button");

    await userEvent.click(button);

    await vi.waitFor(() => {
      expect(button).not.toBeDisabled();
    });
  });
});
