import { ComponentProps, useState } from "react";
import { SpinnerButton } from "../SpinnerButton";

type AsyncSpinnerButtonProps = {
  children: React.ReactNode;
  /** async function to execute on click */
  onClick: () => Promise<void>;
  /** determines whether the button is disabled for external reasons */
  disabled?: boolean;
  /** callback for when the async operation fails */
  onError?: (error: Error) => void;
} & Omit<ComponentProps<typeof SpinnerButton>, "children" | "onClick" | "disabled" | "isPending">;

/**
 * A button that shows a spinner when the async operation is pending.
 */
export const AsyncSpinnerButton = ({
  children,
  onClick,
  disabled = false,
  onError,
  ...restProps
}: AsyncSpinnerButtonProps) => {
  const [isPending, setIsPending] = useState(false);

  const handleClick = async () => {
    if (isPending || disabled) return;

    setIsPending(true);
    try {
      await onClick();
    } catch (error) {
      const errorObj = error instanceof Error ? error : new Error("Unknown error");
      if (onError) {
        onError(errorObj);
      } else {
        // If no error handler provided, log to console
        console.error("AsyncSpinnerButton error:", errorObj);
      }
    } finally {
      setIsPending(false);
    }
  };

  return (
    <SpinnerButton isPending={isPending} disabled={disabled} onClick={handleClick} {...restProps}>
      {children}
    </SpinnerButton>
  );
};
