import { useState, useContext, createContext } from "react";
import PropTypes from "prop-types";

const ModalContext = createContext();

export const useModal = () => useContext(ModalContext);

export const ModalProvider = ({ children }) => {
  const [currentModal, setCurrentModal] = useState(null);

  const openModal = (modalName, modalProps) => {
    setCurrentModal({ name: modalName, props: modalProps });
  };

  const updateModalProps = (newProps) => {
    setCurrentModal((currentModal) =>
      currentModal
        ? {
            ...currentModal,
            props: { ...currentModal.props, ...newProps },
          }
        : null,
    );
  };

  const closeModal = () => {
    setCurrentModal(null);
  };

  return (
    <ModalContext.Provider value={{ currentModal, openModal, updateModalProps, closeModal }}>
      {children}
    </ModalContext.Provider>
  );
};

ModalProvider.propTypes = {
  children: PropTypes.node.isRequired,
};
