//helpers for task
import * as apiSdk from "@floqastinc/transform-v3";
import { Task } from "@floqastinc/transform-v3";
import v3, { ApiError } from "@/services/v3";

/**
 * Creates task inputs based on previous task outputs
 */
export async function createTaskInputs({
  workflowId,
  task,
  previousTaskId,
  prevTaskOutputs,
}: {
  workflowId: string;
  task: Task;
  previousTaskId: string;
  prevTaskOutputs: apiSdk.TaskOutput[];
}): Promise<apiSdk.TaskInput[]> {
  if (
    task.strategy.kind === "FLOLAKE" ||
    task.strategy.kind === "JEM_TEMPLATE_FETCH" ||
    task.strategy.kind === "PARSE_PDF"
  ) {
    return [];
  }

  const newTaskInputs = [];
  for (const prevTaskOutput of prevTaskOutputs) {
    const source: apiSdk.Source = {
      taskId: previousTaskId,
      taskOutputId: prevTaskOutput.id,
    };
    const createTaskInputQuery = await v3.taskInputs.createTaskInput({
      workflowId,
      taskId: task.id,
      input: {
        name: prevTaskOutput.name,
        type: prevTaskOutput.type,
        source,
      },
    });
    if (createTaskInputQuery.errors.length) {
      throw new ApiError(createTaskInputQuery.errors);
    }
    if (!createTaskInputQuery.data) {
      throw new Error("Unexpected data error: Failed to create task input");
    }

    newTaskInputs.push(createTaskInputQuery.data);
  }
  return newTaskInputs;
}

/**
 * Helper function to create task outputs
 */
export async function createTaskOutputs({
  workflowId,
  task,
}: {
  workflowId: string;
  task: Task;
}): Promise<apiSdk.TaskOutput> {
  let type: apiSdk.DataType = "FILE";
  switch (task.strategy.kind) {
    case "FLOLAKE":
    case "JEM_TEMPLATE_FETCH":
    case "LLM_THREAD":
    case "SCRIPT":
    case "REVIEW":
    case "PARSE_PDF":
      type = "FILE";
      break;
    case "JEM_EXPORT":
      type = "TEXT";
      break;
    default:
      throw new Error("Must specify output type for task strategy kind");
  }

  const { data: taskOutput, errors: createTaskErrors } = await v3.taskOutputs.createTaskOutput({
    workflowId,
    taskId: task.id,
    output: {
      name: `${task.name} Output`,
      type,
    },
  });

  if (createTaskErrors.length) {
    throw new ApiError(createTaskErrors);
  }
  if (!taskOutput) {
    throw new Error("Unexpected data error: Failed to create task output");
  }
  return taskOutput;
}

/**
 * Helper function to create task output and connect it to workflow output
 */
export async function createOutputAndConnectWorkflow({
  workflowId,
  task,
}: {
  workflowId: string;
  task: Task;
}): Promise<apiSdk.TaskOutput> {
  // Create task output
  const taskOutput = await createTaskOutputs({ workflowId, task });

  // Update workflow output source to point to the new task output
  const { data: workflowOutputsRes } = await v3.workflowOutputs.getWorkflowOutputs({
    workflowId,
  });
  if (workflowOutputsRes?.[0]?.id) {
    await v3.workflowOutputs.updateWorkflowOutput({
      workflowId,
      workflowOutputId: workflowOutputsRes[0].id,
      output: {
        source: { taskId: task.id, taskOutputId: taskOutput.id },
      },
    });
  }

  return taskOutput;
}

/**
 * Helper function to handle first task creation with workflow inputs
 */
export async function handleFirstTaskInputs({
  workflowId,
  task,
}: {
  workflowId: string;
  task: Task;
}): Promise<apiSdk.TaskInput[] | undefined> {
  //get workflow inputs
  const { data: workflowInputsRes } = await v3.workflowInputs.getWorkflowInputs({
    workflowId,
  });

  //if the task is not a special strategy, create task inputs linked to workflow inputs
  if (task.strategy.kind !== "JEM_TEMPLATE_FETCH" && task.strategy.kind !== "FLOLAKE") {
    // If workflow inputs exist, create task inputs linked to them
    if (workflowInputsRes?.length) {
      const taskInputsRes = await v3.taskInputs.createTaskInput({
        workflowId,
        taskId: task.id,
        input: {
          name: workflowInputsRes[0].name,
          type: workflowInputsRes[0].type,
          description: workflowInputsRes[0].description,
          source: {
            workflowInputId: workflowInputsRes[0].id,
          },
        },
      });
      if (taskInputsRes.errors?.length) {
        throw new ApiError(taskInputsRes.errors);
      }
      if (!taskInputsRes.data) {
        throw new Error("Unexpected error: no data or errors returned from createTaskInput");
      }
      return [taskInputsRes.data];
    }
  }
  return undefined;
}

/** Helper function to handle middle task creation with previous task outputs */
export async function handleMiddleTaskInputs({
  workflowId,
  task,
  previousTaskId,
}: {
  workflowId: string;
  task: Task;
  previousTaskId: string;
}): Promise<apiSdk.TaskInput[]> {
  //previousTaskId exists, we are creating a new middle task
  //get previous task outputs
  const { data: taskOutputs, errors } = await v3.taskOutputs.getTaskOutputs({
    workflowId,
    taskId: previousTaskId,
  });
  if (errors.length) {
    throw new ApiError(errors);
  }
  if (!taskOutputs) {
    throw new Error("Unexpected data error: Failed to get previous task outputs");
  }

  //create task inputs based on previous task outputs
  const taskInputs = await createTaskInputs({
    workflowId,
    task,
    previousTaskId,
    prevTaskOutputs: taskOutputs,
  });
  return taskInputs;
}

/**
 * Helper function to handle last task input creation with previous task outputs
 */
export async function handleLastTaskInputs({
  workflowId,
  task,
  previousTaskId,
}: {
  workflowId: string;
  task: Task;
  previousTaskId: string;
}): Promise<apiSdk.TaskInput[]> {
  //get previous task outputs
  const { data: taskOutputs, errors } = await v3.taskOutputs.getTaskOutputs({
    workflowId,
    taskId: previousTaskId,
  });
  if (errors.length) {
    throw new ApiError(errors);
  }
  if (!taskOutputs) {
    throw new Error("Unexpected data error: Failed to get previous task outputs");
  }

  //create task inputs based on previous task outputs
  return createTaskInputs({
    workflowId,
    task,
    previousTaskId,
    prevTaskOutputs: taskOutputs,
  });
}

/**
 * Helper function to update next task's input with current task's output
 */
export async function updateNextTaskInput({
  workflowId,
  currentTask,
  nextTask,
  taskOutput,
}: {
  workflowId: string;
  currentTask: Task;
  nextTask: Task;
  taskOutput: apiSdk.TaskOutput;
}): Promise<void> {
  if (nextTask.strategy.kind !== "JEM_TEMPLATE_FETCH" && nextTask.strategy.kind !== "FLOLAKE") {
    const { data: nextTaskInputsRes } = await v3.taskInputs.getTaskInputs({
      workflowId,
      taskId: nextTask.id,
    });

    if (nextTaskInputsRes?.[0]?.id) {
      await v3.taskInputs.updateTaskInput({
        taskId: nextTask.id,
        workflowId,
        taskInputId: nextTaskInputsRes[0].id,
        input: {
          name: taskOutput.name,
          type: taskOutput.type,
          description: taskOutput.description,
          source: { taskId: currentTask.id, taskOutputId: taskOutput.id },
        },
      });
    }
  }
}
