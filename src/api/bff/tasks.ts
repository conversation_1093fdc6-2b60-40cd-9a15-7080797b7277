import * as apiSdk from "@floqastinc/transform-v3";
import { featureFlags } from "@/components/FeatureFlag";
import v3, { ApiError } from "@/services/v3";
import { t } from "@/utils/i18n";
import {
  handleFirstTaskInputs,
  handleMiddleTaskInputs,
  handleLastTaskInputs,
  updateNextTaskInput,
  createOutputAndConnectWorkflow,
  createTaskInputs,
  createTaskOutputs,
} from "@/api/helpers/bff/tasks";

type RevertTaskArgs = {
  workflowId: string;
  taskId: string;
  tasks: apiSdk.Task[];
};
export const revertTaskMutation = async ({ workflowId, taskId, tasks }: RevertTaskArgs) => {
  const taskToRevertToIndex = tasks.findIndex((task) => task.id === taskId);

  if (taskToRevertToIndex === -1) {
    throw new Error("Task to revert to not found");
  }

  const tasksToRemove = tasks.slice(taskToRevertToIndex + 1).map((task) => task.id);
  await Promise.all(tasksToRemove.map((taskId) => v3.tasks.deleteTask({ workflowId, taskId })));

  // point workflow output source to last task after reverting
  const revertedTasks = tasks.slice(0, taskToRevertToIndex + 1);
  const lastTask = revertedTasks[revertedTasks.length - 1];

  const { data: taskOutputsRes } = await v3.taskOutputs.getTaskOutputs({
    taskId: lastTask.id,
    workflowId,
  });
  const { data: workflowOutputsRes } = await v3.workflowOutputs.getWorkflowOutputs({ workflowId });
  await v3.workflowOutputs.updateWorkflowOutput({
    workflowId,
    workflowOutputId: workflowOutputsRes[0]?.id,
    output: {
      source: { taskId: lastTask.id, taskOutputId: taskOutputsRes[0]?.id },
    },
  });
};

type DeleteTaskArgs = {
  workflowId: string;
  taskId: string;
  tasks: apiSdk.Task[];
  deleteAfter?: boolean;
};
export const deleteTaskMutation = async ({ workflowId, taskId, tasks }: DeleteTaskArgs) => {
  const taskToDeleteIndex = tasks.findIndex((task) => task.id === taskId);
  if (taskToDeleteIndex === -1) {
    throw new Error("Task to delete not found");
  }
  const tasksToRemove = tasks.slice(taskToDeleteIndex).map((task) => task.id);
  await Promise.all(tasksToRemove.map((taskId) => v3.tasks.deleteTask({ workflowId, taskId })));

  // point workflow output source to last task after deleting
  const lastTask = tasks[taskToDeleteIndex - 1];
  const { data: taskOutputsRes } = await v3.taskOutputs.getTaskOutputs({
    taskId: lastTask.id,
    workflowId,
  });
  const { data: workflowOutputsRes } = await v3.workflowOutputs.getWorkflowOutputs({ workflowId });
  await v3.workflowOutputs.updateWorkflowOutput({
    workflowId,
    workflowOutputId: workflowOutputsRes[0]?.id,
    output: {
      source: { taskId: lastTask.id, taskOutputId: taskOutputsRes[0]?.id },
    },
  });

  return lastTask;
};

export type CreateTaskParams = {
  workflowId: string;
  task: apiSdk.NewTask;
  previousTaskId?: string;
};

/**
 * Aggregate API call for creating a Task and piping previous task
 *  outputs into the inputs for the given task. Also creates the initial
 *  exampleSet.
 */
export const createTask = async ({
  workflowId,
  task,
  previousTaskId,
}: CreateTaskParams): Promise<{
  task: apiSdk.Task;
  taskInputs: apiSdk.TaskInput[] | undefined;
  taskOutput: apiSdk.TaskOutput;
  example: apiSdk.ExampleSet;
}> => {
  let createdTask: apiSdk.Task;
  let taskInputs: apiSdk.TaskInput[] | undefined;
  let taskOutput: apiSdk.TaskOutput;

  if (featureFlags.get("transform-advanced-step-manager")) {
    if (task.position !== undefined) {
      //position exists, task is either new first task or new middle task
      //create the task
      const createTaskResponse = await v3.tasks.createTask({
        workflowId,
        task,
      });

      if (createTaskResponse.errors?.length) {
        throw new ApiError(createTaskResponse.errors);
      }
      if (!createTaskResponse.data) {
        throw new Error(t("components.BuilderV3API.Errors.failedCreateTask"));
      }
      createdTask = createTaskResponse.data;

      //get new task array
      const { data: tasks, errors: getTaskErrors } = await v3.tasks.getTasks({ workflowId });
      if (getTaskErrors.length) {
        throw new ApiError(getTaskErrors);
      }

      if (previousTaskId !== undefined) {
        //previousTaskId exists, we are creating a new middle task
        taskInputs = await handleMiddleTaskInputs({
          workflowId,
          task: createdTask,
          previousTaskId,
        });
      } else {
        //previousTaskId does not exist, we are creating a new first task
        taskInputs = await handleFirstTaskInputs({
          workflowId,
          task: createdTask,
        });
      }
      //create task output
      taskOutput = await createTaskOutputs({ workflowId, task: createdTask });

      //if there is a next task and it's not a special strategy, update the next task input
      if (tasks.length > task.position + 1) {
        const nextTask = tasks[task.position + 1];
        await updateNextTaskInput({
          workflowId,
          currentTask: createdTask,
          nextTask,
          taskOutput,
        });
      }
    } else {
      //position does not exist, we are creating a new last task or the very first task
      //create the task
      const createTaskResponse = await v3.tasks.createTask({
        workflowId,
        task,
      });

      if (createTaskResponse.errors.length) {
        throw new ApiError(createTaskResponse.errors);
      }
      if (!createTaskResponse.data) {
        throw new Error("Unexpected data error: Failed to create task");
      }
      createdTask = createTaskResponse.data;

      if (previousTaskId) {
        //previousTaskId exists, we are creating a new last task
        taskInputs = await handleLastTaskInputs({
          workflowId,
          task: createdTask,
          previousTaskId,
        });
      }

      //create task outputs and connect to workflow output
      taskOutput = await createOutputAndConnectWorkflow({ workflowId, task: createdTask });
    }
  } else {
    //feature flag is off
    const createTaskResponse = await v3.tasks.createTask({
      workflowId,
      task,
    });

    if (createTaskResponse.errors.length) {
      throw new ApiError(createTaskResponse.errors);
    }
    if (!createTaskResponse.data) {
      throw new Error("Unexpected data error: Failed to create task");
    }
    createdTask = createTaskResponse.data;

    if (previousTaskId) {
      const { data: taskOutputs, errors } = await v3.taskOutputs.getTaskOutputs({
        workflowId,
        taskId: previousTaskId,
      });
      if (errors.length) {
        throw new ApiError(errors);
      }
      if (!taskOutputs) {
        throw new Error(t("components.BuilderV3API.Errors.failedGetTaskOutputs"));
      }

      taskInputs = await createTaskInputs({
        workflowId,
        task: createdTask,
        previousTaskId,
        prevTaskOutputs: taskOutputs,
      });
    }

    taskOutput = await createTaskOutputs({ workflowId, task: createdTask });

    const { data: workflowOutputsRes } = await v3.workflowOutputs.getWorkflowOutputs({
      workflowId,
    });
    await v3.workflowOutputs.updateWorkflowOutput({
      workflowId,
      workflowOutputId: workflowOutputsRes[0]?.id,
      output: {
        source: { taskId: createdTask.id, taskOutputId: taskOutput.id },
      },
    });
  }

  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const exampleResponse = await v3.examples.createExample({
    workflowId,
    taskId: createdTask.id,
    example: {
      status: "DRAFT",
      timezone,
    },
  });

  if (exampleResponse.errors?.length) {
    throw new ApiError(exampleResponse.errors);
  }
  if (!exampleResponse.data) {
    throw new Error("Unexpected data error: Failed to create example");
  }
  const example = exampleResponse.data;

  if (createdTask.strategy.kind === "JEM_TEMPLATE_FETCH") {
    try {
      const jemTemplate = await v3.jemTemplate.getJemTemplate({
        workflowId,
        taskId: createdTask.id,
        exampleSetId: example.id,
      });
      if (jemTemplate.errors?.length) {
        throw new ApiError(jemTemplate.errors);
      }
    } catch (error) {
      console.error("[createTask] Error fetching JEM Template:", error);
    }
  }
  // if transform-file-sample is undefined or true, create a file sample
  if (featureFlags.get("transform-file-sample") !== false) {
    try {
      const exampleInputs = await v3.exampleInputs.getExampleInputs({
        workflowId,
        taskId: createdTask.id,
        exampleSetId: example.id,
      });
      if (exampleInputs.errors?.length) {
        throw new ApiError(exampleInputs.errors);
      }
      await Promise.all(
        exampleInputs.data
          .filter((input) => input.value?.kind === "FILE")
          .map((exampleInput) => {
            return v3.fileSamples.createFileSample({
              workflowId,
              taskId: createdTask.id,
              exampleSetId: example.id,
              exampleInputId: exampleInput.id,
            });
          }),
      );
    } catch (e) {
      console.error("[createTask] Error creating file samples:", e);
    }
  }

  return {
    task: createdTask,
    taskInputs,
    taskOutput,
    example: exampleResponse.data!,
  };
};
