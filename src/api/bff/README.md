# API BFF (Backend for Frontend)

This folder contains aggregate operations that involve multiple API calls
usually operating across multiple resources in such a way that it comprises
a single domain action.

e.g. Creating a task involves not only creating a `Task`, but piping the output
from another task as the new input (thereby creating a TaskInput), but also involves
creating an ExampleSet and the associated ExampleSetInput as well.
