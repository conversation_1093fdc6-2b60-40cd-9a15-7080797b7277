import { describe, test, expect, beforeEach, vi, afterEach } from "vitest";
import { safeGetWorkflowLatestVersion } from "./workflowVersions";
import { v3 } from "@/services/v3";

// Mock the v3 service
vi.mock("@/services/v3", () => ({
  v3: {
    workflows: {
      getWorkflowLatestVersion: vi.fn(),
    },
  },
}));

// Mock console.error to avoid noise in test output
const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

describe("safeGetWorkflowLatestVersion", () => {
  const mockWorkflowId = "test-workflow-id";
  const mockLatestVersionId = "latest-version-id";

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    consoleSpy.mockClear();
  });

  test("GIVEN a successful API response with data WHEN called THEN should return the latest version ID", async () => {
    // Arrange
    const mockResponse = {
      errors: [],
      data: { id: mockLatestVersionId },
    } as any;

    vi.mocked(v3.workflows.getWorkflowLatestVersion).mockResolvedValue(mockResponse);

    // Act
    const result = await safeGetWorkflowLatestVersion(mockWorkflowId);

    // Assert
    expect(v3.workflows.getWorkflowLatestVersion).toHaveBeenCalledWith({
      workflowId: mockWorkflowId,
    });
    expect(result).toBe(mockLatestVersionId);
    expect(consoleSpy).not.toHaveBeenCalled();
  });

  test("GIVEN API response with errors WHEN called THEN should log errors and return original workflowId", async () => {
    // Arrange
    const mockErrors = [{ message: "Something went wrong" }, { message: "Another error" }];
    const mockResponse = {
      errors: mockErrors,
      data: { id: mockLatestVersionId },
    } as any;
    vi.mocked(v3.workflows.getWorkflowLatestVersion).mockResolvedValue(mockResponse);

    // Act
    const result = await safeGetWorkflowLatestVersion(mockWorkflowId);

    // Assert
    expect(v3.workflows.getWorkflowLatestVersion).toHaveBeenCalledWith({
      workflowId: mockWorkflowId,
    });
    expect(consoleSpy).toHaveBeenCalledWith(mockErrors);
    expect(consoleSpy).toHaveBeenCalledWith(new Error("Unexpected error getting latest version"));
    expect(result).toBe(mockWorkflowId);
  });

  test("GIVEN API response with no data WHEN called THEN should log error and return original workflowId", async () => {
    // Arrange
    const mockResponse = {
      errors: [],
      data: null,
    } as any;
    vi.mocked(v3.workflows.getWorkflowLatestVersion).mockResolvedValue(mockResponse);

    // Act
    const result = await safeGetWorkflowLatestVersion(mockWorkflowId);

    // Assert
    expect(v3.workflows.getWorkflowLatestVersion).toHaveBeenCalledWith({
      workflowId: mockWorkflowId,
    });
    expect(consoleSpy).toHaveBeenCalledWith(new Error("Unable to get latest version"));
    expect(result).toBe(mockWorkflowId);
  });

  test("GIVEN API call throws an exception WHEN called THEN should log error and return original workflowId", async () => {
    // Arrange
    const mockError = new Error("Network error");
    vi.mocked(v3.workflows.getWorkflowLatestVersion).mockRejectedValue(mockError);

    // Act
    const result = await safeGetWorkflowLatestVersion(mockWorkflowId);

    // Assert
    expect(v3.workflows.getWorkflowLatestVersion).toHaveBeenCalledWith({
      workflowId: mockWorkflowId,
    });
    expect(consoleSpy).toHaveBeenCalledWith(mockError);
    expect(result).toBe(mockWorkflowId);
  });
});
