import { v3 } from "@/services/v3";

/**
 * Returns the latest version of a workflow, or the workflowId if the latest version is not found.
 * @param workflowId - The id of the workflow to get the latest version of.
 * @returns The id of the latest version of the workflow, or the workflowId if the latest version is not found.
 */
export const safeGetWorkflowLatestVersion = async (workflowId: string) => {
  try {
    const latestVersion = await v3.workflows.getWorkflowLatestVersion({ workflowId });
    if (latestVersion.errors.length) {
      console.error(latestVersion.errors);
      throw new Error("Unexpected error getting latest version");
    }

    if (!latestVersion.data) {
      throw new Error("Unable to get latest version");
    }

    return latestVersion.data.id;
  } catch (error) {
    console.error(error);
    return workflowId;
  }
};
