/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  BulkCreateLlmMetricsParams,
  CreateLlmMetricParams,
  Error,
  GetAllMetricsParams,
  LlmMetricsOutput,
  NewLlmMetricItem,
} from "@floqastinc/transform-v3";
import {
  type InfiniteData,
  queryOptions,
  type UndefinedInitialDataOptions,
  useInfiniteQuery,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseInfiniteQuery,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useLlmMetricsLogService } from "./context";
import {
  applyPageParam,
  assert,
  compact,
  getInitialPageParam,
  getNextPageParam,
  getPreviousPageParam,
  guard,
  type PageParam,
  type QueryError,
} from "./runtime";

const useGetAllMetricsQueryOptions = (params: GetAllMetricsParams) => {
  const llmMetricsLogService = useLlmMetricsLogService();
  return queryOptions<LlmMetricsOutput, QueryError<Error[]>>({
    queryKey: [
      `/workflows/${params.workflowId}/metrics`,
      compact({
        first: params.first,
        after: params.after,
        last: params.last,
        before: params.before,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(llmMetricsLogService.getAllMetrics(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * Get all metrics for a specific workflow.
 */
export function useAllMetrics(
  params: GetAllMetricsParams,
  options?: Omit<
    UndefinedInitialDataOptions<LlmMetricsOutput, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetAllMetricsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get all metrics for a specific workflow.
 */
export function useSuspenseAllMetrics(
  params: GetAllMetricsParams,
  options?: Omit<
    UndefinedInitialDataOptions<LlmMetricsOutput, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetAllMetricsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
function useInfiniteAllMetricsQueryOptions(params: GetAllMetricsParams) {
  const llmMetricsLogService = useLlmMetricsLogService();
  return {
    queryKey: [`/workflows/${params.workflowId}/metrics`, { inifinite: true }],
    queryFn: async ({ pageParam }: PageParam) => {
      const res = await guard(
        llmMetricsLogService.getAllMetrics(applyPageParam(params, pageParam)),
      );
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data: InfiniteData<LlmMetricsOutput, string | undefined>) =>
      data.pages.flatMap((page) => page.data),
    initialPageParam: getInitialPageParam(params),
    getNextPageParam,
    getPreviousPageParam,
  };
}

/**
 * Get all metrics for a specific workflow.
 */
export const useInfiniteAllMetrics = (params: GetAllMetricsParams) => {
  const options = useInfiniteAllMetricsQueryOptions(params);
  return useInfiniteQuery(options);
};

/**
 * Get all metrics for a specific workflow.
 */
export const useSuspenseInfiniteAllMetrics = (params: GetAllMetricsParams) => {
  const options = useInfiniteAllMetricsQueryOptions(params);
  return useSuspenseInfiniteQuery(options);
};

/**
 * Bulk create LLM metrics for a workflow.
 */
export function useBulkCreateLlmMetrics(
  options?: Omit<
    UseMutationOptions<NewLlmMetricItem, QueryError<Error[]>, BulkCreateLlmMetricsParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const llmMetricsLogService = useLlmMetricsLogService();
  return useMutation({
    mutationFn: async (params: BulkCreateLlmMetricsParams) => {
      const res = await guard(llmMetricsLogService.bulkCreateLlmMetrics(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/metrics/bulk`] });
      return res.data;
    },
    ...options,
  });
}

/**
 * Log a new LLM metric for a specific task in a workflow.
 */
export function useCreateLlmMetric(
  options?: Omit<
    UseMutationOptions<NewLlmMetricItem, QueryError<Error[]>, CreateLlmMetricParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const llmMetricsLogService = useLlmMetricsLogService();
  return useMutation({
    mutationFn: async (params: CreateLlmMetricParams) => {
      const res = await guard(llmMetricsLogService.createLlmMetric(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/metrics`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}
