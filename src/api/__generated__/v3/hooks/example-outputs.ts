/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  CreateExampleOutputParams,
  DeleteExampleOutputParams,
  Error,
  ExampleOutput,
  ExampleOutputResponse,
  ExampleOutputsResponse,
  GetExampleOutputParams,
  GetExampleOutputsParams,
  GetTaskOutputExampleUriParams,
  PresignedUrl,
  PresignedUrlResponse,
} from "@floqastinc/transform-v3";
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useExampleOutputService } from "./context";
import { assert, guard, type QueryError } from "./runtime";

/**
 * Create a new output
 */
export function useCreateExampleOutput(
  options?: Omit<
    UseMutationOptions<ExampleOutput, QueryError<Error[]>, CreateExampleOutputParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const exampleOutputService = useExampleOutputService();
  return useMutation({
    mutationFn: async (params: CreateExampleOutputParams) => {
      const res = await guard(exampleOutputService.createExampleOutput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/outputs`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Delete an output
 */
export function useDeleteExampleOutput(
  options?: Omit<
    UseMutationOptions<ExampleOutput, QueryError<Error[]>, DeleteExampleOutputParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const exampleOutputService = useExampleOutputService();
  return useMutation({
    mutationFn: async (params: DeleteExampleOutputParams) => {
      const res = await guard(exampleOutputService.deleteExampleOutput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/outputs/${params.exampleOutputId}`,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/outputs`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetExampleOutputQueryOptions = (params: GetExampleOutputParams) => {
  const exampleOutputService = useExampleOutputService();
  return queryOptions<ExampleOutputResponse, QueryError<Error[]>, ExampleOutput>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/outputs/${params.exampleOutputId}`,
    ],
    queryFn: async () => {
      const res = await guard(exampleOutputService.getExampleOutput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get an output
 */
export function useExampleOutput(
  params: GetExampleOutputParams,
  options?: Omit<
    UndefinedInitialDataOptions<ExampleOutputResponse, QueryError<Error[]>, ExampleOutput>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetExampleOutputQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get an output
 */
export function useSuspenseExampleOutput(
  params: GetExampleOutputParams,
  options?: Omit<
    UndefinedInitialDataOptions<ExampleOutputResponse, QueryError<Error[]>, ExampleOutput>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetExampleOutputQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetExampleOutputsQueryOptions = (params: GetExampleOutputsParams) => {
  const exampleOutputService = useExampleOutputService();
  return queryOptions<ExampleOutputsResponse, QueryError<Error[]>, ExampleOutput[]>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/outputs`,
    ],
    queryFn: async () => {
      const res = await guard(exampleOutputService.getExampleOutputs(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all outputs for an example set
 */
export function useExampleOutputs(
  params: GetExampleOutputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<ExampleOutputsResponse, QueryError<Error[]>, ExampleOutput[]>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetExampleOutputsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all outputs for an example set
 */
export function useSuspenseExampleOutputs(
  params: GetExampleOutputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<ExampleOutputsResponse, QueryError<Error[]>, ExampleOutput[]>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetExampleOutputsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskOutputExampleUriQueryOptions = (params: GetTaskOutputExampleUriParams) => {
  const exampleOutputService = useExampleOutputService();
  return queryOptions<PresignedUrlResponse, QueryError<Error[]>, PresignedUrl>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/outputs/${params.exampleOutputId}/value/uri`,
    ],
    queryFn: async () => {
      const res = await guard(exampleOutputService.getTaskOutputExampleUri(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get the URI for an example output
 */
export function useTaskOutputExampleUri(
  params: GetTaskOutputExampleUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<PresignedUrlResponse, QueryError<Error[]>, PresignedUrl>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetTaskOutputExampleUriQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get the URI for an example output
 */
export function useSuspenseTaskOutputExampleUri(
  params: GetTaskOutputExampleUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<PresignedUrlResponse, QueryError<Error[]>, PresignedUrl>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetTaskOutputExampleUriQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
