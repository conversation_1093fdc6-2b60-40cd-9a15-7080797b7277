/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  CleanupInputFileParams,
  CreateFileSampleParams,
  EmptyResponseData,
  Error,
  FileSampleDownloadUriResponse,
  FileSampleDownloadUriResponseData,
  FileSizeInfoResponse,
  FileSizeInfoResponseData,
  GetFileSampleDownloadUriParams,
  HandleGetFileSizeParams,
  OutputValue,
  RemoveInputFileValueParams,
  ReplaceS3FileWithSampleParams,
} from "@floqastinc/transform-v3";
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useFileSampleService } from "./context";
import { assert, guard, type QueryError } from "./runtime";

/**
 * Clean up an input file via cascading deletions (removes from S3, workflow values, example inputs, task inputs, and workflow inputs)
 */
export function useCleanupInputFile(
  options?: Omit<
    UseMutationOptions<EmptyResponseData, QueryError<Error[]>, CleanupInputFileParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const fileSampleService = useFileSampleService();
  return useMutation({
    mutationFn: async (params: CleanupInputFileParams) => {
      const res = await guard(fileSampleService.cleanupInputFile(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs/${params.exampleInputId}/cleanup`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Create an input file sample
 */
export function useCreateFileSample(
  options?: Omit<
    UseMutationOptions<EmptyResponseData, QueryError<Error[]>, CreateFileSampleParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const fileSampleService = useFileSampleService();
  return useMutation({
    mutationFn: async (params: CreateFileSampleParams) => {
      const res = await guard(fileSampleService.createFileSample(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs/${params.exampleInputId}/value/file/sample`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetFileSampleDownloadUriQueryOptions = (params: GetFileSampleDownloadUriParams) => {
  const fileSampleService = useFileSampleService();
  return queryOptions<
    FileSampleDownloadUriResponse,
    QueryError<Error[]>,
    FileSampleDownloadUriResponseData
  >({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs/${params.exampleInputId}/value/file/sample/uri`,
    ],
    queryFn: async () => {
      const res = await guard(fileSampleService.getFileSampleDownloadUri(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Retrieve a presigned S3 download URI for the file sample associated with the specified example input. The sample is located using the sampleValueId field from the input value.
 */
export function useFileSampleDownloadUri(
  params: GetFileSampleDownloadUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<
      FileSampleDownloadUriResponse,
      QueryError<Error[]>,
      FileSampleDownloadUriResponseData
    >,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetFileSampleDownloadUriQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Retrieve a presigned S3 download URI for the file sample associated with the specified example input. The sample is located using the sampleValueId field from the input value.
 */
export function useSuspenseFileSampleDownloadUri(
  params: GetFileSampleDownloadUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<
      FileSampleDownloadUriResponse,
      QueryError<Error[]>,
      FileSampleDownloadUriResponseData
    >,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetFileSampleDownloadUriQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useHandleGetFileSizeQueryOptions = (params: HandleGetFileSizeParams) => {
  const fileSampleService = useFileSampleService();
  return queryOptions<FileSizeInfoResponse, QueryError<Error[]>, FileSizeInfoResponseData>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs/${params.exampleInputId}/value/file/size`,
    ],
    queryFn: async () => {
      const res = await guard(fileSampleService.handleGetFileSize(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get file size information for an input file
 */
export function useHandleGetFileSize(
  params: HandleGetFileSizeParams,
  options?: Omit<
    UndefinedInitialDataOptions<
      FileSizeInfoResponse,
      QueryError<Error[]>,
      FileSizeInfoResponseData
    >,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useHandleGetFileSizeQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get file size information for an input file
 */
export function useHandleGetFileSize(
  params: HandleGetFileSizeParams,
  options?: Omit<
    UndefinedInitialDataOptions<
      FileSizeInfoResponse,
      QueryError<Error[]>,
      FileSizeInfoResponseData
    >,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useHandleGetFileSizeQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

/**
 * Remove an input file value from workflow
 */
export function useRemoveInputFileValue(
  options?: Omit<
    UseMutationOptions<OutputValue, QueryError<Error[]>, RemoveInputFileValueParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const fileSampleService = useFileSampleService();
  return useMutation({
    mutationFn: async (params: RemoveInputFileValueParams) => {
      const res = await guard(fileSampleService.removeInputFileValue(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/value/${params.inputValueId}`],
      });
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/value`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Replace the original S3 file with a trimmed sample version. This operation downloads the sample file from S3 and overwrites the original file with the trimmed content.
 */
export function useReplaceS3FileWithSample(
  options?: Omit<
    UseMutationOptions<EmptyResponseData, QueryError<Error[]>, ReplaceS3FileWithSampleParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const fileSampleService = useFileSampleService();
  return useMutation({
    mutationFn: async (params: ReplaceS3FileWithSampleParams) => {
      const res = await guard(fileSampleService.replaceS3FileWithSample(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs/${params.exampleInputId}/value/file/sample/replace`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}
