/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  CreateMessageParams,
  DeleteMessageParams,
  DeleteMessageResponseData,
  EditMessageParams,
  EditMessageResponseData,
  EmptyResponseData,
  Error,
  GetMessageParams,
  GetMessagesParams,
  Message,
  MessageResponse,
  MessagesResponse,
  RegenerateMessageParams,
  RegenerateMessageResponseData,
} from "@floqastinc/transform-v3";
import {
  type InfiniteData,
  queryOptions,
  type UndefinedInitialDataOptions,
  useInfiniteQuery,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseInfiniteQuery,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useMessageService } from "./context";
import {
  applyPageParam,
  assert,
  compact,
  getInitialPageParam,
  getNextPageParam,
  getPreviousPageParam,
  guard,
  type PageParam,
  type QueryError,
} from "./runtime";

/**
 * Create a new message for a thread
 */
export function useCreateMessage(
  options?: Omit<
    UseMutationOptions<EmptyResponseData, QueryError<Error[]>, CreateMessageParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const messageService = useMessageService();
  return useMutation({
    mutationFn: async (params: CreateMessageParams) => {
      const res = await guard(messageService.createMessage(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/messages`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Delete a message
 */
export function useDeleteMessage(
  options?: Omit<
    UseMutationOptions<DeleteMessageResponseData, QueryError<Error[]>, DeleteMessageParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const messageService = useMessageService();
  return useMutation({
    mutationFn: async (params: DeleteMessageParams) => {
      const res = await guard(messageService.deleteMessage(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/messages/${params.messageId}`,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/messages`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Edit a message
 */
export function useEditMessage(
  options?: Omit<
    UseMutationOptions<EditMessageResponseData, QueryError<Error[]>, EditMessageParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const messageService = useMessageService();
  return useMutation({
    mutationFn: async (params: EditMessageParams) => {
      const res = await guard(messageService.editMessage(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/messages/${params.messageId}`,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/messages`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetMessageQueryOptions = (params: GetMessageParams) => {
  const messageService = useMessageService();
  return queryOptions<MessageResponse, QueryError<Error[]>, Message>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/messages/${params.messageId}`,
    ],
    queryFn: async () => {
      const res = await guard(messageService.getMessage(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get a message
 */
export function useMessage(
  params: GetMessageParams,
  options?: Omit<
    UndefinedInitialDataOptions<MessageResponse, QueryError<Error[]>, Message>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetMessageQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get a message
 */
export function useSuspenseMessage(
  params: GetMessageParams,
  options?: Omit<
    UndefinedInitialDataOptions<MessageResponse, QueryError<Error[]>, Message>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetMessageQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetMessagesQueryOptions = (params: GetMessagesParams) => {
  const messageService = useMessageService();
  return queryOptions<MessagesResponse, QueryError<Error[]>>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/messages`,
      compact({
        first: params.first,
        after: params.after,
        last: params.last,
        before: params.before,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(messageService.getMessages(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * List all messages for a thread
 */
export function useMessages(
  params: GetMessagesParams,
  options?: Omit<
    UndefinedInitialDataOptions<MessagesResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetMessagesQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all messages for a thread
 */
export function useSuspenseMessages(
  params: GetMessagesParams,
  options?: Omit<
    UndefinedInitialDataOptions<MessagesResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetMessagesQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
function useInfiniteMessagesQueryOptions(params: GetMessagesParams) {
  const messageService = useMessageService();
  return {
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/messages`,
      { inifinite: true },
    ],
    queryFn: async ({ pageParam }: PageParam) => {
      const res = await guard(messageService.getMessages(applyPageParam(params, pageParam)));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data: InfiniteData<MessagesResponse, string | undefined>) =>
      data.pages.flatMap((page) => page.data ?? []),
    initialPageParam: getInitialPageParam(params),
    getNextPageParam,
    getPreviousPageParam,
  };
}

/**
 * List all messages for a thread
 */
export const useInfiniteMessages = (params: GetMessagesParams) => {
  const options = useInfiniteMessagesQueryOptions(params);
  return useInfiniteQuery(options);
};

/**
 * List all messages for a thread
 */
export const useSuspenseInfiniteMessages = (params: GetMessagesParams) => {
  const options = useInfiniteMessagesQueryOptions(params);
  return useSuspenseInfiniteQuery(options);
};

/**
 * Regenerate a message
 */
export function useRegenerateMessage(
  options?: Omit<
    UseMutationOptions<RegenerateMessageResponseData, QueryError<Error[]>, RegenerateMessageParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const messageService = useMessageService();
  return useMutation({
    mutationFn: async (params: RegenerateMessageParams) => {
      const res = await guard(messageService.regenerateMessage(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/messages/${params.messageId}/regenerate`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}
