/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import {
  type AppsyncAuthenticationService,
  type EntityService,
  type ExampleInputService,
  type ExampleOutputService,
  type ExampleService,
  type ExperimentAssignmentService,
  type ExperimentService,
  type FetchLike,
  type FileContextService,
  type FileSampleService,
  type FlolakeExecuteService,
  type FlolakeSourceService,
  HttpAppsyncAuthenticationService,
  HttpEntityService,
  HttpExampleInputService,
  HttpExampleOutputService,
  HttpExampleService,
  HttpExperimentAssignmentService,
  HttpExperimentService,
  HttpFileContextService,
  HttpFileSampleService,
  HttpFlolakeExecuteService,
  HttpFlolakeSourceService,
  HttpJemTemplateService,
  HttpLlmMetricsLogService,
  HttpMessageService,
  HttpNlToSqlService,
  HttpParsePdfService,
  HttpPromptService,
  HttpRunService,
  HttpSettingService,
  HttpStrategyService,
  HttpTaskDescriptionService,
  HttpTaskInputService,
  HttpTaskOutputService,
  HttpTaskService,
  HttpWorkflowInputService,
  HttpWorkflowOutputService,
  HttpWorkflowService,
  type JemTemplateService,
  type LlmMetricsLogService,
  type MessageService,
  type NlToSqlService,
  type ParsePdfService,
  type PromptService,
  type RunService,
  type SettingService,
  type StrategyService,
  type TaskDescriptionService,
  type TaskInputService,
  type TaskOutputService,
  type TaskService,
  type TransformOptions,
  type WorkflowInputService,
  type WorkflowOutputService,
  type WorkflowService,
} from "@floqastinc/transform-v3";
import { createContext, type FC, type PropsWithChildren, useContext, useMemo } from "react";

export interface TransformContextProps extends TransformOptions {
  fetch?: FetchLike;
}
const TransformContext = createContext<TransformContextProps | undefined>(undefined);

export const TransformProvider: FC<PropsWithChildren<TransformContextProps>> = ({
  children,
  ...props
}) => {
  const value = useMemo(
    () => ({ ...props }),
    [props.fetch, props.mapUnhandledException, props.mapValidationError, props.root],
  );
  return <TransformContext.Provider value={value}>{children}</TransformContext.Provider>;
};

export const useAppsyncAuthenticationService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useAppsyncAuthenticationService must be used within a TransformProvider");
  }
  const appsyncAuthenticationService: AppsyncAuthenticationService =
    new HttpAppsyncAuthenticationService(context.fetch ?? window.fetch.bind(window), context);
  return appsyncAuthenticationService;
};

export const useEntityService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useEntityService must be used within a TransformProvider");
  }
  const entityService: EntityService = new HttpEntityService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return entityService;
};

export const useExampleService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useExampleService must be used within a TransformProvider");
  }
  const exampleService: ExampleService = new HttpExampleService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return exampleService;
};

export const useExampleInputService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useExampleInputService must be used within a TransformProvider");
  }
  const exampleInputService: ExampleInputService = new HttpExampleInputService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return exampleInputService;
};

export const useExampleOutputService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useExampleOutputService must be used within a TransformProvider");
  }
  const exampleOutputService: ExampleOutputService = new HttpExampleOutputService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return exampleOutputService;
};

export const useExperimentService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useExperimentService must be used within a TransformProvider");
  }
  const experimentService: ExperimentService = new HttpExperimentService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return experimentService;
};

export const useExperimentAssignmentService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useExperimentAssignmentService must be used within a TransformProvider");
  }
  const experimentAssignmentService: ExperimentAssignmentService =
    new HttpExperimentAssignmentService(context.fetch ?? window.fetch.bind(window), context);
  return experimentAssignmentService;
};

export const useFileContextService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useFileContextService must be used within a TransformProvider");
  }
  const fileContextService: FileContextService = new HttpFileContextService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return fileContextService;
};

export const useFileSampleService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useFileSampleService must be used within a TransformProvider");
  }
  const fileSampleService: FileSampleService = new HttpFileSampleService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return fileSampleService;
};

export const useFlolakeSourceService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useFlolakeSourceService must be used within a TransformProvider");
  }
  const flolakeSourceService: FlolakeSourceService = new HttpFlolakeSourceService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return flolakeSourceService;
};

export const useFlolakeExecuteService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useFlolakeExecuteService must be used within a TransformProvider");
  }
  const flolakeExecuteService: FlolakeExecuteService = new HttpFlolakeExecuteService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return flolakeExecuteService;
};

export const useJemTemplateService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useJemTemplateService must be used within a TransformProvider");
  }
  const jemTemplateService: JemTemplateService = new HttpJemTemplateService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return jemTemplateService;
};

export const useLlmMetricsLogService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useLlmMetricsLogService must be used within a TransformProvider");
  }
  const llmMetricsLogService: LlmMetricsLogService = new HttpLlmMetricsLogService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return llmMetricsLogService;
};

export const useMessageService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useMessageService must be used within a TransformProvider");
  }
  const messageService: MessageService = new HttpMessageService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return messageService;
};

export const useNlToSqlService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useNlToSqlService must be used within a TransformProvider");
  }
  const nlToSqlService: NlToSqlService = new HttpNlToSqlService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return nlToSqlService;
};

export const useParsePdfService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useParsePdfService must be used within a TransformProvider");
  }
  const parsePdfService: ParsePdfService = new HttpParsePdfService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return parsePdfService;
};

export const usePromptService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("usePromptService must be used within a TransformProvider");
  }
  const promptService: PromptService = new HttpPromptService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return promptService;
};

export const useRunService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useRunService must be used within a TransformProvider");
  }
  const runService: RunService = new HttpRunService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return runService;
};

export const useSettingService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useSettingService must be used within a TransformProvider");
  }
  const settingService: SettingService = new HttpSettingService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return settingService;
};

export const useStrategyService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useStrategyService must be used within a TransformProvider");
  }
  const strategyService: StrategyService = new HttpStrategyService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return strategyService;
};

export const useTaskService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useTaskService must be used within a TransformProvider");
  }
  const taskService: TaskService = new HttpTaskService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return taskService;
};

export const useTaskDescriptionService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useTaskDescriptionService must be used within a TransformProvider");
  }
  const taskDescriptionService: TaskDescriptionService = new HttpTaskDescriptionService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return taskDescriptionService;
};

export const useTaskInputService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useTaskInputService must be used within a TransformProvider");
  }
  const taskInputService: TaskInputService = new HttpTaskInputService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return taskInputService;
};

export const useTaskOutputService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useTaskOutputService must be used within a TransformProvider");
  }
  const taskOutputService: TaskOutputService = new HttpTaskOutputService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return taskOutputService;
};

export const useWorkflowService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useWorkflowService must be used within a TransformProvider");
  }
  const workflowService: WorkflowService = new HttpWorkflowService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return workflowService;
};

export const useWorkflowInputService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useWorkflowInputService must be used within a TransformProvider");
  }
  const workflowInputService: WorkflowInputService = new HttpWorkflowInputService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return workflowInputService;
};

export const useWorkflowOutputService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useWorkflowOutputService must be used within a TransformProvider");
  }
  const workflowOutputService: WorkflowOutputService = new HttpWorkflowOutputService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return workflowOutputService;
};
