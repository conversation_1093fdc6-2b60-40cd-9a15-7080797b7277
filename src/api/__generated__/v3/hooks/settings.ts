/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type { Error, SettingsResponse, WorkflowSetting } from "@floqastinc/transform-v3";
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useQuery,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useSettingService } from "./context";
import { guard, type QueryError } from "./runtime";

const useGetSettingsQueryOptions = () => {
  const settingService = useSettingService();
  return queryOptions<SettingsResponse, QueryError<Error[]>, WorkflowSetting[]>({
    queryKey: [`/settings`],
    queryFn: async () => {
      const res = await guard(settingService.getSettings());
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all settings
 */
export function useSettings(
  options?: Omit<
    UndefinedInitialDataOptions<SettingsResponse, QueryError<Error[]>, WorkflowSetting[]>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetSettingsQueryOptions();
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all settings
 */
export function useSuspenseSettings(
  options?: Omit<
    UndefinedInitialDataOptions<SettingsResponse, QueryError<Error[]>, WorkflowSetting[]>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetSettingsQueryOptions();
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
