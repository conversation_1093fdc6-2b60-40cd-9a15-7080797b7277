/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  CreateWorkflowInputParams,
  DeleteWorkflowInputParams,
  Error,
  GetWorkflowInputExampleUriParams,
  GetWorkflowInputParams,
  GetWorkflowInputsParams,
  PresignedUrl,
  PresignedUrlResponse,
  SetWorkflowInputDatetimeParams,
  SetWorkflowInputFileParams,
  SetWorkflowInputNumberParams,
  SetWorkflowInputTextParams,
  UpdateWorkflowInputParams,
  WorkflowInput,
  WorkflowInputResponse,
  WorkflowInputsResponse,
} from "@floqastinc/transform-v3";
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useWorkflowInputService } from "./context";
import { assert, guard, type QueryError } from "./runtime";

/**
 * Create a new input
 */
export function useCreateWorkflowInput(
  options?: Omit<
    UseMutationOptions<WorkflowInput, QueryError<Error[]>, CreateWorkflowInputParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const workflowInputService = useWorkflowInputService();
  return useMutation({
    mutationFn: async (params: CreateWorkflowInputParams) => {
      const res = await guard(workflowInputService.createWorkflowInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/inputs`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Delete an input
 */
export function useDeleteWorkflowInput(
  options?: Omit<
    UseMutationOptions<WorkflowInput, QueryError<Error[]>, DeleteWorkflowInputParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const workflowInputService = useWorkflowInputService();
  return useMutation({
    mutationFn: async (params: DeleteWorkflowInputParams) => {
      const res = await guard(workflowInputService.deleteWorkflowInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/inputs/${params.workflowInputId}`],
      });
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/inputs`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Set a datetime value for a workflow input
 */
export function useSetWorkflowInputDatetime(
  options?: Omit<
    UseMutationOptions<WorkflowInput, QueryError<Error[]>, SetWorkflowInputDatetimeParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const workflowInputService = useWorkflowInputService();
  return useMutation({
    mutationFn: async (params: SetWorkflowInputDatetimeParams) => {
      const res = await guard(workflowInputService.setWorkflowInputDatetime(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/inputs/${params.workflowInputId}/value/datetime`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Set a file value for a workflow input
 */
export function useSetWorkflowInputFile(
  options?: Omit<
    UseMutationOptions<PresignedUrl, QueryError<Error[]>, SetWorkflowInputFileParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const workflowInputService = useWorkflowInputService();
  return useMutation({
    mutationFn: async (params: SetWorkflowInputFileParams) => {
      const res = await guard(workflowInputService.setWorkflowInputFile(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/inputs/${params.workflowInputId}/value/file`],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Set a number value for a workflow input
 */
export function useSetWorkflowInputNumber(
  options?: Omit<
    UseMutationOptions<WorkflowInput, QueryError<Error[]>, SetWorkflowInputNumberParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const workflowInputService = useWorkflowInputService();
  return useMutation({
    mutationFn: async (params: SetWorkflowInputNumberParams) => {
      const res = await guard(workflowInputService.setWorkflowInputNumber(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/inputs/${params.workflowInputId}/value/number`],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Set a text value for a workflow input
 */
export function useSetWorkflowInputText(
  options?: Omit<
    UseMutationOptions<WorkflowInput, QueryError<Error[]>, SetWorkflowInputTextParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const workflowInputService = useWorkflowInputService();
  return useMutation({
    mutationFn: async (params: SetWorkflowInputTextParams) => {
      const res = await guard(workflowInputService.setWorkflowInputText(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/inputs/${params.workflowInputId}/value/text`],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Update an input
 */
export function useUpdateWorkflowInput(
  options?: Omit<
    UseMutationOptions<WorkflowInput, QueryError<Error[]>, UpdateWorkflowInputParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const workflowInputService = useWorkflowInputService();
  return useMutation({
    mutationFn: async (params: UpdateWorkflowInputParams) => {
      const res = await guard(workflowInputService.updateWorkflowInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/inputs/${params.workflowInputId}`],
      });
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/inputs`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetWorkflowInputQueryOptions = (params: GetWorkflowInputParams) => {
  const workflowInputService = useWorkflowInputService();
  return queryOptions<WorkflowInputResponse, QueryError<Error[]>, WorkflowInput>({
    queryKey: [`/workflows/${params.workflowId}/inputs/${params.workflowInputId}`],
    queryFn: async () => {
      const res = await guard(workflowInputService.getWorkflowInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get an input
 */
export function useWorkflowInput(
  params: GetWorkflowInputParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowInputResponse, QueryError<Error[]>, WorkflowInput>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetWorkflowInputQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get an input
 */
export function useSuspenseWorkflowInput(
  params: GetWorkflowInputParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowInputResponse, QueryError<Error[]>, WorkflowInput>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetWorkflowInputQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetWorkflowInputExampleUriQueryOptions = (params: GetWorkflowInputExampleUriParams) => {
  const workflowInputService = useWorkflowInputService();
  return queryOptions<PresignedUrlResponse, QueryError<Error[]>, PresignedUrl>({
    queryKey: [`/workflows/${params.workflowId}/inputs/${params.workflowInputId}/value/file/uri`],
    queryFn: async () => {
      const res = await guard(workflowInputService.getWorkflowInputExampleUri(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Set a file value for a workflow input
 */
export function useWorkflowInputExampleUri(
  params: GetWorkflowInputExampleUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<PresignedUrlResponse, QueryError<Error[]>, PresignedUrl>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetWorkflowInputExampleUriQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Set a file value for a workflow input
 */
export function useSuspenseWorkflowInputExampleUri(
  params: GetWorkflowInputExampleUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<PresignedUrlResponse, QueryError<Error[]>, PresignedUrl>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetWorkflowInputExampleUriQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetWorkflowInputsQueryOptions = (params: GetWorkflowInputsParams) => {
  const workflowInputService = useWorkflowInputService();
  return queryOptions<WorkflowInputsResponse, QueryError<Error[]>, WorkflowInput[]>({
    queryKey: [`/workflows/${params.workflowId}/inputs`],
    queryFn: async () => {
      const res = await guard(workflowInputService.getWorkflowInputs(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all inputs for a workflow
 */
export function useWorkflowInputs(
  params: GetWorkflowInputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowInputsResponse, QueryError<Error[]>, WorkflowInput[]>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetWorkflowInputsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all inputs for a workflow
 */
export function useSuspenseWorkflowInputs(
  params: GetWorkflowInputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowInputsResponse, QueryError<Error[]>, WorkflowInput[]>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetWorkflowInputsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
