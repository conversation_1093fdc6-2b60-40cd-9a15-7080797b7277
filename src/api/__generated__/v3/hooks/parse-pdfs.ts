/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type { Error, ParsePdfParams, ParsePdfResponseData } from "@floqastinc/transform-v3";
import { useMutation, type UseMutationOptions, useQueryClient } from "@tanstack/react-query";
import { useParsePdfService } from "./context";
import { guard, type QueryError } from "./runtime";

/**
 * Parse a PDF file
 */
export function useParsePdf(
  options?: Omit<
    UseMutationOptions<ParsePdfResponseData, QueryError<Error[]>, ParsePdfParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const parsePdfService = useParsePdfService();
  return useMutation({
    mutationFn: async (params: ParsePdfParams) => {
      const res = await guard(parsePdfService.parsePdf(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/parse-pdf`,
        ],
      });
      return res.data;
    },
    ...options,
  });
}
