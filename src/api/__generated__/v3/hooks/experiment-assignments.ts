/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  AssignWorkflowToExperimentParams,
  DeleteWorkflowExperimentAssignmentParams,
  Error,
  GetWorkflowExperimentAssignmentsParams,
  GetWorkflowExperimentAssignmentsResponse,
  UpdateWorkflowExperimentAssignmentParams,
  WorkflowExperimentAssignment,
} from "@floqastinc/transform-v3";
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useExperimentAssignmentService } from "./context";
import { assert, guard, type QueryError } from "./runtime";

/**
 * Assign a workflow to a specific experiment variant.
 */
export function useAssignWorkflowToExperiment(
  options?: Omit<
    UseMutationOptions<
      WorkflowExperimentAssignment,
      QueryError<Error[]>,
      AssignWorkflowToExperimentParams
    >,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const experimentAssignmentService = useExperimentAssignmentService();
  return useMutation({
    mutationFn: async (params: AssignWorkflowToExperimentParams) => {
      const res = await guard(experimentAssignmentService.assignWorkflowToExperiment(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/assignments`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Delete a workflow's assignment to a specific experiment.
 */
export function useDeleteWorkflowExperimentAssignment(
  options?: Omit<
    UseMutationOptions<
      WorkflowExperimentAssignment,
      QueryError<Error[]>,
      DeleteWorkflowExperimentAssignmentParams
    >,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const experimentAssignmentService = useExperimentAssignmentService();
  return useMutation({
    mutationFn: async (params: DeleteWorkflowExperimentAssignmentParams) => {
      const res = await guard(
        experimentAssignmentService.deleteWorkflowExperimentAssignment(params),
      );
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/assignments/${params.assignmentId}`],
      });
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/assignments`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Update the assigned variant for a given workflow's experiment.
 */
export function useUpdateWorkflowExperimentAssignment(
  options?: Omit<
    UseMutationOptions<
      WorkflowExperimentAssignment,
      QueryError<Error[]>,
      UpdateWorkflowExperimentAssignmentParams
    >,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const experimentAssignmentService = useExperimentAssignmentService();
  return useMutation({
    mutationFn: async (params: UpdateWorkflowExperimentAssignmentParams) => {
      const res = await guard(
        experimentAssignmentService.updateWorkflowExperimentAssignment(params),
      );
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/assignments/${params.assignmentId}`],
      });
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/assignments`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetWorkflowExperimentAssignmentsQueryOptions = (
  params: GetWorkflowExperimentAssignmentsParams,
) => {
  const experimentAssignmentService = useExperimentAssignmentService();
  return queryOptions<GetWorkflowExperimentAssignmentsResponse, QueryError<Error[]>>({
    queryKey: [`/workflows/${params.workflowId}/assignments`],
    queryFn: async () => {
      const res = await guard(experimentAssignmentService.getWorkflowExperimentAssignments(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * Get all experiment assignments for a specific workflow.
 */
export function useWorkflowExperimentAssignments(
  params: GetWorkflowExperimentAssignmentsParams,
  options?: Omit<
    UndefinedInitialDataOptions<GetWorkflowExperimentAssignmentsResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetWorkflowExperimentAssignmentsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get all experiment assignments for a specific workflow.
 */
export function useSuspenseWorkflowExperimentAssignments(
  params: GetWorkflowExperimentAssignmentsParams,
  options?: Omit<
    UndefinedInitialDataOptions<GetWorkflowExperimentAssignmentsResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetWorkflowExperimentAssignmentsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
