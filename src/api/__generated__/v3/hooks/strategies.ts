/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  Error,
  FlolakeStoredQueriesResponse,
  GetStoredFlolakeQueriesParams,
  GetWorkflowTaskStrategiesParams,
  WorkflowTaskStrategiesResponse,
} from "@floqastinc/transform-v3";
import {
  type InfiniteData,
  queryOptions,
  type UndefinedInitialDataOptions,
  useInfiniteQuery,
  useQuery,
  useSuspenseInfiniteQuery,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useStrategyService } from "./context";
import {
  applyPageParam,
  compact,
  getInitialPageParam,
  getNextPageParam,
  getPreviousPageParam,
  guard,
  type PageParam,
  type QueryError,
} from "./runtime";

const useGetStoredFlolakeQueriesQueryOptions = (params?: GetStoredFlolakeQueriesParams) => {
  const strategyService = useStrategyService();
  return queryOptions<FlolakeStoredQueriesResponse, QueryError<Error[]>>({
    queryKey: [
      `/workflows/tasks/strategies/FLOLAKE/queries`,
      compact({
        first: params?.first,
        after: params?.after,
        last: params?.last,
        before: params?.before,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(strategyService.getStoredFlolakeQueries(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * List all FloLake stored queries
 */
export function useStoredFlolakeQueries(
  params?: GetStoredFlolakeQueriesParams,
  options?: Omit<
    UndefinedInitialDataOptions<FlolakeStoredQueriesResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetStoredFlolakeQueriesQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all FloLake stored queries
 */
export function useSuspenseStoredFlolakeQueries(
  params?: GetStoredFlolakeQueriesParams,
  options?: Omit<
    UndefinedInitialDataOptions<FlolakeStoredQueriesResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetStoredFlolakeQueriesQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
function useInfiniteStoredFlolakeQueriesQueryOptions(params?: GetStoredFlolakeQueriesParams) {
  const strategyService = useStrategyService();
  return {
    queryKey: [`/workflows/tasks/strategies/FLOLAKE/queries`, { inifinite: true }],
    queryFn: async ({ pageParam }: PageParam) => {
      const res = await guard(
        strategyService.getStoredFlolakeQueries(applyPageParam(params ?? {}, pageParam)),
      );
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data: InfiniteData<FlolakeStoredQueriesResponse, string | undefined>) =>
      data.pages.flatMap((page) => page.data ?? []),
    initialPageParam: getInitialPageParam(params ?? {}),
    getNextPageParam,
    getPreviousPageParam,
  };
}

/**
 * List all FloLake stored queries
 */
export const useInfiniteStoredFlolakeQueries = (params?: GetStoredFlolakeQueriesParams) => {
  const options = useInfiniteStoredFlolakeQueriesQueryOptions(params);
  return useInfiniteQuery(options);
};

/**
 * List all FloLake stored queries
 */
export const useSuspenseInfiniteStoredFlolakeQueries = (params?: GetStoredFlolakeQueriesParams) => {
  const options = useInfiniteStoredFlolakeQueriesQueryOptions(params);
  return useSuspenseInfiniteQuery(options);
};

const useGetWorkflowTaskStrategiesQueryOptions = (params?: GetWorkflowTaskStrategiesParams) => {
  const strategyService = useStrategyService();
  return queryOptions<WorkflowTaskStrategiesResponse, QueryError<Error[]>>({
    queryKey: [
      `/workflows/tasks/strategies`,
      compact({
        first: params?.first,
        after: params?.after,
        last: params?.last,
        before: params?.before,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(strategyService.getWorkflowTaskStrategies(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * List all task strategies
 */
export function useWorkflowTaskStrategies(
  params?: GetWorkflowTaskStrategiesParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowTaskStrategiesResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetWorkflowTaskStrategiesQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all task strategies
 */
export function useSuspenseWorkflowTaskStrategies(
  params?: GetWorkflowTaskStrategiesParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowTaskStrategiesResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetWorkflowTaskStrategiesQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
function useInfiniteWorkflowTaskStrategiesQueryOptions(params?: GetWorkflowTaskStrategiesParams) {
  const strategyService = useStrategyService();
  return {
    queryKey: [`/workflows/tasks/strategies`, { inifinite: true }],
    queryFn: async ({ pageParam }: PageParam) => {
      const res = await guard(
        strategyService.getWorkflowTaskStrategies(applyPageParam(params ?? {}, pageParam)),
      );
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data: InfiniteData<WorkflowTaskStrategiesResponse, string | undefined>) =>
      data.pages.flatMap((page) => page.data ?? []),
    initialPageParam: getInitialPageParam(params ?? {}),
    getNextPageParam,
    getPreviousPageParam,
  };
}

/**
 * List all task strategies
 */
export const useInfiniteWorkflowTaskStrategies = (params?: GetWorkflowTaskStrategiesParams) => {
  const options = useInfiniteWorkflowTaskStrategiesQueryOptions(params);
  return useInfiniteQuery(options);
};

/**
 * List all task strategies
 */
export const useSuspenseInfiniteWorkflowTaskStrategies = (
  params?: GetWorkflowTaskStrategiesParams,
) => {
  const options = useInfiniteWorkflowTaskStrategiesQueryOptions(params);
  return useSuspenseInfiniteQuery(options);
};
