/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  AddNlSqlFeedbackParams,
  EmptyResponseData,
  EndNlSqlConversationParams,
  Error,
  GetNlSqlConversationParams,
  GetNlSqlConversationsParams,
  NlSqlConversation,
  NlSqlConversationResponseResponse,
  NlSqlConversationsResponse,
  NlSqlParams,
  NlSqlResponseResponseData,
  StartNlSqlConversationParams,
} from "@floqastinc/transform-v3";
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useNlToSqlService } from "./context";
import { assert, compact, guard, type QueryError } from "./runtime";

/**
 * Add feedback to refine SQL in a conversation
 */
export function useAddNlSqlFeedback(
  options?: Omit<
    UseMutationOptions<NlSqlConversation, QueryError<Error[]>, AddNlSqlFeedbackParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const nlToSqlService = useNlToSqlService();
  return useMutation({
    mutationFn: async (params: AddNlSqlFeedbackParams) => {
      const res = await guard(nlToSqlService.addNlSqlFeedback(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/nlsql-conversations/${params.conversationId}/feedback`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * End an NL to SQL conversation
 */
export function useEndNlSqlConversation(
  options?: Omit<
    UseMutationOptions<EmptyResponseData, QueryError<Error[]>, EndNlSqlConversationParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const nlToSqlService = useNlToSqlService();
  return useMutation({
    mutationFn: async (params: EndNlSqlConversationParams) => {
      const res = await guard(nlToSqlService.endNlSqlConversation(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/nlsql-conversations/${params.conversationId}`,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/nlsql-conversations`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Generate SQL from natural language query
 */
export function useNlSql(
  options?: Omit<
    UseMutationOptions<NlSqlResponseResponseData, QueryError<Error[]>, NlSqlParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const nlToSqlService = useNlToSqlService();
  return useMutation({
    mutationFn: async (params: NlSqlParams) => {
      const res = await guard(nlToSqlService.nlSql(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/parse-nl-to-sql`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetNlSqlConversationQueryOptions = (params: GetNlSqlConversationParams) => {
  const nlToSqlService = useNlToSqlService();
  return queryOptions<NlSqlConversationResponseResponse, QueryError<Error[]>, NlSqlConversation>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/nlsql-conversations/${params.conversationId}`,
      compact({
        first: params.first,
        after: params.after,
        last: params.last,
        before: params.before,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(nlToSqlService.getNlSqlConversation(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get an existing NL to SQL conversation
 */
export function useNlSqlConversation(
  params: GetNlSqlConversationParams,
  options?: Omit<
    UndefinedInitialDataOptions<
      NlSqlConversationResponseResponse,
      QueryError<Error[]>,
      NlSqlConversation
    >,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetNlSqlConversationQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get an existing NL to SQL conversation
 */
export function useSuspenseNlSqlConversation(
  params: GetNlSqlConversationParams,
  options?: Omit<
    UndefinedInitialDataOptions<
      NlSqlConversationResponseResponse,
      QueryError<Error[]>,
      NlSqlConversation
    >,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetNlSqlConversationQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetNlSqlConversationsQueryOptions = (params: GetNlSqlConversationsParams) => {
  const nlToSqlService = useNlToSqlService();
  return queryOptions<NlSqlConversationsResponse, QueryError<Error[]>>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/nlsql-conversations`,
    ],
    queryFn: async () => {
      const res = await guard(nlToSqlService.getNlSqlConversations(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * Get all NL to SQL conversations
 */
export function useNlSqlConversations(
  params: GetNlSqlConversationsParams,
  options?: Omit<
    UndefinedInitialDataOptions<NlSqlConversationsResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetNlSqlConversationsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get all NL to SQL conversations
 */
export function useSuspenseNlSqlConversations(
  params: GetNlSqlConversationsParams,
  options?: Omit<
    UndefinedInitialDataOptions<NlSqlConversationsResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetNlSqlConversationsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

/**
 * Start a new NL to SQL conversation
 */
export function useStartNlSqlConversation(
  options?: Omit<
    UseMutationOptions<NlSqlConversation, QueryError<Error[]>, StartNlSqlConversationParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const nlToSqlService = useNlToSqlService();
  return useMutation({
    mutationFn: async (params: StartNlSqlConversationParams) => {
      const res = await guard(nlToSqlService.startNlSqlConversation(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/nlsql-conversations`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}
