/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  CreateExampleInputParams,
  DeleteExampleInputParams,
  Error,
  ExampleInput,
  ExampleInputResponse,
  ExampleInputsResponse,
  GetExampleInputParams,
  GetExampleInputsParams,
  GetTaskInputExampleUriParams,
  PresignedUrl,
  PresignedUrlResponse,
  SetExampleInputDatetimeParams,
  SetExampleInputFileValueParams,
  SetExampleInputNumberParams,
  SetExampleInputSourceParams,
  SetExampleInputTextParams,
} from "@floqastinc/transform-v3";
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useExampleInputService } from "./context";
import { assert, guard, type QueryError } from "./runtime";

/**
 * Create a new input
 */
export function useCreateExampleInput(
  options?: Omit<
    UseMutationOptions<ExampleInput, QueryError<Error[]>, CreateExampleInputParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const exampleInputService = useExampleInputService();
  return useMutation({
    mutationFn: async (params: CreateExampleInputParams) => {
      const res = await guard(exampleInputService.createExampleInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Delete an input
 */
export function useDeleteExampleInput(
  options?: Omit<
    UseMutationOptions<ExampleInput, QueryError<Error[]>, DeleteExampleInputParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const exampleInputService = useExampleInputService();
  return useMutation({
    mutationFn: async (params: DeleteExampleInputParams) => {
      const res = await guard(exampleInputService.deleteExampleInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs/${params.exampleInputId}`,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetExampleInputQueryOptions = (params: GetExampleInputParams) => {
  const exampleInputService = useExampleInputService();
  return queryOptions<ExampleInputResponse, QueryError<Error[]>, ExampleInput>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs/${params.exampleInputId}`,
    ],
    queryFn: async () => {
      const res = await guard(exampleInputService.getExampleInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get an input
 */
export function useExampleInput(
  params: GetExampleInputParams,
  options?: Omit<
    UndefinedInitialDataOptions<ExampleInputResponse, QueryError<Error[]>, ExampleInput>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetExampleInputQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get an input
 */
export function useSuspenseExampleInput(
  params: GetExampleInputParams,
  options?: Omit<
    UndefinedInitialDataOptions<ExampleInputResponse, QueryError<Error[]>, ExampleInput>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetExampleInputQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetExampleInputsQueryOptions = (params: GetExampleInputsParams) => {
  const exampleInputService = useExampleInputService();
  return queryOptions<ExampleInputsResponse, QueryError<Error[]>, ExampleInput[]>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs`,
    ],
    queryFn: async () => {
      const res = await guard(exampleInputService.getExampleInputs(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all inputs for an example set
 */
export function useExampleInputs(
  params: GetExampleInputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<ExampleInputsResponse, QueryError<Error[]>, ExampleInput[]>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetExampleInputsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all inputs for an example set
 */
export function useSuspenseExampleInputs(
  params: GetExampleInputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<ExampleInputsResponse, QueryError<Error[]>, ExampleInput[]>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetExampleInputsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

/**
 * Set an input datetime value
 */
export function useSetExampleInputDatetime(
  options?: Omit<
    UseMutationOptions<ExampleInput, QueryError<Error[]>, SetExampleInputDatetimeParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const exampleInputService = useExampleInputService();
  return useMutation({
    mutationFn: async (params: SetExampleInputDatetimeParams) => {
      const res = await guard(exampleInputService.setExampleInputDatetime(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs/${params.exampleInputId}/value/datetime`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Set an input file value
 */
export function useSetExampleInputFileValue(
  options?: Omit<
    UseMutationOptions<PresignedUrl, QueryError<Error[]>, SetExampleInputFileValueParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const exampleInputService = useExampleInputService();
  return useMutation({
    mutationFn: async (params: SetExampleInputFileValueParams) => {
      const res = await guard(exampleInputService.setExampleInputFileValue(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs/${params.exampleInputId}/value/file`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Set an input number value
 */
export function useSetExampleInputNumber(
  options?: Omit<
    UseMutationOptions<ExampleInput, QueryError<Error[]>, SetExampleInputNumberParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const exampleInputService = useExampleInputService();
  return useMutation({
    mutationFn: async (params: SetExampleInputNumberParams) => {
      const res = await guard(exampleInputService.setExampleInputNumber(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs/${params.exampleInputId}/value/number`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Copies the value from the specified source to this input. (Note that if the source value is changed, this input will not be updated.)
 */
export function useSetExampleInputSource(
  options?: Omit<
    UseMutationOptions<ExampleInput, QueryError<Error[]>, SetExampleInputSourceParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const exampleInputService = useExampleInputService();
  return useMutation({
    mutationFn: async (params: SetExampleInputSourceParams) => {
      const res = await guard(exampleInputService.setExampleInputSource(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs/${params.exampleInputId}/source`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Set an input text value
 */
export function useSetExampleInputText(
  options?: Omit<
    UseMutationOptions<ExampleInput, QueryError<Error[]>, SetExampleInputTextParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const exampleInputService = useExampleInputService();
  return useMutation({
    mutationFn: async (params: SetExampleInputTextParams) => {
      const res = await guard(exampleInputService.setExampleInputText(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs/${params.exampleInputId}/value/text`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetTaskInputExampleUriQueryOptions = (params: GetTaskInputExampleUriParams) => {
  const exampleInputService = useExampleInputService();
  return queryOptions<PresignedUrlResponse, QueryError<Error[]>, PresignedUrl>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs/${params.exampleInputId}/value/file/uri`,
    ],
    queryFn: async () => {
      const res = await guard(exampleInputService.getTaskInputExampleUri(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get the URI for an example input file
 */
export function useTaskInputExampleUri(
  params: GetTaskInputExampleUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<PresignedUrlResponse, QueryError<Error[]>, PresignedUrl>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetTaskInputExampleUriQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get the URI for an example input file
 */
export function useSuspenseTaskInputExampleUri(
  params: GetTaskInputExampleUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<PresignedUrlResponse, QueryError<Error[]>, PresignedUrl>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetTaskInputExampleUriQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
