/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  Error,
  FlolakeResponseResponse,
  GetFloLakeSourcesParams,
} from "@floqastinc/transform-v3";
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useQuery,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useFlolakeSourceService } from "./context";
import { compact, guard, type QueryError } from "./runtime";

const useGetFloLakeSourcesQueryOptions = (params?: GetFloLakeSourcesParams) => {
  const flolakeSourceService = useFlolakeSourceService();
  return queryOptions<FlolakeResponseResponse, QueryError<Error[]>>({
    queryKey: [
      `/flolake-sources`,
      compact({ filterByFivetranMap: params?.filterByFivetranMap }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(flolakeSourceService.getFloLakeSources(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * List all FloLake sources
 */
export function useFloLakeSources(
  params?: GetFloLakeSourcesParams,
  options?: Omit<
    UndefinedInitialDataOptions<FlolakeResponseResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetFloLakeSourcesQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all FloLake sources
 */
export function useSuspenseFloLakeSources(
  params?: GetFloLakeSourcesParams,
  options?: Omit<
    UndefinedInitialDataOptions<FlolakeResponseResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetFloLakeSourcesQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
