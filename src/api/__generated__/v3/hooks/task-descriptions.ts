/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  CreateTaskDescriptionParams,
  DeleteTaskDescriptionParams,
  Error,
  GetTaskActiveDescriptionParams,
  GetTaskDescriptionByCompositeKeyParams,
  GetTaskDescriptionParams,
  GetTaskDescriptionsParams,
  PutTaskDescriptionParams,
  TaskDescription,
  TaskDescriptionResponse,
  TaskDescriptionsResponse,
  UpdateTaskDescriptionParams,
} from "@floqastinc/transform-v3";
import {
  type InfiniteData,
  queryOptions,
  type UndefinedInitialDataOptions,
  useInfiniteQuery,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseInfiniteQuery,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useTaskDescriptionService } from "./context";
import {
  applyPageParam,
  assert,
  compact,
  getInitialPageParam,
  getNextPageParam,
  getPreviousPageParam,
  guard,
  type PageParam,
  type QueryError,
} from "./runtime";

/**
 * Create a new description
 */
export function useCreateTaskDescription(
  options?: Omit<
    UseMutationOptions<TaskDescription, QueryError<Error[]>, CreateTaskDescriptionParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const taskDescriptionService = useTaskDescriptionService();
  return useMutation({
    mutationFn: async (params: CreateTaskDescriptionParams) => {
      const res = await guard(taskDescriptionService.createTaskDescription(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/description`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Delete a description
 */
export function useDeleteTaskDescription(
  options?: Omit<
    UseMutationOptions<TaskDescription, QueryError<Error[]>, DeleteTaskDescriptionParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const taskDescriptionService = useTaskDescriptionService();
  return useMutation({
    mutationFn: async (params: DeleteTaskDescriptionParams) => {
      const res = await guard(taskDescriptionService.deleteTaskDescription(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/descriptions/${params.taskDescriptionId}`,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/descriptions`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Create or update a description
 */
export function usePutTaskDescription(
  options?: Omit<
    UseMutationOptions<TaskDescription, QueryError<Error[]>, PutTaskDescriptionParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const taskDescriptionService = useTaskDescriptionService();
  return useMutation({
    mutationFn: async (params: PutTaskDescriptionParams) => {
      const res = await guard(taskDescriptionService.putTaskDescription(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/description`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetTaskActiveDescriptionQueryOptions = (params: GetTaskActiveDescriptionParams) => {
  const taskDescriptionService = useTaskDescriptionService();
  return queryOptions<TaskDescriptionResponse, QueryError<Error[]>, TaskDescription>({
    queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/description`],
    queryFn: async () => {
      const res = await guard(taskDescriptionService.getTaskActiveDescription(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get the description for a task from the active example set.
 */
export function useTaskActiveDescription(
  params: GetTaskActiveDescriptionParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskDescriptionResponse, QueryError<Error[]>, TaskDescription>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetTaskActiveDescriptionQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get the description for a task from the active example set.
 */
export function useSuspenseTaskActiveDescription(
  params: GetTaskActiveDescriptionParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskDescriptionResponse, QueryError<Error[]>, TaskDescription>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetTaskActiveDescriptionQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskDescriptionQueryOptions = (params: GetTaskDescriptionParams) => {
  const taskDescriptionService = useTaskDescriptionService();
  return queryOptions<TaskDescriptionResponse, QueryError<Error[]>, TaskDescription>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/descriptions/${params.taskDescriptionId}`,
    ],
    queryFn: async () => {
      const res = await guard(taskDescriptionService.getTaskDescription(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get description for a task example with id
 */
export function useTaskDescription(
  params: GetTaskDescriptionParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskDescriptionResponse, QueryError<Error[]>, TaskDescription>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetTaskDescriptionQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get description for a task example with id
 */
export function useSuspenseTaskDescription(
  params: GetTaskDescriptionParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskDescriptionResponse, QueryError<Error[]>, TaskDescription>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetTaskDescriptionQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskDescriptionByCompositeKeyQueryOptions = (
  params: GetTaskDescriptionByCompositeKeyParams,
) => {
  const taskDescriptionService = useTaskDescriptionService();
  return queryOptions<TaskDescriptionResponse, QueryError<Error[]>, TaskDescription>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/description`,
      compact({
        first: params.first,
        after: params.after,
        last: params.last,
        before: params.before,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(taskDescriptionService.getTaskDescriptionByCompositeKey(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get description for a task example with composite key
 */
export function useTaskDescriptionByCompositeKey(
  params: GetTaskDescriptionByCompositeKeyParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskDescriptionResponse, QueryError<Error[]>, TaskDescription>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetTaskDescriptionByCompositeKeyQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get description for a task example with composite key
 */
export function useSuspenseTaskDescriptionByCompositeKey(
  params: GetTaskDescriptionByCompositeKeyParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskDescriptionResponse, QueryError<Error[]>, TaskDescription>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetTaskDescriptionByCompositeKeyQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskDescriptionsQueryOptions = (params: GetTaskDescriptionsParams) => {
  const taskDescriptionService = useTaskDescriptionService();
  return queryOptions<TaskDescriptionsResponse, QueryError<Error[]>>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/descriptions`,
      compact({
        first: params.first,
        after: params.after,
        last: params.last,
        before: params.before,
        exampleSetId: params.exampleSetId,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(taskDescriptionService.getTaskDescriptions(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * List all descriptions for a task
 */
export function useTaskDescriptions(
  params: GetTaskDescriptionsParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskDescriptionsResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetTaskDescriptionsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all descriptions for a task
 */
export function useSuspenseTaskDescriptions(
  params: GetTaskDescriptionsParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskDescriptionsResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetTaskDescriptionsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
function useInfiniteTaskDescriptionsQueryOptions(params: GetTaskDescriptionsParams) {
  const taskDescriptionService = useTaskDescriptionService();
  return {
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/descriptions`,
      compact({ exampleSetId: params.exampleSetId }),
      { inifinite: true },
    ].filter(Boolean),
    queryFn: async ({ pageParam }: PageParam) => {
      const res = await guard(
        taskDescriptionService.getTaskDescriptions(applyPageParam(params, pageParam)),
      );
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data: InfiniteData<TaskDescriptionsResponse, string | undefined>) =>
      data.pages.flatMap((page) => page.data),
    initialPageParam: getInitialPageParam(params),
    getNextPageParam,
    getPreviousPageParam,
  };
}

/**
 * List all descriptions for a task
 */
export const useInfiniteTaskDescriptions = (params: GetTaskDescriptionsParams) => {
  const options = useInfiniteTaskDescriptionsQueryOptions(params);
  return useInfiniteQuery(options);
};

/**
 * List all descriptions for a task
 */
export const useSuspenseInfiniteTaskDescriptions = (params: GetTaskDescriptionsParams) => {
  const options = useInfiniteTaskDescriptionsQueryOptions(params);
  return useSuspenseInfiniteQuery(options);
};

/**
 * Update a description
 */
export function useUpdateTaskDescription(
  options?: Omit<
    UseMutationOptions<TaskDescription, QueryError<Error[]>, UpdateTaskDescriptionParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const taskDescriptionService = useTaskDescriptionService();
  return useMutation({
    mutationFn: async (params: UpdateTaskDescriptionParams) => {
      const res = await guard(taskDescriptionService.updateTaskDescription(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/descriptions/${params.taskDescriptionId}`,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/descriptions`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}
