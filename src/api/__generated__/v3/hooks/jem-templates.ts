/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  Error,
  GetJemTemplateParams,
  JemTemplateContextResponse,
  JemTemplateKey,
} from "@floqastinc/transform-v3";
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useQuery,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useJemTemplateService } from "./context";
import { assert, guard, type QueryError } from "./runtime";

const useGetJemTemplateQueryOptions = (params: GetJemTemplateParams) => {
  const jemTemplateService = useJemTemplateService();
  return queryOptions<JemTemplateContextResponse, QueryError<Error[]>, JemTemplateKey>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/jem-template`,
    ],
    queryFn: async () => {
      const res = await guard(jemTemplateService.getJemTemplate(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Fetches the JEM template associated with an entity
 */
export function useJemTemplate(
  params: GetJemTemplateParams,
  options?: Omit<
    UndefinedInitialDataOptions<JemTemplateContextResponse, QueryError<Error[]>, JemTemplateKey>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetJemTemplateQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Fetches the JEM template associated with an entity
 */
export function useSuspenseJemTemplate(
  params: GetJemTemplateParams,
  options?: Omit<
    UndefinedInitialDataOptions<JemTemplateContextResponse, QueryError<Error[]>, JemTemplateKey>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetJemTemplateQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
