/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v0/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import {
  type ApiKeyService,
  type FetchLike,
  HttpApiKeyService,
  HttpPresignedUrlService,
  HttpPrincipalService,
  HttpTeamService,
  HttpUserService,
  type PresignedUrlService,
  type PrincipalService,
  type TeamService,
  type TransformOptions,
  type UserService,
} from "@floqastinc/transform-v0";
import { createContext, type FC, type PropsWithChildren, useContext, useMemo } from "react";

export interface TransformContextProps extends TransformOptions {
  fetch?: FetchLike;
}
const TransformContext = createContext<TransformContextProps | undefined>(undefined);

export const TransformProvider: FC<PropsWithChildren<TransformContextProps>> = ({
  children,
  ...props
}) => {
  const value = useMemo(
    () => ({ ...props }),
    [props.fetch, props.mapUnhandledException, props.mapValidationError, props.root],
  );
  return <TransformContext.Provider value={value}>{children}</TransformContext.Provider>;
};

export const useApiKeyService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useApiKeyService must be used within a TransformProvider");
  }
  const apiKeyService: ApiKeyService = new HttpApiKeyService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return apiKeyService;
};

export const usePresignedUrlService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("usePresignedUrlService must be used within a TransformProvider");
  }
  const presignedUrlService: PresignedUrlService = new HttpPresignedUrlService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return presignedUrlService;
};

export const usePrincipalService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("usePrincipalService must be used within a TransformProvider");
  }
  const principalService: PrincipalService = new HttpPrincipalService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return principalService;
};

export const useTeamService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useTeamService must be used within a TransformProvider");
  }
  const teamService: TeamService = new HttpTeamService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return teamService;
};

export const useUserService = () => {
  const context = useContext(TransformContext);
  if (!context) {
    throw new Error("useUserService must be used within a TransformProvider");
  }
  const userService: UserService = new HttpUserService(
    context.fetch ?? window.fetch.bind(window),
    context,
  );
  return userService;
};
