/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v0/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type { Error, PresignedUrlWithKey } from "@floqastinc/transform-v0";
import { useMutation, type UseMutationOptions, useQueryClient } from "@tanstack/react-query";
import { usePresignedUrlService } from "./context";
import { assert, guard, type QueryError } from "./runtime";

/**
 * Create a presigned url to upload a file to S3
 */
export function useCreatePresignedUrlUpload(
  options?: Omit<UseMutationOptions<PresignedUrlWithKey, QueryError<Error[]>>, "mutationFn">,
) {
  const queryClient = useQueryClient();
  const presignedUrlService = usePresignedUrlService();
  return useMutation({
    mutationFn: async () => {
      const res = await guard(presignedUrlService.createPresignedUrlUpload());
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/presigned-urls/upload`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}
