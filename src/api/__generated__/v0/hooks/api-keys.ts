/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v0/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  ApiKey,
  ApiKeyResponse,
  ApiKeysResponse,
  CreateApiKeyParams,
  DeleteApiKeyParams,
  Error,
  GetApiKeyParams,
  GetApiKeysByTeamParams,
} from "@floqastinc/transform-v0";
import {
  type InfiniteData,
  queryOptions,
  type UndefinedInitialDataOptions,
  useInfiniteQuery,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseInfiniteQuery,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useApiKeyService } from "./context";
import {
  applyPageParam,
  assert,
  compact,
  getInitialPageParam,
  getNextPageParam,
  getPreviousPageParam,
  guard,
  type PageParam,
  type QueryError,
} from "./runtime";

const useGetApiKeyQueryOptions = (params: GetApiKeyParams) => {
  const apiKeyService = useApiKeyService();
  return queryOptions<ApiKeyResponse, QueryError<Error[]>, ApiKey>({
    queryKey: [`/teams/${params.teamId}/api-keys/${params.apiKeyId}`],
    queryFn: async () => {
      const res = await guard(apiKeyService.getApiKey(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get an API key
 */
export function useApiKey(
  params: GetApiKeyParams,
  options?: Omit<
    UndefinedInitialDataOptions<ApiKeyResponse, QueryError<Error[]>, ApiKey>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetApiKeyQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get an API key
 */
export function useSuspenseApiKey(
  params: GetApiKeyParams,
  options?: Omit<
    UndefinedInitialDataOptions<ApiKeyResponse, QueryError<Error[]>, ApiKey>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetApiKeyQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetApiKeysByTeamQueryOptions = (params: GetApiKeysByTeamParams) => {
  const apiKeyService = useApiKeyService();
  return queryOptions<ApiKeysResponse, QueryError<Error[]>>({
    queryKey: [
      `/teams/${params.teamId}/api-keys`,
      compact({
        first: params.first,
        after: params.after,
        last: params.last,
        before: params.before,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(apiKeyService.getApiKeysByTeam(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * Get API keys for a team
 */
export function useApiKeysByTeam(
  params: GetApiKeysByTeamParams,
  options?: Omit<
    UndefinedInitialDataOptions<ApiKeysResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetApiKeysByTeamQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get API keys for a team
 */
export function useSuspenseApiKeysByTeam(
  params: GetApiKeysByTeamParams,
  options?: Omit<
    UndefinedInitialDataOptions<ApiKeysResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetApiKeysByTeamQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
function useInfiniteApiKeysByTeamQueryOptions(params: GetApiKeysByTeamParams) {
  const apiKeyService = useApiKeyService();
  return {
    queryKey: [`/teams/${params.teamId}/api-keys`, { inifinite: true }],
    queryFn: async ({ pageParam }: PageParam) => {
      const res = await guard(apiKeyService.getApiKeysByTeam(applyPageParam(params, pageParam)));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data: InfiniteData<ApiKeysResponse, string | undefined>) =>
      data.pages.flatMap((page) => page.data ?? []),
    initialPageParam: getInitialPageParam(params),
    getNextPageParam,
    getPreviousPageParam,
  };
}

/**
 * Get API keys for a team
 */
export const useInfiniteApiKeysByTeam = (params: GetApiKeysByTeamParams) => {
  const options = useInfiniteApiKeysByTeamQueryOptions(params);
  return useInfiniteQuery(options);
};

/**
 * Get API keys for a team
 */
export const useSuspenseInfiniteApiKeysByTeam = (params: GetApiKeysByTeamParams) => {
  const options = useInfiniteApiKeysByTeamQueryOptions(params);
  return useSuspenseInfiniteQuery(options);
};

/**
 * Create a new API key. This is the only response that will return the full API key!
 */
export function useCreateApiKey(
  options?: Omit<UseMutationOptions<ApiKey, QueryError<Error[]>, CreateApiKeyParams>, "mutationFn">,
) {
  const queryClient = useQueryClient();
  const apiKeyService = useApiKeyService();
  return useMutation({
    mutationFn: async (params: CreateApiKeyParams) => {
      const res = await guard(apiKeyService.createApiKey(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/teams/${params.teamId}/api-keys`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Delete an API key
 */
export function useDeleteApiKey(
  options?: Omit<UseMutationOptions<ApiKey, QueryError<Error[]>, DeleteApiKeyParams>, "mutationFn">,
) {
  const queryClient = useQueryClient();
  const apiKeyService = useApiKeyService();
  return useMutation({
    mutationFn: async (params: DeleteApiKeyParams) => {
      const res = await guard(apiKeyService.deleteApiKey(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/teams/${params.teamId}/api-keys/${params.apiKeyId}`],
      });
      queryClient.invalidateQueries({ queryKey: [`/teams/${params.teamId}/api-keys`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}
