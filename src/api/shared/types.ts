export interface AvailableData {
  key: string;
  value: string;
}

export interface Provider {
  name: {
    label: string;
    slug: string;
  };
  connectionType: string;
  availableData: AvailableData[];
  description: { key: string; value: string };
  products: { slug: string; label: string }[];
  isBeta: boolean;
  isFFEnabled: boolean;
}

export interface ProvidersData {
  categoryName: {
    key: string;
    value: string;
  };
  providers: Provider[];
}

export interface EntityAssociation {
  connectionInfo: {
    organizationId: string;
  };
  entityId: string;
  _id: string;
}

export interface ActiveConnection {
  connectionInfo: {
    type: string;
    username: string;
    connectionId: string;
    bankName: string;
    status: string;
  };
  createdAt: string;
  deleted: boolean;
  entityAssociation: EntityAssociation[];
  integrationSystem: string;
  name: string;
  tlcId: string;
  updatedAt: string;
  __v: number;
  _id: string;
}

export interface ColumnData {
  name: string;
  type: string;
}

export interface TableData {
  tableName: string;
  schemaName: string;
  databaseName: string;
  columns: ColumnData[];
}

export interface TableOption {
  name: string;
  databaseName: string;
  schemaName: string;
  columns: ColumnData[];
  formattedName: string;
}

export interface FlolakeConnectionData {
  id: string;
  connectionName: string;
  integrationSystem: string;
  schemaName: string;
  transformSystem: string;
  tableData: TableData[];
}

export interface FlolakeResponse {
  totalConnections: number;
  connections: FlolakeConnectionData[];
}
