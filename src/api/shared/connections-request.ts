import axios, { AxiosError, AxiosRequestConfig } from "axios";
import { getJwt } from "@/utils/jwt";

interface LambdaErrorResponse {
  message?: string;
  name?: string;
  status?: number;
  code?: number;
  [key: string]: unknown;
}

export const buildLambdaError = (error: unknown) => {
  const axiosError = error as AxiosError;
  const resBody = (axiosError?.response?.data as LambdaErrorResponse) || {};

  return {
    ...resBody,
    message: resBody.message || "There was an unexpected error",
    name: resBody.name || "Unexpected Error",
    status: resBody.status || 500,
    code: resBody.code || 9999,
  };
};

export interface LambdaEndpoint {
  system: string;
  path: string;
}

export const getLambdaEndpoint = (lambda: LambdaEndpoint): string => {
  const apiRoot = window.location.origin;
  const domain = apiRoot.split("//")[1];
  const { system, path } = lambda;

  let lambdaEndpoint: string;

  if (process.env.NODE_ENV === "development") {
    // 4707 is the default port for the Lola Hub
    lambdaEndpoint = `http://${system}.localhost:4707/${path}`;
  } else if (process.env.REACT_APP_GATEWAY_HOSTNAME) {
    lambdaEndpoint = `https://${process.env.REACT_APP_GATEWAY_HOSTNAME}/${system}/${path}`;
  } else {
    lambdaEndpoint = `https://${system}.${domain}/${path}`;
  }

  return lambdaEndpoint;
};

export const lambdaGet = async <T>(
  lambda: LambdaEndpoint,
  params?: Record<string, unknown>,
): Promise<T> => {
  return lambdaRequest<T>("GET", lambda, params);
};

export const lambdaPost = async <T>(lambda: LambdaEndpoint, data?: unknown): Promise<T> => {
  return lambdaRequest<T>("POST", lambda, undefined, data);
};

export const lambdaRequest = async <T>(
  method: string,
  lambda: LambdaEndpoint,
  params?: Record<string, unknown>,
  data?: unknown,
): Promise<T> => {
  const lambdaEndpoint = getLambdaEndpoint(lambda);
  const jwt = getJwt();

  const config: AxiosRequestConfig = {
    method,
    url: lambdaEndpoint,
    headers: {
      Authorization: `Bearer ${jwt}`,
      "Content-Type": "application/json",
    },
    params,
    data,
  };

  try {
    const response = await axios<T>(config);
    return response.data;
  } catch (error) {
    throw buildLambdaError(error);
  }
};
