import {
  useMutation,
  useQuery,
  useQueryClient,
  UseMutationOptions,
  UseQueryOptions,
} from "@tanstack/react-query";
import { fetchStuff, getLambdaEndpoint } from "@/utils/request";

const BASE_URL = `${getLambdaEndpoint("transform_api", true)}/v3`;

// Types
export interface StartConversationRequest {
  naturalLanguageQuery: string;
}

export interface FeedbackRequest {
  feedbackText: string;
}

export interface ConversationMessage {
  id: string;
  role: "USER" | "ASSISTANT";
  content: string;
  timestamp: string;
  metadata?: {
    sqlQuery?: string;
    sqlDescription?: string;
    feedbackType?: string;
    errorDetails?: string;
  };
}

export interface ConversationData {
  conversationId: string;
  messages: ConversationMessage[];
  currentSql: string;
  currentDescription: string;
  originalQuery: string;
  createdAt: string;
  updatedAt: string;
}

export interface PageInfo {
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  startCursor: string;
  endCursor: string;
}

export interface ConversationResponse {
  data: ConversationData | Record<string, never>;
  errors: { title: string; code: string; detail?: string }[];
}

export interface ConversationListItem {
  id: string;
  exampleSetId: string;
  workflowId: string;
  taskId: string;
  conversationType: string;
  status: string;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface ConversationListResponse {
  data: ConversationListItem[];
  errors: { title: string; code: string; detail?: string }[];
}

// Type guards for better type safety
export function isConversationData(data: unknown): data is ConversationData {
  return (
    typeof data === "object" &&
    data !== null &&
    "conversationId" in data &&
    "messages" in data &&
    "currentSql" in data &&
    "currentDescription" in data &&
    "originalQuery" in data &&
    "createdAt" in data &&
    "updatedAt" in data &&
    typeof (data as ConversationData).conversationId === "string" &&
    Array.isArray((data as ConversationData).messages) &&
    typeof (data as ConversationData).currentSql === "string" &&
    typeof (data as ConversationData).currentDescription === "string"
  );
}

export function isSuccessResponse(
  response: ConversationResponse,
): response is ConversationResponse & { data: ConversationData } {
  return response.errors.length === 0 && isConversationData(response.data);
}

export function isErrorResponse(
  response: ConversationResponse,
): response is ConversationResponse & { data: Record<string, never> } {
  return response.errors.length > 0;
}

// Response validation function
function validateApiResponse(response: unknown): ConversationResponse {
  if (
    typeof response !== "object" ||
    response === null ||
    !("data" in response) ||
    !("errors" in response)
  ) {
    throw new Error("Invalid API response structure");
  }

  const validatedResponse = response as ConversationResponse;

  // Validate errors array
  if (!Array.isArray(validatedResponse.errors)) {
    throw new Error("API response errors must be an array");
  }

  // Validate each error object
  for (const error of validatedResponse.errors) {
    if (
      typeof error !== "object" ||
      error === null ||
      typeof error.title !== "string" ||
      typeof error.code !== "string"
    ) {
      throw new Error("Invalid error object in API response");
    }
  }

  return validatedResponse;
}

// Response validation function for conversation list
function validateConversationListResponse(response: unknown): ConversationListResponse {
  if (
    typeof response !== "object" ||
    response === null ||
    !("data" in response) ||
    !("errors" in response)
  ) {
    throw new Error("Invalid conversation list API response structure");
  }

  const validatedResponse = response as ConversationListResponse;

  // Validate data array
  if (!Array.isArray(validatedResponse.data)) {
    throw new Error("API response data must be an array");
  }

  // Validate each conversation list item
  for (const conversation of validatedResponse.data) {
    if (
      typeof conversation !== "object" ||
      conversation === null ||
      typeof conversation.id !== "string" ||
      typeof conversation.exampleSetId !== "string" ||
      typeof conversation.workflowId !== "string" ||
      typeof conversation.taskId !== "string" ||
      typeof conversation.conversationType !== "string" ||
      typeof conversation.status !== "string" ||
      typeof conversation.createdAt !== "string" ||
      typeof conversation.updatedAt !== "string" ||
      typeof conversation.createdBy !== "string" ||
      typeof conversation.updatedBy !== "string"
    ) {
      throw new Error("Invalid conversation list item in API response");
    }
  }

  // Validate errors array
  if (!Array.isArray(validatedResponse.errors)) {
    throw new Error("API response errors must be an array");
  }

  // Validate each error object
  for (const error of validatedResponse.errors) {
    if (
      typeof error !== "object" ||
      error === null ||
      typeof error.title !== "string" ||
      typeof error.code !== "string"
    ) {
      throw new Error("Invalid error object in API response");
    }
  }

  return validatedResponse;
}

// Helper function to build the conversation path
function buildConversationPath(
  workflowId: string,
  taskId: string,
  exampleSetId: string,
  conversationId?: string,
) {
  const basePath = `/workflows/${workflowId}/tasks/${taskId}/examples/${exampleSetId}/nlsql-conversations`;
  return conversationId ? `${basePath}/${conversationId}` : basePath;
}

// Start a new conversation
export function useStartConversation(
  workflowId: string,
  taskId: string,
  exampleSetId: string,
  options?: UseMutationOptions<ConversationResponse, any, StartConversationRequest>,
) {
  return useMutation({
    mutationFn: async (body: StartConversationRequest) => {
      const path = buildConversationPath(workflowId, taskId, exampleSetId);
      const res = await fetchStuff(`${BASE_URL}${path}`, {
        method: "POST",
        body: JSON.stringify(body),
        headers: { "Content-Type": "application/json" },
      });
      const data = await res.json();
      return validateApiResponse(data);
    },
    ...options,
  });
}

// Get conversations from exampleSet
export function useGetConversations(
  workflowId: string,
  taskId: string,
  exampleSetId: string,
  options?: UseQueryOptions<ConversationListResponse, any, ConversationListResponse>,
) {
  return useQuery({
    queryKey: ["nlsql-conversations", workflowId, taskId, exampleSetId],
    queryFn: async () => {
      const path = buildConversationPath(workflowId, taskId, exampleSetId);
      const res = await fetchStuff(`${BASE_URL}${path}`, {});
      const data = await res.json();
      return validateConversationListResponse(data);
    },
    enabled: !!workflowId && !!taskId && !!exampleSetId,
    ...options,
  });
}

// Get conversation history
export function useConversation(
  workflowId: string,
  taskId: string,
  exampleSetId: string,
  conversationId: string | undefined,
  enabled = true,
  options?: UseQueryOptions<ConversationResponse, any, ConversationResponse>,
) {
  return useQuery({
    queryKey: ["nlsql-conversations", workflowId, taskId, exampleSetId, conversationId],
    queryFn: async () => {
      if (!conversationId) throw new Error("No conversationId");
      const path = buildConversationPath(workflowId, taskId, exampleSetId, conversationId);
      const res = await fetchStuff(`${BASE_URL}${path}`, {});
      const data = await res.json();
      return validateApiResponse(data);
    },
    enabled: !!conversationId && enabled,
    ...options,
  });
}

// Add feedback to an existing conversation
export function useAddFeedback(
  workflowId: string,
  taskId: string,
  exampleSetId: string,
  conversationId: string | undefined,
  options?: UseMutationOptions<ConversationResponse, any, FeedbackRequest>,
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (body: FeedbackRequest) => {
      if (!conversationId) throw new Error("No conversationId");
      const path = buildConversationPath(workflowId, taskId, exampleSetId, conversationId);
      const res = await fetchStuff(`${BASE_URL}${path}/feedback`, {
        method: "POST",
        body: JSON.stringify(body),
        headers: { "Content-Type": "application/json" },
      });
      const data = await res.json();
      return validateApiResponse(data);
    },
    onSuccess: (_, __, ___) => {
      if (conversationId) {
        queryClient.invalidateQueries({
          queryKey: ["nlsql-conversations", workflowId, taskId, exampleSetId, conversationId],
        });
      }
    },
    ...options,
  });
}

// End a conversation
export function useEndConversation(
  workflowId: string,
  taskId: string,
  exampleSetId: string,
  conversationId: string | undefined,
  options?: UseMutationOptions<ConversationResponse, any, void>,
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async () => {
      if (!conversationId) throw new Error("No conversationId");
      const path = buildConversationPath(workflowId, taskId, exampleSetId, conversationId);
      const res = await fetchStuff(`${BASE_URL}${path}`, {
        method: "DELETE",
      });
      const data = await res.json();
      return validateApiResponse(data);
    },
    onSuccess: (_, __, ___) => {
      if (conversationId) {
        queryClient.invalidateQueries({
          queryKey: ["nlsql-conversations", workflowId, taskId, exampleSetId, conversationId],
        });
      }
    },
    ...options,
  });
}
