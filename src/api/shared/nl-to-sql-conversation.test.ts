import { describe, expect, test, vi, beforeEach, afterEach } from "vitest";
import { renderHook } from "vitest-browser-react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { createElement } from "react";
import {
  useStartConversation,
  useConversation,
  useAddFeedback,
  useEndConversation,
  ConversationResponse,
  ConversationMessage,
  isConversationData,
  isSuccessResponse,
  isErrorResponse,
} from "./nl-to-sql-conversation";

// Mock fetchStuff and getLambdaEndpoint
vi.mock("@/utils/request", () => ({
  fetchStuff: vi.fn(),
  getLambdaEndpoint: vi.fn(() => "https://api.example.com"),
}));

// Mock conversation data
const mockConversationMessage: ConversationMessage = {
  id: "msg-1",
  role: "USER",
  content: "Show me all users",
  timestamp: "2023-10-01T10:00:00Z",
};

const mockConversationResponse: ConversationResponse = {
  data: {
    conversationId: "conv-123",
    messages: [mockConversationMessage],
    currentSql: "SELECT * FROM users",
    currentDescription: "Query to get all users",
    originalQuery: "Show me all users",
    createdAt: "2023-10-01T10:00:00Z",
    updatedAt: "2023-10-01T10:00:00Z",
  },
  errors: [],
};

const mockErrorResponse: ConversationResponse = {
  data: {},
  errors: [
    {
      title: "Validation Error",
      code: "INVALID_INPUT",
      detail: "Natural language query is required",
    },
  ],
};

// Test parameters
const testParams = {
  workflowId: "workflow-123",
  taskId: "task-456",
  exampleSetId: "example-789",
};

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return function Wrapper({ children }: { children: React.ReactNode }) {
    return createElement(QueryClientProvider, { client: queryClient }, children);
  };
};

describe("Type Guards and Validation", () => {
  describe("isConversationData", () => {
    test("should return true for valid conversation data", () => {
      const validData = {
        conversationId: "conv-123",
        messages: [mockConversationMessage],
        currentSql: "SELECT * FROM users",
        currentDescription: "Query to get all users",
        originalQuery: "Show me all users",
        createdAt: "2023-10-01T10:00:00Z",
        updatedAt: "2023-10-01T10:00:00Z",
      };

      expect(isConversationData(validData)).toBe(true);
    });

    test("should return false for invalid conversation data", () => {
      const invalidData = {
        conversationId: "conv-123",
        // Missing required fields
      };

      expect(isConversationData(invalidData)).toBe(false);
    });

    test("should return false for null or undefined", () => {
      expect(isConversationData(null)).toBe(false);
      expect(isConversationData(undefined)).toBe(false);
    });

    test("should return false for non-object types", () => {
      expect(isConversationData("string")).toBe(false);
      expect(isConversationData(123)).toBe(false);
      expect(isConversationData([])).toBe(false);
    });
  });

  describe("isSuccessResponse", () => {
    test("should return true for successful response", () => {
      expect(isSuccessResponse(mockConversationResponse)).toBe(true);
    });

    test("should return false for error response", () => {
      expect(isSuccessResponse(mockErrorResponse)).toBe(false);
    });
  });

  describe("isErrorResponse", () => {
    test("should return true for error response", () => {
      expect(isErrorResponse(mockErrorResponse)).toBe(true);
    });

    test("should return false for successful response", () => {
      expect(isErrorResponse(mockConversationResponse)).toBe(false);
    });
  });
});

describe("nl-to-sql-conversation API hooks", () => {
  let fetchStuff: ReturnType<typeof vi.fn>;

  beforeEach(async () => {
    vi.clearAllMocks();
    const { fetchStuff: mockFetchStuff } = await import("@/utils/request");
    fetchStuff = vi.mocked(mockFetchStuff);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe("useStartConversation", () => {
    test("should start a conversation successfully", async () => {
      // Arrange
      fetchStuff.mockResolvedValue({
        json: () => Promise.resolve(mockConversationResponse),
      } as Response);

      const { result } = renderHook(
        () =>
          useStartConversation(testParams.workflowId, testParams.taskId, testParams.exampleSetId),
        {
          wrapper: createWrapper(),
        },
      );

      // Act
      const startConversation = result.current.mutateAsync;
      const response = await startConversation({
        naturalLanguageQuery: "Show me all users",
      });

      // Assert
      expect(fetchStuff).toHaveBeenCalledWith(
        `https://api.example.com/v3/workflows/${testParams.workflowId}/tasks/${testParams.taskId}/examples/${testParams.exampleSetId}/nlsql-conversations`,
        {
          method: "POST",
          body: JSON.stringify({ naturalLanguageQuery: "Show me all users" }),
          headers: { "Content-Type": "application/json" },
        },
      );
      expect(response).toEqual(mockConversationResponse);
    });

    test("should handle invalid API response", async () => {
      // Arrange
      fetchStuff.mockResolvedValue({
        json: () => Promise.resolve({ invalid: "response" }),
      } as Response);

      const { result } = renderHook(
        () =>
          useStartConversation(testParams.workflowId, testParams.taskId, testParams.exampleSetId),
        {
          wrapper: createWrapper(),
        },
      );

      // Act & Assert
      await expect(
        result.current.mutateAsync({
          naturalLanguageQuery: "Show me all users",
        }),
      ).rejects.toThrow("Invalid API response structure");
    });

    test("should handle start conversation error", async () => {
      // Arrange
      fetchStuff.mockRejectedValue(new Error("Network error"));

      const { result } = renderHook(
        () =>
          useStartConversation(testParams.workflowId, testParams.taskId, testParams.exampleSetId),
        {
          wrapper: createWrapper(),
        },
      );

      // Act & Assert
      await expect(
        result.current.mutateAsync({
          naturalLanguageQuery: "Show me all users",
        }),
      ).rejects.toThrow("Network error");
    });

    test("should call onSuccess callback when provided", async () => {
      // Arrange
      const onSuccess = vi.fn();
      fetchStuff.mockResolvedValue({
        json: () => Promise.resolve(mockConversationResponse),
      } as Response);

      const { result } = renderHook(
        () =>
          useStartConversation(testParams.workflowId, testParams.taskId, testParams.exampleSetId, {
            onSuccess,
          }),
        {
          wrapper: createWrapper(),
        },
      );

      // Act
      await result.current.mutateAsync({
        naturalLanguageQuery: "Show me all users",
      });

      // Assert - React Query passes (data, variables, context) to onSuccess
      expect(onSuccess).toHaveBeenCalledWith(
        mockConversationResponse,
        { naturalLanguageQuery: "Show me all users" },
        undefined,
      );
    });
  });

  describe("useConversation", () => {
    test("should fetch conversation successfully when enabled", async () => {
      // Arrange
      fetchStuff.mockResolvedValue({
        json: () => Promise.resolve(mockConversationResponse),
      } as Response);

      renderHook(
        () =>
          useConversation(
            testParams.workflowId,
            testParams.taskId,
            testParams.exampleSetId,
            "conv-123",
            true,
          ),
        {
          wrapper: createWrapper(),
        },
      );

      // Wait for the query to complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(fetchStuff).toHaveBeenCalledWith(
        `https://api.example.com/v3/workflows/${testParams.workflowId}/tasks/${testParams.taskId}/examples/${testParams.exampleSetId}/nlsql-conversations/conv-123`,
        {},
      );
    });

    test("should not fetch when conversationId is undefined", () => {
      // Arrange
      const { result } = renderHook(
        () =>
          useConversation(
            testParams.workflowId,
            testParams.taskId,
            testParams.exampleSetId,
            undefined,
            true,
          ),
        {
          wrapper: createWrapper(),
        },
      );

      // Assert
      expect(fetchStuff).not.toHaveBeenCalled();
      expect(result.current.data).toBeUndefined();
    });

    test("should not fetch when enabled is false", () => {
      // Arrange
      const { result } = renderHook(
        () =>
          useConversation(
            testParams.workflowId,
            testParams.taskId,
            testParams.exampleSetId,
            "conv-123",
            false,
          ),
        {
          wrapper: createWrapper(),
        },
      );

      // Assert
      expect(fetchStuff).not.toHaveBeenCalled();
      expect(result.current.data).toBeUndefined();
    });

    test("should handle fetch conversation error", async () => {
      // Arrange
      fetchStuff.mockRejectedValue(new Error("Not found"));

      const { result } = renderHook(
        () =>
          useConversation(
            testParams.workflowId,
            testParams.taskId,
            testParams.exampleSetId,
            "conv-123",
            true,
          ),
        {
          wrapper: createWrapper(),
        },
      );

      // Wait for the error to occur
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(result.current.isError).toBe(true);
    });
  });

  describe("useAddFeedback", () => {
    test("should add feedback successfully", async () => {
      // Arrange
      fetchStuff.mockResolvedValue({
        json: () => Promise.resolve(mockConversationResponse),
      } as Response);

      const { result } = renderHook(
        () =>
          useAddFeedback(
            testParams.workflowId,
            testParams.taskId,
            testParams.exampleSetId,
            "conv-123",
          ),
        {
          wrapper: createWrapper(),
        },
      );

      // Act
      const response = await result.current.mutateAsync({
        feedbackText: "Add WHERE clause for active users",
      });

      // Assert
      expect(fetchStuff).toHaveBeenCalledWith(
        `https://api.example.com/v3/workflows/${testParams.workflowId}/tasks/${testParams.taskId}/examples/${testParams.exampleSetId}/nlsql-conversations/conv-123/feedback`,
        {
          method: "POST",
          body: JSON.stringify({ feedbackText: "Add WHERE clause for active users" }),
          headers: { "Content-Type": "application/json" },
        },
      );
      expect(response).toEqual(mockConversationResponse);
    });

    test("should handle add feedback error", async () => {
      // Arrange
      fetchStuff.mockRejectedValue(new Error("Validation error"));

      const { result } = renderHook(
        () =>
          useAddFeedback(
            testParams.workflowId,
            testParams.taskId,
            testParams.exampleSetId,
            "conv-123",
          ),
        {
          wrapper: createWrapper(),
        },
      );

      // Act & Assert
      await expect(
        result.current.mutateAsync({
          feedbackText: "Add WHERE clause for active users",
        }),
      ).rejects.toThrow("Validation error");
    });

    test("should not make request when conversationId is undefined", async () => {
      // Arrange
      const { result } = renderHook(
        () =>
          useAddFeedback(
            testParams.workflowId,
            testParams.taskId,
            testParams.exampleSetId,
            undefined,
          ),
        {
          wrapper: createWrapper(),
        },
      );

      // Act & Assert
      await expect(
        result.current.mutateAsync({
          feedbackText: "Add WHERE clause for active users",
        }),
      ).rejects.toThrow("No conversationId");

      expect(fetchStuff).not.toHaveBeenCalled();
    });
  });

  describe("useEndConversation", () => {
    test("should end conversation successfully", async () => {
      // Arrange
      const endResponse = { data: {}, errors: [] };
      fetchStuff.mockResolvedValue({
        json: () => Promise.resolve(endResponse),
      } as Response);

      const { result } = renderHook(
        () =>
          useEndConversation(
            testParams.workflowId,
            testParams.taskId,
            testParams.exampleSetId,
            "conv-123",
          ),
        {
          wrapper: createWrapper(),
        },
      );

      // Act
      const response = await result.current.mutateAsync();

      // Assert
      expect(fetchStuff).toHaveBeenCalledWith(
        `https://api.example.com/v3/workflows/${testParams.workflowId}/tasks/${testParams.taskId}/examples/${testParams.exampleSetId}/nlsql-conversations/conv-123`,
        { method: "DELETE" },
      );
      expect(response).toEqual(endResponse);
    });

    test("should handle end conversation error", async () => {
      // Arrange
      fetchStuff.mockRejectedValue(new Error("Server error"));

      const { result } = renderHook(
        () =>
          useEndConversation(
            testParams.workflowId,
            testParams.taskId,
            testParams.exampleSetId,
            "conv-123",
          ),
        {
          wrapper: createWrapper(),
        },
      );

      // Act & Assert
      await expect(result.current.mutateAsync()).rejects.toThrow("Server error");
    });

    test("should not make request when conversationId is undefined", async () => {
      // Arrange
      const { result } = renderHook(
        () =>
          useEndConversation(
            testParams.workflowId,
            testParams.taskId,
            testParams.exampleSetId,
            undefined,
          ),
        {
          wrapper: createWrapper(),
        },
      );

      // Act & Assert
      await expect(result.current.mutateAsync()).rejects.toThrow("No conversationId");

      expect(fetchStuff).not.toHaveBeenCalled();
    });
  });

  describe("API response validation", () => {
    test("should handle API response with errors", async () => {
      // Arrange
      fetchStuff.mockResolvedValue({
        json: () => Promise.resolve(mockErrorResponse),
      } as Response);

      const { result } = renderHook(
        () =>
          useStartConversation(testParams.workflowId, testParams.taskId, testParams.exampleSetId),
        {
          wrapper: createWrapper(),
        },
      );

      // Act
      const response = await result.current.mutateAsync({
        naturalLanguageQuery: "",
      });

      // Assert
      expect(response).toEqual(mockErrorResponse);
      expect(response.errors).toHaveLength(1);
      expect(response.errors[0].title).toBe("Validation Error");
    });

    test("should handle malformed JSON response", async () => {
      // Arrange
      fetchStuff.mockResolvedValue({
        json: () => Promise.reject(new Error("Invalid JSON")),
      } as Response);

      const { result } = renderHook(
        () =>
          useStartConversation(testParams.workflowId, testParams.taskId, testParams.exampleSetId),
        {
          wrapper: createWrapper(),
        },
      );

      // Act & Assert
      await expect(
        result.current.mutateAsync({
          naturalLanguageQuery: "Show me all users",
        }),
      ).rejects.toThrow("Invalid JSON");
    });
  });
});
