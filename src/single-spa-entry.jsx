/* eslint-disable i18next/no-literal-string */
import "./initialize-i18n";
import React from "react";
import { createRoot } from "react-dom/client";
import singleSpaReact from "single-spa-react";
import { App } from "./App";
import authState from "./utils/authState";
import "./set-public-path";
import packageJson from "../package.json";
import Observability from "./observability";
import v0 from "./services/v0";
import ReactDOMClient from "react-dom/client";

const elementId = "transform-client";
const domElementGetter = () => document.getElementById(elementId);
const isStandalone = process.env.REACT_APP_RUNTIME_MODE === "standalone";

const errorBoundary = isStandalone ? () => <div>Failed to load UI</div> : undefined;

// Each front-end app in the client-hub follows a lifecycle.
// See "Application Lifecycle" in https://floqast.atlassian.net/wiki/spaces/CLIO/pages/3207856139/How+to+Develop+with+Internationalization+and+Localization+in+Mind+-+Net+New+Development+Workflow#%F0%9F%AA%9C--Translation-Readiness-Steps
// eslint-disable-next-line
const reactLifecycles = singleSpaReact({
  React,
  ReactDOMClient,
  rootComponent: App,
  domElementGetter,
  errorBoundary,
  renderType: "createRoot",
});

export const mount = async ({ cssLifecycles, ...props }) => {
  Observability.setTag("client", "transform-client");
  Observability.setTag("version", packageJson.version);
  authState.set("jwt", props.jwt);

  // Wait for i18n to initialize
  await import("./initialize-i18n");

  if (typeof cssLifecycles?.mount === "function") {
    await cssLifecycles.mount(props);
  }

  return reactLifecycles.mount(props);
};
export const unmount = ({ cssLifecycles, ...props }) => {
  Observability.removeTag("client");
  Observability.removeTag("version");
  if (typeof cssLifecycles?.unmount === "function") {
    cssLifecycles.unmount(props);
  }

  return reactLifecycles.unmount(props);
};
