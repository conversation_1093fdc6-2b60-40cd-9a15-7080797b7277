{"components": {"ActivityHistoryItem": {"runBy": "Run by"}, "ActivityLogSideDrawer": {"activityLog": "Activity Log", "close": "Close"}, "AddInputModal": {"addInput": "Add Input", "cancel": "Cancel", "save": "Save", "selectType": "Select Type"}, "AddStepButton": {"addNewStep": "Add New Step", "unableCreateNewStep": "Unable to create new step"}, "AddTaskModal": {"addStep": "Add Step", "cancel": "Cancel", "editStep": "Edit Step", "Errors": {"errorFetchingStrat": "Error fetching strategies", "noData": "No data returned", "noStratAvailable": "No strategies available", "unsupportedStrat": "Unsupported strategy"}, "loading": "Loading...", "name": "Name", "noDataAvailable": "No data available", "save": "Save", "selectStrat": "Select Strategy", "strategy": "Strategy"}, "AgentHistoryList": {"errorOccurredWhileLoadingRuns": "Error occurred while loading runs", "noRunsFound": "No runs found", "unexpectedError": "An unexpected error occurred"}, "AgentList": {"oneHumanStep": "One human step is required for this agent."}, "AgentListPage": {"agents": "Agents", "create": "Create", "search": "Search", "searchAgents": "Search Agents"}, "AgentsTable": {"connectedData": "Connected Data", "description": "Description", "emptyState": "No agents found", "lastEdited": "Last Edited", "lastRun": "Last Run", "name": "Name", "unexpectedFallthrough": "Unexpected fallthrough while rendering agents table"}, "ApiKeysTab": {"APIKeyCreated": "API Key Created", "apiKeyDeleted": "API Key deleted", "apiKeys": "API Keys:", "APImakeRequests": "API Make Requests", "cancel": "Cancel", "close": "Close", "created": "Created", "createdNewAPIKey": "A new API key has been created with the secret key: {{secretKey}}", "createKey": "Create Key", "delete": "Delete", "deleteAPIKey": "Delete API Key", "lastUsed": "Last Used", "noAPIKey": "No API Key", "permanentlyDeleteAPIKey": "Permanently Delete API Key", "pleaseCopySecret": "Please copy this secret key now. You won't be able to see it again!", "role": "Role", "secretAPIKey": "Secret API Key", "secretKey": "Secret Key", "somethingWentWrong": "Something went wrong", "startAPIKey": "Start API Key", "unableCreateAPI": "Unable to create API key", "unableDeleteAPI": "Unable to delete API key"}, "Audit": {"Errors": {"failedPrincipals": "Failed to load principals"}}, "BackToBuilderButton": {"backToBuilder": "Back to Builder"}, "Builder": {"add": "Add", "addFlag": "Add Flag", "autoOpeningNewOutput": "Auto-opening new output", "Errors": {"couldNotFindTaskInput": "Could not find task input for example input", "errorDuringCleanup": "Error during cleanup", "invalidEntryKey": "Invalid entry key", "noData": "No data returned"}, "stepOutput": "Step {{index}} Output"}, "BuilderLoadingPage": {"back": "Back", "createVersion": "Create Version", "downloadFile": "Download File", "noActiveExampleFound": "No active example found", "noExamplesFound": "No examples found", "noWorkflowID": "No workflow ID provided", "selectVersion": "Select Version"}, "BuilderPage": {"active": "Active", "addNewStep": "Add New Step", "analyzingFiles": "Analyzing files...", "archived": "Archived", "downloadFile": "Download File", "draft": "Draft", "edit": "Edit", "Errors": {"currentTaskNotFound": "Current task not found", "errorUpdatingTask": "Error updating task", "fetchJEMTemplate": "Failed to fetch JEM template", "fetchMutation": "Failed to fetch mutation", "fileFromURI": "Failed to get file from URI", "noIDsprovided": "No ID provided", "taskExampleIDMissing": "Task example ID missing"}, "Hooks": {"Errors": {"errorCreatingContext": "Error creating file context", "failedSetExampleInput": "Failed to set example input", "failedUploadFile": "Failed to upload file", "fetchJEMTemplate": "Failed to fetch JEM template", "fileContextDisabled": "File context is disabled", "noDataReturnedCEI": "No data returned from create example input", "noDataReturnedCTI": "No data returned from create task input", "noDataReturnedCWI": "No data returned from create workflow input", "noIDsprovided": "No ID provided", "noWorkflowID": "No workflow ID provided", "unsupportedInputType": "Unsupported input type"}}, "pleaseWaitStep": "Please wait for the current step to complete", "publish": "Publish", "reachToSupport": "Reach out to support", "readyToRun": "Ready to run", "stepTaskName": "Step: {{currentTask}}", "thisVersionIsArchived": "This version is archived", "thisVersionIsDraft": "This version is a draft", "thisVersionIsPublished": "This version is published", "viewDetails": "View Details"}, "BuilderPageHooks": {"task": "Task"}, "BuilderSectionActionBar": {"actionBarEdit": "Edit", "actionBarSave": "Save", "createBlankVersion": "Create a blank version", "deleteStep": "Delete Step", "deleteStepAfter": "Delete this step and everything after", "draftOneOutput": "Draft One Output", "rename": "<PERSON><PERSON>", "save": "Save"}, "BuilderSectionHeader": {"Errors": {"taskNotFound": "Task not found"}}, "BuilderSectionTabs": {"build": "Build", "InputForm": {"number": "Number"}, "inputs": "Inputs", "summary": "Summary", "versions": "Versions"}, "BuilderV3API": {"Errors": {"badNetworkResponse": "Network response was not ok", "failedCreateExample": "Failed to create example", "failedCreateInputs": "Failed to create inputs", "failedCreateOutputs": "Failed to create outputs", "failedCreateTask": "Failed to create task", "failedCreateTaskInput": "Failed to create task input", "failedCreateTaskOutput": "Failed to create task output", "failedCreateWorkflow": "Failed to create workflow", "failedDeleteExample": "Failed to delete example", "failedDeleteInputs": "Failed to delete inputs", "failedDeleteOutputs": "Failed to delete outputs", "failedExampleInputs": "Failed to fetch example inputs", "failedExampleOutputs": "Failed to fetch example outputs", "failedGetExample": "Failed to get example", "failedGetTask": "Failed to get task", "failedGetTaskInputURI": "Failed to get task input URI", "failedGetTaskOutputs": "Failed to get task outputs", "failedInputFile": "Failed to process input file", "failedOutputFile": "Failed to process output file", "failedToGetWorkflow": "Failed to get workflow", "failedUpdateTask": "Failed to update task", "failedWorkflowIn": "Failed to get workflow input", "failedWorkflowInputs": "Failed to get workflow inputs", "noDataExample": "No data available for example", "noDataMessage": "No data available for message", "noPrevTaskOutputs": "No previous task outputs", "noRunID": "No run ID provided", "noStrategyType": "No strategy type provided", "noTaskDelete": "No task to delete", "noTaskRevert": "No task to revert", "taskDataUndefined": "Task data is undefined", "taskIDinput": "Task ID required for input", "taskIDoutput": "Task ID required for output", "workflowDataUndefined": "Workflow data is undefined", "workflowInputUri": "Failed to get workflow input URI"}}, "Chat": {"actions": "Actions", "areYouSureRegenerateResponse": "Are you sure you want to regenerate this message? Regenerating this response will erase all subsequent messages in the thread.", "cancel": "Cancel", "chatMSGTextArea": "Chat message textarea", "confirm": "Confirm", "couldTakeAMinute": "This could take a minute", "delete": "Delete", "deleteMessage": "Are you sure you want to delete this message? Deleting this response will erase all subsequent messages in the thread.", "editMessage": "Are you sure you want to edit this message? Editing this response will erase all subsequent messages in the thread.", "enhanceWithAI": "Enhance with AI", "enhancingWithAI": "Enhancing with AI", "regenerateWithAI": "Regenerate with AI", "Errors": {"emptyMessage": "Message cannot be empty", "invalidConfirm": "Invalid confirmation type", "noMsgIDDel": "No message ID for deletion", "noMsgIDEdit": "No message ID for editing", "noMsgIDRegen": "No message ID for regeneration"}, "message": "Message", "messageDeleted": "Message deleted", "messageRegenerated": "Message regenerated", "no": "No", "regenerate": "Regenerate", "regenerateMessage": "Regenerate message", "save": "Save", "send": "Send", "submit": "Submit", "thankYouForYourFeedback": "Thank you for your feedback!", "TODO": "TODO", "typeCommand": "Type Command", "undo": "Undo", "yourFeedbackHelpsUsImprove": "Your feedback helps us improve user Experience."}, "ChatMessage": {"areYouSureRegenerateResponse": "Are you sure you want to regenerate response?", "deleteMessage": "Delete Message", "draftMessage": "Draft Message", "edit": "Edit", "editMessage": "Edit Message", "Errors": {"noIDsprovided": "No ID provided", "noMessageID": "No message ID provided"}, "messageDeleted": "Message deleted", "messageRegenerated": "Message regenerated", "regenerateAssistantResponse": "Regenerate Assistant Response"}, "ChatWindow": {"chatWithAI": "Chat with AI", "Errors": {"appSyncAuthenticationValidationInvalid": "AppSync authentication validation invalid", "noIDsprovided": "No ID provided", "rejectedFiles": "Some files were rejected", "unexpectedInternalError": "Unexpected internal error"}, "includeSelectedRange": "Include selected range", "theRange": "The range"}, "ConfirmMessageActionDialogue": {"aboutTo": "You are about to", "aboutTo2": "This action cannot be undone", "cancel": "Cancel", "confirmAction": "Confirm Action", "Errors": {"noMessageID": "No message ID provided"}, "save": "Save"}, "ConfirmStepDeletionDialog": {"aboutToDeleteStep": "You are about to delete your step {{taskName}}, saved at {{taskCreatedAt}}.", "aboutToDeleteStepCascade": "You are about to delete your step {{taskName}}, saved at {{taskCreatedAt}}, and all steps following it.", "aPreviousStep": "A previous step", "cancel": "Cancel", "delete": "Delete", "deleteStepAfter": "Delete this step and everything after", "deleteThisStep": "Delete this step", "Errors": {"noIDsprovided": "No ID provided"}, "willRemoveAllStepsAfter": "This will permanently remove this step and all steps after.", "willRemoveStep": "This will permanently remove this step."}, "Connections": {"Actions": {"manage": "Manage", "requestAccess": "Request Access", "setup": "Set Up"}, "Errors": {"loadingPage": "Error loading page", "pleaseTryAgain": "Please try again"}, "Header": {"searchPlaceholder": "Filter connections"}, "Permissions": {"adminAccessRequired": "Requires admin access"}, "Status": {"connected": "Connected"}, "title": "Transform Connections"}, "CreateAgent": {"experiments": "Experiments", "modes": "Modes", "off": "Off", "on": "On"}, "CreateWorkflowWizard": {"back": "Back", "cancel": "Cancel", "details": "Details", "nameAndDescription": "Name and Description", "next": "Next", "selectAgentType": "Select Agent Type", "selectInput": "Select Input"}, "DashboardPage": {"attemptedRuns": "Attempted Runs: {{totalAttemptedRuns}}", "completedAttempted": "of attempted runs completed", "completion": "Completion", "completionRate": "Completion Rate: {{completionRate}}%", "count": "Count", "createdAt": "Created At", "dashboard": "Dashboard", "date": "Date", "name": "Name", "runDetails": "Run Details", "runsByDate": "Runs by Date", "runsByStatus": "Runs by Status", "runStats": "Run Statistics", "status": "Status", "strategy": "Strategy", "taskRunStats": "Task Run Statistics", "taskRunStatsByDate": "Task Run Statistics by Date", "taskRunStatsByStrat": "Task Run Statistics by Strategy", "total": "Total", "totalRuns": "Total Runs: {{totalRuns}}", "workflowID": "Workflow ID", "workflowRunID": "Workflow Run ID", "workflowStats": "Workflow Statistics"}, "DeleteConfirmationDialog": {"actionPermanentDeleteTeam": "This action will permanently delete the team and all associated data. This action cannot be undone.", "cancel": "Cancel", "delete": "Delete", "deleteTeam": "Delete Team"}, "DeleteInputMessageDialog": {"cancel": "Cancel", "deleteInput": "Delete Input", "Errors": {"issueDeletingInput": "Issue deleting input"}, "permanentlyDelete1": "This action will permanently delete the input", "permanentlyDelete2": "This action cannot be undone", "permanentlyDeleteInput": "Permanently Delete Input"}, "DeleteWorkflowModal": {"areYouSure": "Are you sure you want to delete this", "cancel": "Cancel", "cannotUndo": "This action cannot be undone", "confirm": "Confirm", "deletion": "Deletion"}, "DeleteWorkflowRunModal": {"allFilesUnchanged": "All files will remain unchanged.", "areYouSure": "Are you sure you want to delete this", "cancel": "Cancel", "cannotUndo": "This action cannot be undone.", "confirm": "Confirm", "runDeletion": "Run Deletion", "runRemoved1": "The run will be removed from the"}, "DetailsCard": {"addSection": "Add Section", "delete": "Delete", "detailsHere": "Details Here", "Errors": {"forItem1": "For item", "forItem2": "at index", "itemNotFoundIndex": "Item not found at index"}, "newItem": "New Item"}, "DetailsSlideout": {"cancel": "Cancel", "close": "Close", "edit": "Edit", "editTaskDescription": "Edit Task Description", "error": "Error", "Errors": {"somethingWentWrong": "Something went wrong!", "unableToGetDescription": "We were unable to get description.", "workflowDescNotFound": "Workflow task exampleSet description not found:"}, "failedToFetchDesc": "Failed to fetch task description", "generatingDescription": "Generating description...", "regenerate": "Regenerate", "save": "Save", "success": "Success", "summary": "Summary", "taskDescription": "Task Description", "updatedTaskDescription": "Updated task description"}, "DetailsTab": {"cancel": "Cancel", "edit": "Edit", "error": "Error", "stepSummary": "Step Summary", "Errors": {"somethingWentWrong": "Something went wrong!", "unableToGetDescription": "We were unable to get description.", "workflowDescNotFound": "Workflow task exampleSet description not found:"}, "failedToFetchDesc": "Failed to fetch task description", "regenerate": "Regenerate", "save": "Save", "success": "Success", "updatedTaskDescription": "Updated task description"}, "DetailSteps": {"noSummaryToPreview": "No Summary to Preview", "subStepHeading": "{{index}}. {{heading}}", "taskUnpublished": "This task currently is unpublished. Please publish this task to generate a description.", "unpublishedStep": "Unpublished Step"}, "EntityInputDropdown": {"selectOption": "Select Option"}, "ExternalJemLink": {"journalEntries": "Journal Entries"}, "FileDropdown": {"currentOutput": "Current Output", "Errors": {"noFileinDropdown": "No file in dropdown"}, "selectFile": "Select File"}, "FileInput": {"dropToUpload": "Or drop file here to upload", "formatList": "CSV, XLSX, or PDF less than 10MB", "onlyPdfSupported": "PDF less than 10MB", "uploadFiles": "Upload Files"}, "FilePreview": {"downloadFile": "Download File", "Errors": {"unexpectedFileError": "Unexpected file error"}, "fileType": "File Type", "makeActive": "Make Active", "noDataPreview": "No data to preview.", "notSupportPreview": "Preview not supported for this file type"}, "GlobalInputs": {"addInput": "Add Input", "cancelOrSave": "Cancel or save before adding new inputs", "dataInput": "Data Input", "date": "Date", "file": "File", "newInput": "New Input", "text": "Text"}, "Header": {"active": "Active", "activeAgentsRequire": "Active agents require at least one human review step.", "archived": "Archived", "draft": "Draft", "Errors": {"errorUpdatingWorkflow": "Error updating workflow status"}, "testRun": "Test Run"}, "HeaderTitle": {"ariaLabels": {"cancelEditing": "Cancel editing", "editWorkflowName": "Edit workflow name", "saveChanges": "Save changes"}, "errors": {"nameRequired": "Name cannot be empty", "nameTooLong": "Name is too long (max {{max}} characters)"}, "toast": {"errorTitle": "Error", "invalidInputTitle": "Invalid Input", "updateAgentError": "Failed to update agent name"}, "tooltip": {"editName": "Edit Name"}}, "InputArgumentValue": {"addInput": "Add Input", "Errors": {"unsupportedInputType": "Unsupported input type"}, "selectDate": "Select Date", "textInput": "Text Input"}, "InputForm": {"builderInput": "Builder Input", "cancel": "Cancel", "date": "Date", "delete": "Delete", "description": "Description", "enterDescription": "Enter description", "enterTitle": "Enter a title", "enterValue": "Enter value", "file": "File", "inputType": "Input Type", "newInput": "New Input", "requestedFormat": "Requested format", "save": "Save", "text": "Text", "title": "Title"}, "InputMessage": {"addInput": "Add Input", "cancel": "Cancel", "draftMessage": "Draft Message", "Errors": {"noIDsprovided": "No ID provided"}, "save": "Save", "selectDate": "Select Date", "textInput": "Text Input"}, "Inputs": {"Errors": {"noIDsprovided": "No ID provided"}}, "InputsDropDown": {"inputsCount": "Inputs ({{count}})"}, "InputsDropdown": {"addInputsPrevSteps": "Add inputs from previous steps", "Errors": {"inputNotFound": "Input not found", "noDataReturned": "No data returned", "noDataReturnedCEI": "No data returned from create example input", "noDataReturnedCTI": "No data returned from create task input", "noDataReturnedSEIFV": "No data returned from SEIFV", "noIDsprovided": "No ID provided", "taskNotFound": "Task not found", "unexpectedWorkflowInput": "Unexpected workflow input"}}, "InputSelectionForm": {"initialInputIntegrationDescription": "Connect to data platform", "initialInputIntegrationTitle": "Integration", "initialInputUploadFileDescription": "Upload excel, csv, etc. Add more details", "initialInputUploadFileTitle": "Upload File", "selectInitialInput": "Select initial input", "thisWillEllipsis": "This will..."}, "InputsTabSection": {"addInput": "Add input", "currentOutput": "Current Output", "edit": "Edit", "Errors": {"failedCreateInputs": "Unexpected data error: Failed to create example input", "noDataOrErrors": "Unexpected error: no data or errors returned"}, "open": "Open", "stepOutput": "Step {{index}} Output"}, "NavBar": {"backToApp": "Back to App", "dashboard": "Dashboard", "teams": "Teams"}, "NewTaskPopover": {"createStep": "Create New Step", "description": "Description", "enterDesc": "Enter a description", "enterTitle": "Enter a title", "selectStrat": "Select a strategy", "selectTool": "Select Tool", "submit": "Submit", "title": "Title"}, "PDFViewer": {"pageXofY": "{{x}} / {{y}}"}, "PublishValuesButton": {"publishValuesButtonText": "Publish Values", "publishValuesErrorHeader": "Error", "publishValuesErrorMessage": "An error occurred while publishing values", "publishValuesSuccessHeader": "Success", "publishValuesSuccessMessage": "Values published successfully"}, "ReviewStep": {"approveRun": "Approve Run", "rejectRun": "Reject Run", "resumeRun": "Resume Run"}, "Row": {"deleteAgent": "Delete Agent", "edit": "Edit", "editAgent": "Edit Agent", "oneHumanStep": "One human step is required for this agent.", "preview": "Preview", "run": "Run", "showActivityLog": "Show activity log"}, "RunInputsWizard": {"addInputsToRunAgent": "Add Inputs to Run Agent", "continue": "Continue", "enterInput": "Enter {{name}}", "enterNumber": "Enter a number", "enterText": "Enter text", "finish": "Finish", "stepOfInput": "Step {{i}} of {{inputs}}", "unsupportedInputType": "Unsupported input type", "uploadFile": "Upload File", "uploadInput": "Upload {{name}}", "uploadInputFile": "Upload {{name}} File"}, "Runner": {"allAgents": "All Agents"}, "RunnerHeader": {"activeAgentsRequire": "Active agents require at least one human review step.", "activityLog": "Activity Log", "allAgents": "All Agents", "runAgent": "Run Agent"}, "RunnerPage": {"agentHasNoSteps": "Agent currently has no steps.", "agentHasNoSteps2": "Please navigate to the builder to add and publish steps for this agent.", "agentPreview": "Agent Preview", "agentPreview2": ": Review the steps prior to running.", "Errors": {"badNetworkResponse": "Network response was not ok", "inputError": "Unexpected error: Failed to create workflow run input", "noDataFromWorkflowRun": "Unexpected error: Did not receive data from successful workflow run creation", "noDataReturned": "No data returned", "noFileFoundForID": "No file found for input ID: ", "noInputFoundForID": "No input found for ID: ", "noRunData": "No run data received", "noWorkflowID": "No workflow ID specified"}}, "ScriptsDropdown": {"selectStep": "Select Step"}, "ScriptsPage": {"Errors": {"failedToFetchScript": "Failed to fetch script", "failedToSaveScript": "Failed to save script"}, "scriptSaved": "<PERSON><PERSON><PERSON> saved", "workflow": "Workflow", "workflowScripts": "Workflow Scripts"}, "SettingsSideDrawer": {"close": "Close", "save": "Save", "saving": "Saving", "settings": "Settings", "settingsError": "Error saving changes", "settingsSaved": "Successfully saved changes"}, "SourceTreeView": {"noConnectionsAvailable": "No connections available", "search": "Search", "searchConnections": "Search Connections", "treeViewTitle": "Data Sources"}, "Spreadsheet": {"Errors": {"fileTypeUnsupported": "File type not supported", "unexpectedFileError": "Unexpected file error"}}, "SQLEditor": {"checkSQLSyntax": "Check SQL Syntax", "ensureInputsFilled": "Ensure inputs are filled", "enterSQLQuery": "Enter SQL Query", "Errors": {"badNetworkResponse": "Network response was not ok", "emptyPrompt": "Empty prompt", "enterSQLDescription": "Enter SQL description", "errorExecutingFlolake": "Error executing <PERSON>", "errorExecutingWorkflow": "Error executing workflow", "errorHandlingInputs": "Error handling inputs", "errorUpdatingTask": "Error updating task", "generationFailed": "Generation failed", "generationFailedMessage": "Generation failed message", "noDataReturnedCEI": "No data returned from create example input", "noDataReturnedCTI": "No data returned from create task input", "noDataReturnedCWI": "No data returned from create workflow input", "noExampleOutputID": "No example output ID", "noIDsprovided": "No ID provided", "schemaError": "<PERSON><PERSON><PERSON> error", "taskNotFound": "Task not found"}, "execute": "Execute", "generate": "Generate", "incompleteInput": "Incomplete Input", "invalidSQLQuery": "Invalid SQL Query", "noSQLQuery": "No SQL Query", "pleaseCheckQuery": "Please check your query", "select": "Select", "sqlEditorPrompt": "Describe what data you want to retrieve, e.g. 'Get all account data in Coupa'.", "sqlEditorPromptLabel": "SQL Generation Prompt", "sqlEditorToggleLabel": "AI Mode", "sqlGenerated": "SQL Generated", "sqlGeneratedSuccess": "SQL generated successfully", "taskBeenRunSuccessfully": "Task has been run successfully", "taskRunFailed": "Task run failed", "taskRunSuccess": "Task run successful"}, "SQLEditorSplit": {"cancel": "Cancel", "chat": "Cha<PERSON>", "chatMSGTextArea": "Chat message textarea", "checkSQLSyntax": "Please check your SQL syntax", "copySQL": "Copy SQL", "copySQLErrorMessage": "Copy Error Message", "copyUserPrompt": "Copy Prompt", "editor": "Editor", "Errors": {"generationFailed": "Generation failed", "generationFailedMessage": "We were unable to generate the SQL query. Please try again.", "invalidConnection": "Invalid connection variable", "noConvertedSQL": "SQL Conversion failed", "noConvertedSQLMessage": "We were unable to convert the SQL query. Please try again."}, "executeLLMGeneratedSQL": "Execute SQL Statement", "generate": "Generate", "invalidSQLQuery": "Invalid SQL Query", "LlmFeedback": {"additionalFeedbackOptional": "Additional feedback (optional):", "feedbackSubmitted": "Feed<PERSON> Submitted", "feedbackSubmittedFailed": "Failed to <PERSON><PERSON>", "feedbackSubmittedFailedMessage": "Please try again later.", "feedbackSubmittedSuccess": "Thank you for your feedback!", "rateResponseBad": "Rate this response as bad", "rateResponseGood": "Rate this response as good", "submitFeedback": "Submit <PERSON>"}, "pleaseCheckQuery": "Please check your query and try again", "sqlEditor": "SQL Editor", "sqlGenerated": "SQL Generated", "sqlGeneratedSuccess": "SQL has been generated successfully", "submit": "Submit", "taskBeenRunSuccessfully": "Your task has been succesfully run.", "taskRunFailed": "Task run failed", "taskRunSuccess": "Task run successful", "typeCommand": "Type Command", "viewLLMDescription": "View LLM Description"}, "StatusChangeDialog": {"activateOnly": "Activate only", "activateTest": "Activate test", "cancel": "Cancel", "changingFromTo": "Changing from {{fromStatus}} to {{toStatus}}", "delete": "Delete", "deleteVersion": "Delete Version", "deleteVersionBody": "This will permanently delete this version. This action cannot be undone.", "Errors": {"noIDsprovided": "No workflowId, taskId, or exampleSetId provided in params", "taskNotDefined": "Task not defined"}, "statusChange1": "Reverting this version to a draft will leave no versions active. Runs cannot be made on this agent until an active version is selected.", "statusChange2": "Activating this version will deactivate all other active versions. Runs will be made using this version.", "statusChange3": "This action will unarchive this version and activate it. All other active versions will be deactivated. Runs will be made using this version.", "statusChange4": "This will convert this version to a draft. Runs will not use this version unless is it selected as 'active.'", "statusChange5": "This action will archive this version. Archived versions can be restored at any time.", "statusChange6": "This action will archive this version. Archived versions can be restored at any time. Runs cannot be made on this agent until an active version is selected.", "statusChange7": "This action will deactivate this version. Runs will not be made using this version.", "statusChange8": "Activating this version will set it as the default for future agent runs. We recommend running a test to ensure every step works before proceeding."}, "Step": {"Errors": {"noDataReturnedFromTaskRun": "Unexpected error: no data returned from task run log"}, "viewErrorDetails": "View Error Details"}, "StepList": {"agentSteps": "Agent Steps", "editStep": "Edit step", "Errors": {"badNetworkResponse": "Network response was not ok", "errorRunningWorkflow": "Error running workflow:", "requestError": "request error"}, "followingStepsExecuted": "The following steps will be executed when an agent is run.", "showStepSummary": "Show step summary"}, "StepResults": {"results": "Results", "runBy": "Run By"}, "Steps": {"delete": "Delete", "Errors": {"noIDsprovided": "No ID provided"}, "view": "View"}, "StepsSidebarList": {"add": "Add", "addStep": "Add a Step", "blockType": "Block Type", "inputs": "Inputs"}, "StepWarningMessage": {"stepWarningMessage": "This step failed. <PERSON> to try again.", "stepWarningMessageEdit": "Edit to try again."}, "TeamDropdownSelect": {"selectTeamMigrate": "Select Team to Migrate"}, "TeamMigrationModal": {"cancel": "Cancel", "migrate": "Migrate", "newCreatedBy": "New Created By", "newEntityID": "New Entity ID", "selectTeamMigrate": "Select Team to Migrate"}, "TeamModal": {"cancel": "Cancel", "save": "Save", "teamExternalID": "Team External ID", "teamName": "Team Name"}, "TeamPage": {"additionalFunctionality": "Additional Functionality", "APISwaggerUI": "API Swagger UI", "notFound": "Not found", "team": "Team"}, "UseCaseForm": {"addCustomPrompt": "Add a custom prompt to the step. This will override the default prompt for the step.", "custom": "Custom", "templates": "Templates"}, "UserPage": {"actions": "Actions", "actionWillMoveUser": "This action will move the user", "actionWillMoveYou": "This action will move you", "cancel": "Cancel", "changeTeam": "Change team", "changeTeamAndLogOut": "Change team and log out", "deleteUser": "Delete User", "emailedTempPassword": "Emailed temporary password", "emailSent": "Email sent", "notFound": "Not found", "permanentlyDelete": "Permanently Delete", "permDeleteUser": "Permanently Delete User", "resendEmail": "<PERSON><PERSON><PERSON>", "roleColon": "Role: ", "roleUpdated": "Role updated", "somethingWentWrong": "Something went wrong", "switchTeams": "Switch Teams", "team": "Team", "teamColon": "Team:", "typeDelete": "Type Delete: ", "unableDeleteUser": "Unable to delete user", "unableTempPassword": "Unable to generate temporary password", "unableToUpdateUserRole": "Unable to update user role", "userDeleted": "User deleted", "userDeletedMessage": "User deleted successfully", "userDeleteUndone": "User deletion undone", "userRoleUpdated": "User role updated", "userStatus": "Status: {{status}}", "you": "You"}, "UsersTab": {"email": "Email", "getStartedInviting": "Get Started Inviting", "inviteByEmail": "Invite by <PERSON><PERSON>", "inviteMoreUsers": "Invite More Users", "inviteSent": "<PERSON><PERSON><PERSON> sent", "inviteUser": "Invite User", "noOneHere": "No one here", "role": "Role", "sentInvitationTo": "Sent invitation to", "somethingWentWrong": "Something went wrong", "status": "Status", "unableToInviteUser": "Unable to invite user", "users": "Users", "you": "You"}, "VersionCard": {"active": "Active", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "editTitle": "Edit title", "Errors": {"noIDsprovided": "No workflowId, taskId, or exampleSetId provided in params for Builder Page"}, "newVersionName": "New Version Name", "renameVersion": "Rename Version", "setActive": "Set as active", "view": "View"}, "VersionDropdown": {"Errors": {"noIDsprovided": "No ID provided"}}, "VersionOptionsDropdown": {"deleteVersion": "Delete Version", "Errors": {"noExamplePropsVOD": "No example props for Version Options Dropdown"}, "newVersionName": "New Version Name", "renameVersion": "Rename Version"}, "VersionsDropdown": {"comments": "Comments", "draft": "Draft", "drafts": "Drafts", "modified": "Modified", "modifiedBy": "Modified By", "newVersion": "New Version", "noVersionsFound": "No versions found", "number": "Number", "published": "Published", "versions": "Versions", "view": "View", "viewing": "Viewing"}, "VersionSlideout": {"archived": "Archived", "default": "<PERSON><PERSON><PERSON>", "developmentVer": "Development version", "draft": "Draft", "Errors": {"noIDsprovided": "No ID provided"}, "finalizedVersionDefault": "Finalized version default", "forWorkflowToRun": "For workflow to run", "noActiveVersions": "No active versions", "notInUse": "Not in use", "noWorkflowRunsCan": "No workflow runs can be made", "published": "Published", "select": "Select", "selected": "Selected", "versionExecutedWorkflow": "Version executed workflow", "versionOfStep": "Version of Step", "versions": "Versions", "versionsCanBeOTFS": "Versions can be on the fly selected"}, "VersionsTab": {"active": "Active", "draft": "Draft", "versionsTabSaved": "Saved"}, "WorkflowForm": {"advancedSetup": "Advanced Setup", "agentName": "Agent Name", "description": "Description", "entity": "Entity", "Errors": {"agentNameRequired": "Agent name is required", "errorFetchingEntities": "Error fetching entities"}, "loading": "Loading...", "nameAgent": "Name your agent", "noDataAvailable": "No data available", "noEntityAvailable": "No entity available", "optionalDescription": "Optional description", "selectEntity": "Select Entity"}, "WorkflowModals": {"agentDescriptionOptional": "Agent description (optional)", "agentNamePlaceholder": "Agent Name Placeholder", "cancel": "Cancel", "createNewWorkflow": "Create New Workflow", "editAgent": "Edit Agent", "save": "Save"}, "WorkflowRunNotes": {"agentDescriptionOptional": "Agent description (optional)", "cancel": "Cancel", "save": "Save"}, "WorkflowRuns": {"completed": "Completed", "failed": "Failed", "inProgress": "In Progress", "rejected": "Rejected"}, "WorkflowRunTable": {"deleteRun": "Delete Run", "editNotes": "Edit Notes", "name": "Name", "notYetRun": "has not yet been run", "runAgain": "Run Again", "runBy": "Run By", "startedAt": "Started At", "status": "Status", "this": "This", "view": "View", "viewRunHistory": "View Run History", "workFlowRunTableSubtitle": "Run this workflow to see results"}, "WorkflowsTab": {"createdAt": "Created At", "filterWorkflows": "Filter Workflows", "hasBeenMigrated": "has been migrated", "ID": "ID", "loadMore": "Load More", "migrate": "Migrate", "name": "Name", "noWorkflowsFound": "No workflows found", "somethingWentWrong": "Something went wrong", "theWorkflow": "The workflow", "unableToMigrateWorkflow": "Unable to migrate workflow", "viewScripts": "<PERSON>", "workflowMigrated": "Workflow migrated", "workflows": "Workflows"}, "WorkflowTable": {"build": "Build", "buildWorkflow": "Build Workflow", "createdBy": "Created By", "delete": "Delete", "deleteWorkflow": "Delete Workflow", "description": "Description", "edit": "Edit", "editWorkflow": "Edit Workflow", "lastModified": "Last Modified", "lastRun": "Last Run", "run": "Run", "runWorkflow": "Run Workflow", "status": "Status"}}}