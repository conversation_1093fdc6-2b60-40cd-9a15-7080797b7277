{"components": {"ActivityHistoryItem": {"runBy": "<PERSON><PERSON><PERSON> par"}, "ActivityLogSideDrawer": {"activityLog": "Journal d'activité", "close": "<PERSON><PERSON><PERSON>"}, "AddInputModal": {"addInput": "Ajouter une entrée", "cancel": "Annuler", "save": "Enregistrer", "selectType": "Sélectionner le type"}, "AddStepButton": {"addNewStep": "Ajouter une nouvelle étape", "unableCreateNewStep": "Impossible de créer une nouvelle étape"}, "AddTaskModal": {"addStep": "Ajouter une étape", "cancel": "Annuler", "editStep": "Modifier l'étape", "Errors": {"errorFetchingStrat": "Erreur lors de la récupération des stratégies", "noData": "<PERSON><PERSON><PERSON> donn<PERSON> retour<PERSON>", "noStratAvailable": "Aucune stratégie disponible", "unsupportedStrat": "Stratégie non prise en charge"}, "loading": "Chargement...", "name": "Nom", "noDataAvailable": "<PERSON><PERSON><PERSON> donnée disponible", "save": "Enregistrer", "selectStrat": "Sélectionner la stratégie", "strategy": "Stratégie"}, "AgentHistoryList": {"errorOccurredWhileLoadingRuns": "Erreur survenue lors du chargement des exécutions", "noRunsFound": "Aucune exécution trouvée", "unexpectedError": "Une erreur inattendue s'est produite"}, "AgentList": {"oneHumanStep": "Une étape humaine est requise pour cet agent."}, "AgentListPage": {"agents": "Agents", "create": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "searchAgents": "Agents de recherche"}, "AgentsTable": {"connectedData": "Données connectées", "description": "Description", "emptyState": "Aucun agent trouvé", "lastEdited": "Dernière modification", "lastRun": "Dernière exécution", "name": "Nom", "unexpectedFallthrough": "Passage inattendu lors du rendu du tableau des agents"}, "ApiKeysTab": {"APIKeyCreated": "Clé API créée", "apiKeyDeleted": "Clé API supprimée", "apiKeys": "Clés API :", "APImakeRequests": "API Faire des requêtes", "cancel": "Annuler", "close": "<PERSON><PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON>", "createdNewAPIKey": "Une nouvelle clé API a été créée avec la clé secrète : {{secretKey}}", "createKey": "<PERSON><PERSON><PERSON> une clé", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteAPIKey": "Supprimer la clé API", "lastUsed": "Dernière utilisation", "noAPIKey": "Aucune clé API", "permanentlyDeleteAPIKey": "Supprimer définitivement la clé API", "pleaseCopySecret": "Veuillez copier cette clé secrète maintenant. Vous ne pourrez plus la voir à nouveau !", "role": "R<PERSON><PERSON>", "secretAPIKey": "Clé API secrète", "secretKey": "<PERSON>lé secrète", "somethingWentWrong": "Un problème est survenu", "startAPIKey": "Démarrer la clé API", "unableCreateAPI": "Impossible de créer la clé API", "unableDeleteAPI": "Impossible de supprimer la clé API"}, "Audit": {"Errors": {"failedPrincipals": "Échec du chargement des principaux"}}, "Builder": {"add": "Ajouter", "addFlag": "Ajouter un drapeau", "autoOpeningNewOutput": "Ouverture automatique de la nouvelle sortie", "Errors": {"couldNotFindTaskInput": "Impossible de trouver l'entrée de tâche pour l'exemple d'entrée", "errorDuringCleanup": "Erreur pendant le nettoyage", "invalidEntryKey": "Entrée invalide pour la clé {{key}}", "noData": "<PERSON><PERSON><PERSON> donn<PERSON> retour<PERSON>"}, "stepOutput": "Étape {{index}} Sortie"}, "BuilderLoadingPage": {"back": "Retour", "createVersion": "<PERSON><PERSON><PERSON> une version", "downloadFile": "Télécharger le fichier", "noActiveExampleFound": "Aucun exemple actif trouvé", "noExamplesFound": "Aucun exemple trouvé", "noWorkflowID": "Aucun identifiant de flux de travail fourni", "selectVersion": "Sélectionner la version"}, "BuilderPage": {"active": "Actifs", "addNewStep": "Ajouter une nouvelle étape", "analyzingFiles": "Analyse des fichiers...", "archived": "Archivé", "downloadFile": "Télécharger le fichier", "draft": "Brouillon", "edit": "Modifier", "Errors": {"currentTaskNotFound": "Tâche actuelle introuvable", "errorUpdatingTask": "Erreur lors de la mise à jour de la tâche", "fetchJEMTemplate": "Échec de récupération du modèle JEM", "fetchMutation": "Échec de récupération de la mutation", "fileFromURI": "Échec de récupération du fichier à partir de l'URI", "noIDsprovided": "Aucun identifiant fourni", "taskExampleIDMissing": "Exemple d'ID de tâche manquant"}, "Hooks": {"Errors": {"errorCreatingContext": "<PERSON><PERSON>ur lors de la création du contexte de fichier", "failedSetExampleInput": "Échec de la définition de l'exemple d'entrée", "failedUploadFile": "Échec du téléchargement du fichier", "fetchJEMTemplate": "Échec de récupération du modèle JEM", "fileContextDisabled": "Le contexte de fichier est désactivé", "noDataReturnedCEI": "<PERSON><PERSON><PERSON> donn<PERSON> retournée depuis la création de l'exemple d'entrée", "noDataReturnedCTI": "Au<PERSON>ne donn<PERSON> retournée depuis l'entrée de création de tâche", "noDataReturnedCWI": "Aucune donn<PERSON> retournée depuis l'entrée de création de workflow", "noIDsprovided": "Aucun identifiant fourni", "noWorkflowID": "Aucun identifiant de flux de travail fourni", "unsupportedInputType": "Type d'entrée non pris en charge"}}, "pleaseWaitStep": "Veuillez attendre que l'étape actuelle soit terminée", "publish": "Publier", "reachToSupport": "Contactez le support", "readyToRun": "<PERSON><PERSON><PERSON><PERSON>", "stepTaskName": "Étape : {{currentTask}}", "thisVersionIsArchived": "Cette version est archivée", "thisVersionIsDraft": "Cette version est une ébauche", "thisVersionIsPublished": "Cette version est publiée", "viewDetails": "Aff<PERSON>r les détails"}, "BuilderPageHooks": {"task": "<PERSON><PERSON><PERSON>"}, "BuilderSectionActionBar": {"actionBarEdit": "Modifier", "actionBarSave": "Enregistrer", "createBlankVersion": "<PERSON><PERSON>er une version vierge", "deleteStep": "Supprimer l'étape", "deleteStepAfter": "Supprimer cette étape et tout ce qui suit", "draftOneOutput": "Première é<PERSON>uche", "rename": "<PERSON>mmer", "save": "Enregistrer"}, "BuilderSectionHeader": {"Errors": {"taskNotFound": "Tâche introuvable"}}, "BuilderSectionTabs": {"build": "Construire", "details": "Détails", "InputForm": {"number": "<PERSON><PERSON><PERSON><PERSON>"}, "inputs": "Entrées", "versions": "Versions"}, "BuilderV3API": {"Errors": {"badNetworkResponse": "La réponse du réseau n'était pas correcte", "failedCreateExample": "Échec de création de l'exemple", "failedCreateInputs": "Échec de création des entrées", "failedCreateOutputs": "Échec de création des sorties", "failedCreateTask": "Échec de la création de la tâche", "failedCreateTaskInput": "Échec de création de l'entrée de tâche", "failedCreateTaskOutput": "Échec de création de la sortie de tâche", "failedCreateWorkflow": "Échec de la création du flux de travail", "failedDeleteExample": "Échec de la suppression de l'exemple", "failedDeleteInputs": "Échec de la suppression des entrées", "failedDeleteOutputs": "Échec de la suppression des sorties", "failedExampleInputs": "Échec de récupération des exemples d'entrées", "failedExampleOutputs": "Échec de récupération des exemples de sorties", "failedGetExample": "Échec de l'obtention de l'exemple", "failedGetTask": "Échec de récupération de la tâche", "failedGetTaskInputURI": "Échec de l'obtention de l'URI d'entrée de la tâche", "failedGetTaskOutputs": "Échec de récupération des sorties de tâche", "failedInputFile": "Échec du traitement du fichier d'entrée", "failedOutputFile": "Échec du traitement du fichier de sortie", "failedToGetWorkflow": "Échec de récupération du workflow", "failedUpdateTask": "Échec de la mise à jour de la tâche", "failedWorkflowIn": "Échec de récupération des données d'entrée du workflow", "failedWorkflowInputs": "Échec de récupération des entrées du workflow", "noDataExample": "Aucune donnée disponible pour l'exemple", "noDataMessage": "Aucune donnée disponible pour le message", "noPrevTaskOutputs": "Aucune sortie de tâche précédente", "noRunID": "Aucun identifiant d'exécution fourni", "noStrategyType": "Aucun type de stratégie fourni", "noTaskDelete": "Aucune tâche à supprimer", "noTaskRevert": "Aucune tâche à annuler", "taskDataUndefined": "Les données de tâche sont indéfinies", "taskIDinput": "ID de tâche requis pour la saisie", "taskIDoutput": "ID de tâche requis pour la sortie", "workflowDataUndefined": "Les données de flux de travail ne sont pas définies", "workflowInputUri": "Échec de l'obtention de l'URI d'entrée du workflow"}}, "Chat": {"actions": "Actions", "areYouSureRegenerateResponse": "Êtes-vous sûr de vouloir régénérer ce message ? La régénération de cette réponse effacera tous les messages suivants dans la conversation.", "cancel": "Annuler", "chatMSGTextArea": "Zone de texte pour les messages de chat", "confirm": "Confirmer", "couldTakeAMinute": "<PERSON><PERSON> pour<PERSON>t prendre une minute", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteMessage": "Êtes-vous sûr de vouloir supprimer ce message ? La suppression de cette réponse effacera tous les messages suivants dans le fil de discussion.", "editMessage": "Êtes-vous sûr de vouloir modifier ce message ? La modification de cette réponse effacera tous les messages suivants dans la conversation.", "enhanceWithAI": "Améliorer avec l'IA", "enhancingWithAI": "Amélioration avec l'IA", "Errors": {"emptyMessage": "Le message ne peut pas être vide", "invalidConfirm": "Type de confirmation invalide", "noMsgIDDel": "Aucun identifiant de message pour la suppression", "noMsgIDEdit": "Aucun identifiant de message pour la modification", "noMsgIDRegen": "Aucun identifiant de message pour la régénération"}, "message": "Message", "messageDeleted": "Message supprimé", "messageRegenerated": "Message régén<PERSON><PERSON>", "no": "Non", "regenerate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "regenerateMessage": "Ré<PERSON>n<PERSON><PERSON> le message", "save": "Enregistrer", "send": "Envoyer", "submit": "So<PERSON><PERSON><PERSON>", "TODO": "À FAIRE", "typeCommand": "<PERSON><PERSON> une commande", "undo": "Annuler"}, "ChatMessage": {"areYouSureRegenerateResponse": "Êtes-vous sûr de vouloir régénérer la réponse ?", "deleteMessage": "Supprimer le message", "draftMessage": "Message brouillon", "edit": "Modifier", "editMessage": "Modifier le message", "Errors": {"noIDsprovided": "Aucun identifiant fourni", "noMessageID": "Aucun identifiant de message fourni"}, "messageDeleted": "Message supprimé", "messageRegenerated": "Message régén<PERSON><PERSON>", "regenerateAssistantResponse": "Régénérer la réponse de l'assistant"}, "ChatWindow": {"chatWithAI": "Discuter avec l'IA", "Errors": {"appSyncAuthenticationValidationInvalid": "Validation d'authentification AppSync invalide", "noIDsprovided": "Aucun identifiant fourni", "rejectedFiles": "Certains fichiers ont été rejetés", "unexpectedInternalError": "<PERSON><PERSON>ur interne inattendue"}, "includeSelectedRange": "Inclure la plage sélectionnée", "theRange": "La gamme"}, "ConfirmMessageActionDialogue": {"aboutTo": "Vous êtes sur le point de", "aboutTo2": "Cette action ne peut pas être annulée", "cancel": "Annuler", "confirmAction": "Confirmer l'action", "Errors": {"noMessageID": "Aucun identifiant de message fourni"}, "save": "Enregistrer"}, "ConfirmStepDeletionDialog": {"aboutToDeleteStep": "Vous êtes sur le point de supprimer une étape", "aPreviousStep": "Une étape précédente", "cancel": "Annuler", "deleteStep": "Supprimer l'étape", "Errors": {"noIDsprovided": "Aucun identifiant fourni"}, "willRemoveAllStepsAfter": "supprimera toutes les étapes suivantes"}, "Connections": {"Actions": {"manage": "<PERSON><PERSON><PERSON>", "requestAccess": "<PERSON><PERSON><PERSON> l'accès", "setup": "Configuration"}, "Errors": {"loadingPage": "Erreur de chargement de la page", "pleaseTryAgain": "<PERSON><PERSON><PERSON>z réessayer"}, "Header": {"searchPlaceholder": "Filtrer les connexions"}, "Permissions": {"adminAccessRequired": "Nécessite un accès administrateur"}, "Status": {"connected": "Connecté"}, "title": "Transformer les connexions"}, "CreateAgent": {"experiments": "Expériences", "modes": "Modes", "off": "Désactivé", "on": "Activé"}, "CreateWorkflowWizard": {"back": "Retour", "cancel": "Annuler", "details": "Détails", "nameAndDescription": "Nom et Description", "next": "Suivant", "selectAgentType": "Sélectionner le type d'agent", "selectInput": "Sélectionner l'entrée"}, "DashboardPage": {"attemptedRuns": "Tentatives effectuées : {{totalAttemptedRuns}}", "completedAttempted": "de tentatives de courses terminées", "completion": "Achèvement", "completionRate": "Taux d'achèvement : {{completionRate}}%", "count": "Nombre", "createdAt": "<PERSON><PERSON><PERSON>", "dashboard": "Tableau de bord", "date": "Date", "name": "Nom", "runDetails": "Détails de l'exécution", "runsByDate": "Courses par date", "runsByStatus": "Exécutions par statut", "runStats": "Statistiques d'exécution", "status": "Statut", "strategy": "Stratégie", "taskRunStats": "Statistiques d'exécution des tâches", "taskRunStatsByDate": "Statistiques d'exécution des tâches par date", "taskRunStatsByStrat": "Statistiques d'exécution des tâches par stratégie", "total": "Total", "totalRuns": "Total des courses : {{totalRuns}}", "workflowID": "ID du workflow", "workflowRunID": "ID d'exécution du workflow", "workflowStats": "Statistiques de flux de travail"}, "DeleteConfirmationDialog": {"actionPermanentDeleteTeam": "Cette action supprimera définitivement l'équipe et toutes les données associées. Cette action ne peut pas être annulée.", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteTeam": "Supprimer l'équipe"}, "DeleteInputMessageDialog": {"cancel": "Annuler", "deleteInput": "Supprimer l'entrée", "Errors": {"issueDeletingInput": "Problème lors de la suppression de l'entrée"}, "permanentlyDelete1": "Cette action supprimera définitivement l'entrée", "permanentlyDelete2": "Cette action ne peut pas être annulée", "permanentlyDeleteInput": "Supprimer définitivement l'entrée"}, "DeleteWorkflowModal": {"areYouSure": "Êtes-vous sûr de vouloir supprimer ceci", "cancel": "Annuler", "cannotUndo": "Cette action ne peut pas être annulée", "confirm": "Confirmer", "deletion": "Suppression"}, "DeleteWorkflowRunModal": {"allFilesUnchanged": "Tous les fichiers resteront inchangés.", "areYouSure": "Êtes-vous sûr de vouloir supprimer ceci", "cancel": "Annuler", "cannotUndo": "Cette action ne peut pas être annulée.", "confirm": "Confirmer", "runDeletion": "Exécuter la <PERSON>", "runRemoved1": "La course sera supprimée du"}, "DetailsCard": {"addSection": "Ajouter une section", "delete": "<PERSON><PERSON><PERSON><PERSON>", "detailsHere": "<PERSON><PERSON><PERSON> ici", "Errors": {"forItem1": "Pour l'article", "forItem2": "à l'index", "itemNotFoundIndex": "Élément introuvable à l'index"}, "newItem": "Nouvel élément"}, "DetailsSlideout": {"cancel": "Annuler", "close": "<PERSON><PERSON><PERSON>", "details": "Détails", "edit": "Modifier", "editTaskDescription": "Modifier la description de la tâche", "error": "<PERSON><PERSON><PERSON>", "Errors": {"somethingWentWrong": "Un problème est survenu !", "unableToGetDescription": "Nous n'avons pas pu obtenir la description.", "workflowDescNotFound": "Description de la tâche de flux de travail non trouvée :"}, "failedToFetchDesc": "Échec de récupération de la description de la tâche", "generatingDescription": "Génération de la description...", "regenerate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "save": "Enregistrer", "success": "C'est bon !", "taskDescription": "Description de la tâche", "updatedTaskDescription": "Description de tâche mise à jour"}, "DetailsTab": {"cancel": "Annuler", "edit": "Modifier", "error": "<PERSON><PERSON><PERSON>", "Errors": {"somethingWentWrong": "Un problème est survenu !", "unableToGetDescription": "Nous n'avons pas pu obtenir la description.", "workflowDescNotFound": "Description de la tâche de flux de travail non trouvée :"}, "failedToFetchDesc": "Échec de récupération de la description de la tâche", "regenerate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "save": "Enregistrer", "success": "C'est bon !", "updatedTaskDescription": "Description de tâche mise à jour"}, "EntityInputDropdown": {"selectOption": "Sélectionner une option"}, "ExternalJemLink": {"journalEntries": "Écritures de journal"}, "FileDropdown": {"currentOutput": "Sortie actuelle", "Errors": {"noFileinDropdown": "<PERSON><PERSON>n fichier dans le menu déroulant"}, "selectFile": "Sélectionner un fichier"}, "FileInput": {"dropToUpload": "<PERSON><PERSON> dé<PERSON>z le fichier ici pour le charger", "formatList": "CSV, XLSX ou PDF de moins de 10 Mo", "uploadFiles": "Téléchargez des fichiers"}, "FilePreview": {"downloadFile": "Télécharger le fichier", "Errors": {"unexpectedFileError": "<PERSON><PERSON><PERSON> <PERSON><PERSON> inattendue"}, "fileType": "Type de fichier", "makeActive": "Activer", "noDataPreview": "Aucune donnée à prévisualiser.", "notSupportPreview": "Aperçu non pris en charge pour ce type de fichier"}, "GlobalInputs": {"addInput": "Ajouter une entrée", "cancelOrSave": "Annuler ou enregistrer avant d'ajouter de nouvelles entrées", "dataInput": "<PERSON><PERSON>", "date": "Date", "file": "<PERSON><PERSON><PERSON>", "newInput": "Nouvelle entrée", "text": "Texte"}, "Header": {"active": "Actifs", "activeAgentsRequire": "Les agents actifs nécessitent au moins une étape de révision humaine.", "archived": "Archivé", "draft": "Brouillon", "Errors": {"errorUpdatingWorkflow": "Erreur lors de la mise à jour du statut du workflow"}, "testRun": "Test de fonctionnement"}, "HeaderTitle": {"ariaLabels": {"cancelEditing": "Annuler la modification", "editWorkflowName": "Modifier le nom du flux de travail", "saveChanges": "Enregistrer les modifications"}, "errors": {"nameRequired": "Le nom ne peut pas être vide", "nameTooLong": "Le nom est trop long ({{max}} caractères maximum)"}, "toast": {"errorTitle": "<PERSON><PERSON><PERSON>", "invalidInputTitle": "En<PERSON><PERSON> invalide", "updateAgentError": "Échec de la mise à jour du nom de l'agent"}, "tooltip": {"editName": "Modifier le nom"}}, "InputArgumentValue": {"addInput": "Ajouter une entrée", "Errors": {"unsupportedInputType": "Type d'entrée non pris en charge"}, "selectDate": "Sélectionner une date", "textInput": "<PERSON><PERSON> de texte"}, "InputForm": {"builderInput": "En<PERSON><PERSON> du constructeur", "cancel": "Annuler", "date": "Date", "delete": "<PERSON><PERSON><PERSON><PERSON>", "description": "Description", "enterDescription": "Entrez une description", "enterTitle": "<PERSON><PERSON> un titre", "enterValue": "<PERSON><PERSON> une valeur", "file": "<PERSON><PERSON><PERSON>", "inputType": "Type d'entrée", "newInput": "Nouvelle entrée", "requestedFormat": "Format demandé", "save": "Enregistrer", "text": "Texte", "title": "Titre"}, "InputMessage": {"addInput": "Ajouter une entrée", "cancel": "Annuler", "draftMessage": "Message brouillon", "Errors": {"noIDsprovided": "Aucun identifiant fourni"}, "save": "Enregistrer", "selectDate": "Sélectionner une date", "textInput": "<PERSON><PERSON> de texte"}, "Inputs": {"Errors": {"noIDsprovided": "Aucun identifiant fourni"}}, "InputsDropDown": {"inputsCount": "Entrées ({{count}})"}, "InputsDropdown": {"addInputsPrevSteps": "Ajouter des entrées des étapes précédentes", "Errors": {"inputNotFound": "Entrée introuvable", "noDataReturned": "<PERSON><PERSON><PERSON> donn<PERSON> retour<PERSON>", "noDataReturnedCEI": "<PERSON><PERSON><PERSON> donn<PERSON> retournée depuis la création de l'exemple d'entrée", "noDataReturnedCTI": "Au<PERSON>ne donn<PERSON> retournée depuis l'entrée de création de tâche", "noDataReturnedSEIFV": "Aucune donnée retournée par SEIFV", "noIDsprovided": "Aucun identifiant fourni", "taskNotFound": "Tâche introuvable", "unexpectedWorkflowInput": "Entrée de flux de travail inattendue"}}, "InputSelectionForm": {"initialInputIntegrationDescription": "Se connecter à la plateforme de données", "initialInputIntegrationTitle": "Intégration", "initialInputUploadFileDescription": "Téléchargez excel, csv, etc. Ajoutez plus de détails", "initialInputUploadFileTitle": "Charger le fichier", "selectInitialInput": "Sélectionner l'entrée initiale", "thisWillEllipsis": "Cela va..."}, "InputsTabSection": {"addInput": "Ajouter une entrée", "currentOutput": "Sortie actuelle", "edit": "Modifier", "Errors": {"failedCreateInputs": "Erreur de données inattendue : Échec de la création de l'exemple d'entrée", "noDataOrErrors": "Erreur inattendue : aucune donnée ou erreur retournée"}, "open": "Ouvert", "stepOutput": "Étape {{index}} Sortie"}, "NavBar": {"backToApp": "Retour à l'application", "dashboard": "Tableau de bord", "teams": "Équipes"}, "NewTaskPopover": {"createStep": "<PERSON><PERSON>er une nouvelle étape", "description": "Description", "enterDesc": "<PERSON><PERSON> une description", "enterTitle": "<PERSON><PERSON> un titre", "selectStrat": "Sélectionnez une stratégie", "selectTool": "Sélectionner l'outil", "submit": "So<PERSON><PERSON><PERSON>", "title": "Titre"}, "ReviewStep": {"approveRun": "Approuver l'exécution", "rejectRun": "Rejeter l'exécution", "resumeRun": "Reprendre l'exécution"}, "Row": {"deleteAgent": "Supprimer l'agent", "edit": "Modifier", "editAgent": "Modifier l'agent", "oneHumanStep": "Une étape humaine est requise pour cet agent.", "preview": "<PERSON><PERSON><PERSON><PERSON>", "run": "<PERSON><PERSON><PERSON>", "showActivityLog": "Afficher le journal d'activité"}, "RunInputsWizard": {"addInputsToRunAgent": "Ajouter des entrées pour exécuter l'agent", "continue": "<PERSON><PERSON><PERSON>", "enterInput": "Entrer {{name}}", "enterNumber": "Saisir un numéro", "enterText": "<PERSON><PERSON> du texte", "finish": "<PERSON><PERSON><PERSON>", "stepOfInput": "Étape {{i}} sur {{inputs}}", "unsupportedInputType": "Type d'entrée non pris en charge", "uploadFile": "Charger le fichier", "uploadInput": "Télécharger {{name}}", "uploadInputFile": "Télécharger le fichier {{name}}"}, "Runner": {"allAgents": "Tous les agents"}, "RunnerHeader": {"activeAgentsRequire": "Les agents actifs nécessitent au moins une étape de révision humaine.", "activityLog": "Journal d'activité", "allAgents": "Tous les agents", "runAgent": "Exécuter l'agent"}, "RunnerPage": {"agentHasNoSteps": "L'agent n'a actuellement aucune étape.", "agentHasNoSteps2": "Veuillez naviguer vers le constructeur pour ajouter et publier des étapes pour cet agent.", "agentPreview": "Aperçu de l'agent", "agentPreview2": ": Examinez les étapes avant de procéder.", "Errors": {"badNetworkResponse": "La réponse du réseau n'était pas correcte", "inputError": "Erreur inattendue : Échec de la création de l'entrée d'exécution du flux de travail", "noDataFromWorkflowRun": "Erreur inattendue : Aucune donnée reçue suite à la création réussie de l'exécution du workflow", "noDataReturned": "<PERSON><PERSON><PERSON> donn<PERSON> retour<PERSON>", "noFileFoundForID": "<PERSON><PERSON>n fichier trouvé pour l'ID d'entrée : ", "noInputFoundForID": "Aucune entrée trouvée pour l'ID : ", "noRunData": "Aucune donnée d'exécution reçue", "noWorkflowID": "Aucun ID de flux de travail spécifié"}}, "ScriptsDropdown": {"selectStep": "Sélectionner l'étape"}, "ScriptsPage": {"Errors": {"failedToFetchScript": "Échec du chargement du script", "failedToSaveScript": "Échec de l'enregistrement du script"}, "scriptSaved": "Script enregistré", "workflow": "Flux de travail", "workflowScripts": "Scripts de flux de travail"}, "SettingsSideDrawer": {"close": "<PERSON><PERSON><PERSON>", "save": "Enregistrer", "saving": "Enregistrement", "settings": "Paramètres", "settingsError": "Erreur lors de l'enregistrement des modifications", "settingsSaved": "Modifications enregistrées avec succès"}, "SourceTreeView": {"noConnectionsAvailable": "Aucune connexion disponible", "search": "<PERSON><PERSON><PERSON>", "searchConnections": "Rechercher des connexions", "treeViewTitle": "Sources de données"}, "Spreadsheet": {"Errors": {"fileTypeUnsupported": "Type de fichier non pris en charge", "unexpectedFileError": "<PERSON><PERSON><PERSON> <PERSON><PERSON> inattendue"}}, "SQLEditor": {"checkSQLSyntax": "Vérifier la syntaxe SQL", "ensureInputsFilled": "Assurez-vous que les champs sont remplis", "enterSQLQuery": "Entrer une requête SQL", "Errors": {"badNetworkResponse": "La réponse du réseau n'était pas correcte", "emptyPrompt": "Invite vide", "enterSQLDescription": "Entrez la description SQL", "errorExecutingFlolake": "Erreur d'exécution de Flolake", "errorExecutingWorkflow": "Erreur lors de l'exécution du workflow", "errorHandlingInputs": "Gestion des erreurs d'entrée", "errorUpdatingTask": "Erreur lors de la mise à jour de la tâche", "generationFailed": "Échec de la génération", "generationFailedMessage": "Message d'échec de génération", "noDataReturnedCEI": "<PERSON><PERSON><PERSON> donn<PERSON> retournée depuis la création de l'exemple d'entrée", "noDataReturnedCTI": "Au<PERSON>ne donn<PERSON> retournée depuis l'entrée de création de tâche", "noDataReturnedCWI": "Au<PERSON>ne donn<PERSON> retournée depuis l'entrée de création de flux de travail", "noExampleOutputID": "Aucun ID d'exemple de sortie", "noIDsprovided": "Aucun identifiant fourni", "schemaError": "<PERSON><PERSON><PERSON>", "taskNotFound": "Tâche introuvable"}, "execute": "Exécuter", "generate": "<PERSON><PERSON><PERSON><PERSON>", "incompleteInput": "Entrée incomplète", "invalidSQLQuery": "Requête SQL invalide", "noSQLQuery": "Aucune requête SQL", "pleaseCheckQuery": "Veuillez vérifier votre requête", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sqlEditorPrompt": "Déc<PERSON>z les données que vous souhaitez récupérer, par ex. « Obtenir toutes les données de compte dans Coupa ».", "sqlEditorPromptLabel": "Invite à la génération SQL", "sqlEditorToggleLabel": "Mode IA", "sqlGenerated": "SQL généré", "sqlGeneratedSuccess": "SQL généré avec succès", "taskBeenRunSuccessfully": "La tâche a été exécutée avec succès", "taskRunFailed": "Échec de l'exécution de la tâche", "taskRunSuccess": "Exécution de la tâche réussie"}, "SQLEditorSplit": {"cancel": "Annuler", "chat": "Cha<PERSON>", "chatMSGTextArea": "Zone de texte pour les messages de chat", "checkSQLSyntax": "Veuillez vérifier votre syntaxe SQL", "copySQL": "Copier SQL", "copySQLErrorMessage": "<PERSON><PERSON><PERSON> le message d'erreur", "copyUserPrompt": "Co<PERSON>r l'invite", "editor": "<PERSON><PERSON><PERSON>", "Errors": {"generationFailed": "Échec de la génération", "generationFailedMessage": "Nous n'avons pas pu générer la requête SQL. Veuillez réessayer.", "invalidConnection": "Variable de connexion invalide", "noConvertedSQL": "Échec de la conversion SQL", "noConvertedSQLMessage": "Nous n'avons pas pu convertir la requête SQL. Veuillez réessayer."}, "executeLLMGeneratedSQL": "Exécuter l'instruction SQL", "generate": "<PERSON><PERSON><PERSON><PERSON>", "invalidSQLQuery": "Requête SQL invalide", "LlmFeedback": {"additionalFeedbackOptional": "Commentaires supplémentaires (facultatif) :", "feedbackSubmitted": "Commentaires soumis", "feedbackSubmittedFailed": "Échec de l'envoi des commentaires", "feedbackSubmittedFailedMessage": "Veuillez réessayer plus tard.", "feedbackSubmittedSuccess": "Merci pour vos commentaires !", "rateResponseBad": "Évaluez cette réponse comme mauvaise", "rateResponseGood": "Évaluez cette réponse comme bonne", "submitFeedback": "Soumettre un commentaire"}, "pleaseCheckQuery": "Veuillez vérifier votre requête et réessayer", "sqlEditor": "Éditeur SQL", "sqlGenerated": "SQL généré", "sqlGeneratedSuccess": "SQL a été généré avec succès", "submit": "So<PERSON><PERSON><PERSON>", "taskBeenRunSuccessfully": "Votre tâche a été exécutée avec succès.", "taskRunFailed": "Échec de l'exécution de la tâche", "taskRunSuccess": "Exécution de la tâche réussie", "typeCommand": "<PERSON><PERSON> une commande", "viewLLMDescription": "Voir la description du LLM"}, "StatusChangeDialog": {"activateOnly": "Activer uniquement", "activateTest": "Activer le test", "cancel": "Annuler", "changingFromTo": "Passage de {{fromStatus}} à {{toStatus}}", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteVersion": "Supprimer la version", "deleteVersionBody": "Cela supprimera définitivement cette version. Cette action ne peut pas être annulée.", "Errors": {"noIDsprovided": "Aucun workflowId, taskId ou exampleSetId fourni dans les paramètres", "taskNotDefined": "Tâche non définie"}, "statusChange1": "La rétrogradation de cette version en brouillon ne laissera aucune version active. Les exécutions ne pourront pas être effectuées sur cet agent tant qu'une version active n'est pas sélectionnée.", "statusChange2": "L'activation de cette version désactivera toutes les autres versions actives. Les exécutions seront effectuées en utilisant cette version.", "statusChange3": "Cette action désarchivera cette version et l'activera. Toutes les autres versions actives seront désactivées. Les exécutions seront effectuées en utilisant cette version.", "statusChange4": "<PERSON>la <PERSON>ira cette version en brouillon. Les exécutions n'utiliseront pas cette version sauf si elle est sélectionnée comme 'active'.", "statusChange5": "Cette action archivera cette version. Les versions archivées peuvent être restaurées à tout moment.", "statusChange6": "Cette action archivera cette version. Les versions archivées peuvent être restaurées à tout moment. Les exécutions ne peuvent pas être effectuées sur cet agent tant qu'une version active n'est pas sélectionnée.", "statusChange7": "Cette action désactivera cette version. Les exécutions ne seront pas effectuées avec cette version.", "statusChange8": "L'activation de cette version la définira comme version par défaut pour les futures exécutions de l'agent. Nous vous recommandons d'effectuer un test pour vous assurer que chaque étape fonctionne correctement avant de continuer."}, "Step": {"Errors": {"noDataReturnedFromTaskRun": "Erreur inattendue : aucune donnée retournée du journal d'exécution de la tâche"}, "viewErrorDetails": "Afficher les détails de l'erreur"}, "StepList": {"agentSteps": "Étapes de l'agent", "Errors": {"badNetworkResponse": "La réponse du réseau n'était pas correcte", "errorRunningWorkflow": "Erreur lors de l'exécution du workflow :", "requestError": "erre<PERSON> de requ<PERSON>"}, "followingStepsExecuted": "Les étapes suivantes seront exécutées lorsqu'un agent est lancé."}, "StepResults": {"results": "Résultats", "runBy": "<PERSON><PERSON><PERSON> par"}, "Steps": {"delete": "<PERSON><PERSON><PERSON><PERSON>", "Errors": {"noIDsprovided": "Aucun identifiant fourni"}, "view": "<PERSON><PERSON>"}, "StepsSidebarList": {"add": "Ajouter", "addStep": "Ajouter une étape", "blockType": "Type de bloc", "inputs": "Entrées"}, "TeamDropdownSelect": {"selectTeamMigrate": "Sélectionner l'équipe à migrer"}, "TeamMigrationModal": {"cancel": "Annuler", "migrate": "<PERSON><PERSON><PERSON>", "newCreatedBy": "Nouveau créé par", "newEntityID": "Nouvel ID d'entité", "selectTeamMigrate": "Sélectionner l'équipe à migrer"}, "TeamModal": {"cancel": "Annuler", "save": "Enregistrer", "teamExternalID": "ID externe de l'équipe", "teamName": "Nom de l'équipe"}, "TeamPage": {"additionalFunctionality": "Fonctionnalités supplémentaires", "APISwaggerUI": "Interface Swagger de l'API", "notFound": "Introuvable", "team": "Équipe"}, "UseCaseForm": {"addCustomPrompt": "Ajoutez une invite personnalisée à l'étape. <PERSON><PERSON> remplacera l'invite par défaut pour cette étape.", "custom": "Personnalisée", "templates": "<PERSON><PERSON><PERSON><PERSON>"}, "UserPage": {"actions": "Actions", "actionWillMoveUser": "Cette action déplacera l'utilisateur", "actionWillMoveYou": "Cette action vous déplacera", "cancel": "Annuler", "changeTeam": "Changer d'é<PERSON><PERSON>", "changeTeamAndLogOut": "Changer d'équipe et se déconnecter", "deleteUser": "Supprimer l'utilisateur", "emailedTempPassword": "Mot de passe temporaire envoyé par e-mail", "emailSent": "E-mail envoyé", "notFound": "Introuvable", "permanentlyDelete": "Supprimer définitivement", "permDeleteUser": "Supprimer définitivement l'utilisateur", "resendEmail": "Renvoyer l'e-mail", "roleColon": "Rôle : ", "roleUpdated": "<PERSON><PERSON>le mis à jour", "somethingWentWrong": "Un problème est survenu", "switchTeams": "Changer d'é<PERSON><PERSON>", "team": "Équipe", "teamColon": "Équipe :", "typeDelete": "<PERSON><PERSON>z Supprimer : ", "unableDeleteUser": "Impossible de supprimer l'utilisateur", "unableTempPassword": "Impossible de générer un mot de passe temporaire", "unableToUpdateUserRole": "Impossible de mettre à jour le rôle de l'utilisateur", "userDeleted": "Utilisateur supprimé", "userDeletedMessage": "Utilisateur supprimé avec succès", "userDeleteUndone": "Suppression d'utilisateur annulée", "userRoleUpdated": "Rôle d'utilisateur mis à jour", "userStatus": "Statut : {{status}}", "you": "Vous"}, "UsersTab": {"email": "E-mail", "getStartedInviting": "Commencer à inviter", "inviteByEmail": "Inviter par e-mail", "inviteMoreUsers": "Inviter plus d'utilisateurs", "inviteSent": "Invitation envoyée", "inviteUser": "Inviter l'utilisateur", "noOneHere": "<PERSON><PERSON> ici", "role": "R<PERSON><PERSON>", "sentInvitationTo": "Invitation envoyée à", "somethingWentWrong": "Un problème est survenu", "status": "Statut", "unableToInviteUser": "Impossible d'inviter l'utilisateur", "users": "Utilisateurs", "you": "Vous"}, "VersionCard": {"active": "Actifs", "cancel": "Annuler", "confirm": "Confirmer", "delete": "<PERSON><PERSON><PERSON><PERSON>", "editTitle": "Modifier le titre", "Errors": {"noIDsprovided": "Aucun workflowId, taskId ou exampleSetId fourni dans les paramètres pour la page Builder"}, "newVersionName": "Nouveau Nom de Version", "renameVersion": "Renommer la version", "setActive": "Définir comme actif", "view": "<PERSON><PERSON>"}, "VersionDropdown": {"Errors": {"noIDsprovided": "Aucun identifiant fourni"}}, "VersionOptionsDropdown": {"deleteVersion": "Supprimer la version", "Errors": {"noExamplePropsVOD": "Aucun exemple de props pour le menu déroulant des options de version"}, "newVersionName": "Nouveau Nom de Version", "renameVersion": "Renommer la version"}, "VersionSlideout": {"archived": "Archivé", "default": "<PERSON><PERSON> <PERSON><PERSON>", "developmentVer": "Version de développement", "draft": "Brouillon", "Errors": {"noIDsprovided": "Aucun identifiant fourni"}, "finalizedVersionDefault": "Version finale par défaut", "forWorkflowToRun": "Pour que le workflow fonctionne", "noActiveVersions": "Aucune version active", "notInUse": "Pas utilisé", "noWorkflowRunsCan": "Aucune exécution de flux de travail ne peut être effectuée", "published": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selected": "Sélectionné", "versionExecutedWorkflow": "Version du workflow exécuté", "versionOfStep": "Version de l'étape", "versions": "Versions", "versionsCanBeOTFS": "Les versions peuvent être sélectionnées à la volée"}, "VersionsTab": {"active": "Actifs", "draft": "Brouillon", "versionsTabSaved": "Enregistré"}, "WorkflowForm": {"advancedSetup": "Configuration avancée", "agentName": "Nom de l'agent", "description": "Description", "entity": "Entité", "Errors": {"agentNameRequired": "Le nom de l'agent est requis", "errorFetchingEntities": "Erreur lors de la récupération des entités"}, "loading": "Chargement...", "nameAgent": "Nommez votre agent", "noDataAvailable": "<PERSON><PERSON><PERSON> donnée disponible", "noEntityAvailable": "Aucune entité disponible", "optionalDescription": "Description facultative", "selectEntity": "Sélectionner l'entité"}, "WorkflowModals": {"agentDescriptionOptional": "Description de l'agent (facultatif)", "agentNamePlaceholder": "Espace réservé au nom de l'agent", "cancel": "Annuler", "createNewWorkflow": "Créer un nouveau flux de travail", "editAgent": "Modifier l'agent", "save": "Enregistrer"}, "WorkflowRunNotes": {"agentDescriptionOptional": "Description de l'agent (facultatif)", "cancel": "Annuler", "save": "Enregistrer"}, "WorkflowRuns": {"completed": "Complété", "failed": "Échec", "inProgress": "En cours", "rejected": "<PERSON><PERSON><PERSON>"}, "WorkflowRunTable": {"deleteRun": "Supprimer l'exécution", "editNotes": "Modifier les notes", "name": "Nom", "notYetRun": "n'a pas encore été exécuté", "runAgain": "Exécuter à nouveau", "runBy": "<PERSON><PERSON><PERSON> par", "startedAt": "<PERSON><PERSON><PERSON><PERSON>", "status": "Statut", "this": "Ce", "view": "<PERSON><PERSON>", "viewRunHistory": "Afficher l'historique d'exécution", "workFlowRunTableSubtitle": "Exécutez ce flux de travail pour voir les résultats"}, "WorkflowsTab": {"createdAt": "<PERSON><PERSON><PERSON>", "filterWorkflows": "Filtrer les flux de travail", "hasBeenMigrated": "a été migré", "ID": "Identifiant", "loadMore": "Charger plus", "migrate": "<PERSON><PERSON><PERSON>", "name": "Nom", "noWorkflowsFound": "Aucun flux de travail trouvé", "somethingWentWrong": "Un problème est survenu", "theWorkflow": "Le flux de travail", "unableToMigrateWorkflow": "Impossible de migrer le workflow", "viewScripts": "Voir les scripts", "workflowMigrated": "Workflow migré", "workflows": "Processus"}, "WorkflowTable": {"build": "Construire", "buildWorkflow": "Flux de travail de construction", "createdBy": "C<PERSON><PERSON> par", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteWorkflow": "Supprimer le flux de travail", "description": "Description", "edit": "Modifier", "editWorkflow": "Modifier le flux de travail", "lastModified": "Dernière modification", "lastRun": "Dernière exécution", "run": "<PERSON><PERSON><PERSON>", "runWorkflow": "Exécuter le workflow", "status": "Statut"}}}