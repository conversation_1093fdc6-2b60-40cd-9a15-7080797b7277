#!/bin/bash

echo "Building for NODE_ENV=$NODE_ENV"

echo "Set REACT_APP_NODE_ENV to $NODE_ENV"
export REACT_APP_NODE_ENV="$NODE_ENV"
export FAST_REFRESH=false

if [ -f .env.$NODE_ENV ];
then
    # Need to use .env.$NODE_ENV if exist
    echo "Building with .env.$NODE_ENV"
    env-cmd -f .env.$NODE_ENV webpack --config webpack.common.js
else
    # If .env.$NODE_ENV doesnt exist, use .env instead
    echo "Building with .env"
    env-cmd -f .env webpack --config webpack.common.js
fi
