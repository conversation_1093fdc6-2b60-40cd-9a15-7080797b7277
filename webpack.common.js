/* For a module at http://localhost:8080/dist/js/main.js,
 * this will set the webpack public path to be
 * http://localhost:8080/dist/js/
 */
require("systemjs-webpack-interop/auto-public-path");
const path = require("path");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const webpack = require("webpack");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const Dotenv = require("dotenv-webpack");
const { getExternals, getStandaloneImportMap } = require("./config-overrides/externals");
const { NODE_ENV, isStandalone } = require("./config-overrides/env");
const StandaloneSingleSpaPlugin = require("standalone-single-spa-webpack-plugin");
const TsconfigPathsWebpackPlugin = require("tsconfig-paths-webpack-plugin");
const { BundleAnalyzerPlugin } = require("webpack-bundle-analyzer");
const CompressionPlugin = require("compression-webpack-plugin");

module.exports = {
  target: "web",
  entry: "./src/single-spa-entry.jsx",
  externals: getExternals(),
  module: {
    rules: [
      {
        loader: "esbuild-loader",
        test: /\.[jt]sx?$/,
        options: {
          target: "es2015",
        },
      },
      {
        test: /\.(png|jpe?g|gif)$/i, // Match image file types
        type: "asset/resource",
      },
      {
        test: /\.svg$/i,
        type: "asset/inline",
      },
      {
        test: /\.css$/,
        sideEffects: true,
        use: [MiniCssExtractPlugin.loader, "css-loader"],
      },
      {
        test: /\.(woff|woff2|eot|ttf|otf)$/i,
        type: "asset/resource",
      },
    ],
  },
  output: {
    path: path.resolve(__dirname, "build"),
    publicPath: "/",
    libraryTarget: "system",
    filename: "main.hub.js",
  },
  plugins: [
    new MiniCssExtractPlugin({
      filename: "main.hub.css",
    }),
    new webpack.ProvidePlugin({
      React: "react",
    }),
    new HtmlWebpackPlugin({
      template: "./src/main.html",
      filename: "index.html",
      favicon: "./src/favicon.ico",
    }),
    // Note: This is mainly for the mock server. This will inline
    //  secrets into the code, so avoid using .env for any production-level
    //  secrets in the app code!
    new Dotenv({
      path: `./.env.${NODE_ENV}`, // e.g., .env.development or .env.production
    }),
    ...(process.env.ANALYZE_BUNDLE ? [new BundleAnalyzerPlugin()] : []),
    new CompressionPlugin({
      test: /\.(js|ts|jsx|tsx|css|html|svg)$/,
    }),
  ].concat(
    isStandalone
      ? new StandaloneSingleSpaPlugin({
          appOrParcelName: `@floqast/transform-client`,
          importMapUrl: new URL("https://services-fq1.floqast.engineering/import-map.json"),
          importMap: getStandaloneImportMap(),
        })
      : [],
  ),
  resolve: {
    plugins: [
      new TsconfigPathsWebpackPlugin({
        extensions: [".js", ".jsx", ".ts", ".tsx"],
      }),
    ],
    extensions: [".js", ".jsx", ".ts", ".tsx"],
    fallback: {
      url: require.resolve("url"),
    },
  },
  cache: {
    type: "filesystem",
    buildDependencies: {
      config: [__filename],
    },
  },
};
