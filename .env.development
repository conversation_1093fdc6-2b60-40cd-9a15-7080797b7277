REACT_APP_RUNTIME_MODE=standalone
REACT_APP_NODE_ENV=development
REACT_APP_CLOSE_API_HOSTNAME=https://$REACT_APP_NODE_ENV.floqast.engineering
REACT_APP_LAMBDA_PORT_NBR=9001
REACT_APP_PORT=${PORT}
REACT_APP_RUNTIME_MODE=injected
NODE_ENV=development
REACT_APP_STATIC_ASSET_URL=https://static-$REACT_APP_NODE_ENV.floqast.engineering/applications/transform-client


# Put the next line in .env.development.local (and/or uncomment it here)
# to run this client against a local nanofrontend named `template-nanofrontend` at port 7010.
# Otherwise it will use the template-nanofrontend deployed to your FQ environment (if any).
# 
REACT_APP_TEMPLATE_NANOFRONTEND_URL=http://localhost:7010
