// Ignore type imports since they don't work from flow-ui currently
declare module "@floqastinc/flow-ui_core";
declare module "@floqastinc/flow-ui_icons";
declare module "@floqastinc/flow-ui_icons/*";
declare module "@floqastinc/flow-ui_composite";
declare module "*.svg" {
  import * as React from "react";

  export const ReactComponent: React.FunctionComponent<
    React.SVGProps<SVGSVGElement> & { title?: string }
  >;

  const src: string;
  export default src;
}
