{"automation": [{"repo": "qe-transform-testing", "workflow-file-name": "run-tests.yaml", "client-payload": "{\"env\": \"automation\", \"jest_cli_options\": \"--grep @ui\" }", "publish-test-results": "true"}], "automation-all": [{"repo": "qe-transform-testing", "workflow-file-name": "run-tests.yaml", "client-payload": "{\"env\": \"automation\", \"jest_cli_options\": \"--grep @ui\" }", "publish-test-results": "true"}]}