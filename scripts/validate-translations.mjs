import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { execSync } from "child_process";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const VALID_KEY_PATTERNS = [
  /^components\.[A-Za-z0-9]+\.[A-Za-z0-9]+(\.[A-Za-z0-9]+)*$/, // components.ComponentName.key
];

function isValidTranslationKey(key) {
  return VALID_KEY_PATTERNS.some((pattern) => pattern.test(key));
}

function getUsedTranslationKeys() {
  try {
    const findFilesCommand = 'find src/ -type f \\( -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js" \\)';
    const files = execSync(findFilesCommand, { encoding: "utf8" }).trim().split('\n');
    
    const keys = new Set();

    files.forEach(file => {
      if (!file.trim()) return;
      
      try {
        const content = fs.readFileSync(file, 'utf8');
        
        const patterns = [
          // Standard pattern with double quotes: t("key")
          /t\("([^"]+)"\)/g,
          // Standard pattern with single quotes: t('key')
          /t\('([^']+)'\)/g,
          // Pattern with whitespace: t( "key" )
          /t\(\s*"([^"]+)"\s*\)/g,
          /t\(\s*'([^']+)'\s*\)/g,
          // Multiline pattern - handles cases where t( is on one line and "key" on another
          /t\(\s*\n\s*"([^"]+)"/g,
          /t\(\s*\n\s*'([^']+)'/g,
          // Patterns with interpolation parameters: t("key", { params })
          /t\("([^"]+)",\s*\{[^}]*\}/g,
          /t\('([^']+)',\s*\{[^}]*\}/g,
          // Multiline patterns with interpolation parameters
          /t\(\s*"([^"]+)",\s*\{[^}]*\}/g,
          /t\(\s*'([^']+)',\s*\{[^}]*\}/g,
          // Patterns where the interpolation object spans multiple lines
          /t\("([^"]+)",\s*\{[\s\S]*?\}/g,
          /t\('([^']+)',\s*\{[\s\S]*?\}/g,
        ];

        patterns.forEach(pattern => {
          let match;
          while ((match = pattern.exec(content)) !== null) {
            const key = match[1];
            if (isValidTranslationKey(key)) {
              keys.add(key);
            }
          }
        });
      } catch (error) {
        console.warn(`Warning: Could not read file ${file}:`, error.message);
      }
    });

    return Array.from(keys);
  } catch (error) {
    console.error("Error finding translation keys:", error.message);
    return [];
  }
}

function getTranslationKeys() {
  const translationFile = path.join(__dirname, "../src/locales/en/translation.json");
  const content = JSON.parse(fs.readFileSync(translationFile, "utf8"));

  function extractKeys(obj, prefix = "") {
    return Object.entries(obj).reduce((keys, [key, value]) => {
      const newKey = prefix ? `${prefix}.${key}` : key;
      if (typeof value === "object" && value !== null) {
        return [...keys, ...extractKeys(value, newKey)];
      }
      return [...keys, newKey];
    }, []);
  }

  return extractKeys(content);
}

function validateTranslations() {
  const usedKeys = getUsedTranslationKeys();
  const translationKeys = getTranslationKeys();

  const missingKeys = usedKeys.filter((key) => !translationKeys.includes(key));
  const unusedKeys = translationKeys.filter((key) => !usedKeys.includes(key));

  let hasErrors = false;

  if (missingKeys.length > 0) {
    console.error(
      `\n✖ npm run validate-translations:\n${missingKeys.length} missing translation keys:`,
    );
    missingKeys.forEach((key) => console.error(`  - ${key}`));
    hasErrors = true;
  }

  if (unusedKeys.length > 0) {
    console.error(
      `\n✖ npm run validate-translations:\n${unusedKeys.length} unused translation keys:`,
    );
    unusedKeys.forEach((key) => console.error(`  - ${key}`));
    hasErrors = true;
  }

  if (hasErrors) {
    process.exit(1);
  }

  console.log("All translation keys are valid!");
}

validateTranslations();
