# Variable Binding Implementation for FLOLAKE SQL Queries

## Overview

This implementation solves the variable binding issues for FLOLAKE SQL queries by:

1. **Variable Format**: Using `{$variableName}` format in the editor for readability
2. **Snowflake Conversion**: Converting to `:1`, `:2`, etc. with proper bindMapping when executing
3. **Unified Extensions**: Single extension system handling both variables and schema references

## Implementation Details

### 1. Variable Conversion Functions (`src/apps/BuilderV3/utils/conversionFunctions.ts`)

- `convertVariablesToSnowflake()`: Converts `{$variableName}` to `:1` and builds bindMapping array
- `convertSnowflakeToVariables()`: Reverse conversion for loading saved queries
- `convertTokenToSchema()`: Updated to skip variables (starting with `$`) during schema conversion

### 2. Unified Editor Extensions (`src/apps/BuilderV3/utils/editorExtensions.ts`)

- **Combined System**: Single extension handling both variables and schema references
- **Syntax Highlighting**: Green for variables `{$var}`, blue for schema `{SYSTEM}`
- **Smart Autocompletion**: Context-aware completion for variables vs schema
- **All Input Types**: Supports all task input types (TEXT, NUMBER, DATETIME, FILE, etc.)
- **No Conflicts**: Intelligent detection prevents completion conflicts

### 3. Updated SQLEditorSplit

- **Single Extension**: Uses `createCombinedReferenceExtensions()` for both types
- **Improved Loading**: Statements load even without task inputs (fixes empty input issue)
- **Proper Conversion Flow**:
  1. Schema tokens converted first (`{SYSTEM}` → `ACTUAL_SCHEMA`, skipping variables)
  2. Variables converted to Snowflake (`{$var}` → `:1`) with bindMapping
- **Load Existing Queries**: Converts saved Snowflake parameters back to variables

### 4. Fixed Tooltip System (`src/apps/BuilderV3/utils/tooltip.ts`)

- **Variable Awareness**: Skips variables during schema validation
- **No False Errors**: Variables like `{$TableName}` won't trigger "system not found" errors

### 5. Variable Insertion

- **Button Click**: Inserts `{$variableName}` format
- **Auto-completion**: Type `{$` to get variable suggestions
- **Schema Completion**: Type `{` to get schema suggestions
- **Task Input Tracking**: Each variable maps to a specific taskInputId

## Usage Flow

1. **Insert Variable**: Click + button or type `{$` to insert variables
2. **Insert Schema**: Type `{` to insert schema references
3. **Visual Feedback**: Variables appear green, schemas appear blue
4. **Execution**: Variables convert to `:1`, `:2` with bindMapping sent to backend
5. **Loading**: Saved queries convert back to `{$variableName}` format

## Key Benefits

- **User-Friendly**: Variables show actual names instead of `:1`
- **Type Safety**: Only valid task inputs and schemas suggested
- **Proper Binding**: Correct taskInputId mapping to backend
- **No Conflicts**: Smart context detection prevents completion conflicts
- **Flexible Usage**: Variables can represent table names, column values, or any SQL element
- **Always Loads**: Statements load even when no task inputs exist
- **Consolidated Code**: Single extension system reduces duplication

## Example

```sql
-- In Editor (User-Friendly):
SELECT * FROM {$TableName}
WHERE account_name = {$AccountName}
AND created_date > {$StartDate}
AND system_id IN (SELECT id FROM {NETSUITE - Production}.{ACCOUNTS})

-- Converted for Snowflake Execution:
SELECT * FROM :1
WHERE account_name = :2
AND created_date > :3
AND system_id IN (SELECT id FROM TLC_ACTUAL_SCHEMA_NAME.ACCOUNTS)

-- With bindMapping: ["taskInputTable123", "taskInputAccount456", "taskInputDate789"]
```

**Note**: Variables can be used anywhere in SQL - as table names, column values, etc. The bindMapping ensures proper parameter ordering for Snowflake execution.

This implementation provides a seamless user experience while maintaining proper backend integration.
