{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Chrome",
      "request": "launch",
      "type": "chrome",
      "url": "http://localhost:8005/transform/v3",
      "webRoot": "${workspaceFolder}"
    },
    {
      "name": "Run standalone",
      "command": "npm run start:proxy:all",
      "request": "launch",
      "type": "node-terminal"
    },
    {
      "name": "Run unit tests",
      "type": "node",
      "request": "launch",
      "env": { "CI": "true" },
      "runtimeExecutable": "${workspaceRoot}/node_modules/.bin/craco",
      "args": ["test", "--runInBand", "--no-cache"],
      "cwd": "${workspaceRoot}",
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    },
    {
      "name": "Run current test",
      "type": "node",
      "request": "launch",
      "env": { "CI": "true" },
      "runtimeExecutable": "${workspaceRoot}/node_modules/.bin/craco",
      "args": ["test", "${fileBasenameNoExtension}", "--runInBand", "--no-cache"],
      "cwd": "${workspaceRoot}",
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    },
    {
      "name": "Run injected",
      "type": "node",
      "request": "launch",
      "cwd": "${workspaceFolder}",
      "runtimeExecutable": "npm",
      "runtimeArgs": ["run-script", "start"],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    }
  ]
}
