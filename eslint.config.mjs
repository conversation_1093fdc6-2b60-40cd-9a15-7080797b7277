// @ts-check
import eslint from "@eslint/js";
import tseslint from "typescript-eslint";
import eslintConfigPrettier from "eslint-config-prettier/flat";
import i18next from "eslint-plugin-i18next";
import validateI18next from "@floqastinc/eslint-plugin-validate-i18next";
import eslintPluginImportX from "eslint-plugin-import-x";
import globals from "globals";
import { createTypeScriptImportResolver } from "eslint-import-resolver-typescript";
import react from "eslint-plugin-react";
import reactRefresh from "eslint-plugin-react-refresh";
import { configs as reactHooks } from "eslint-plugin-react-hooks";

const standardConf = {
  languageOptions: {
    globals: {
      ...globals.node,
      ...globals.serviceworker,
      ...globals.browser,
      ...globals.vitest,
    },
    parserOptions: {
      ecmaFeatures: {
        jsx: true,
      },
    },
  },
  ignores: [
    "**/node_modules/**",
    "**/build/**",
    "**/webpack.*.js",
    "**/__generated__/**",
    "src/apps/BuilderV3/app-components/**",
    "src/apps/BuilderV3/routes/**",
  ],
};
export default tseslint.config(
  {
    ...standardConf,
    extends: [
      eslint.configs.recommended,
      tseslint.configs.recommended,
      react.configs.flat.recommended,
      react.configs.flat["jsx-runtime"],
      reactRefresh.configs.recommended,
      reactHooks["recommended-latest"],
      eslintPluginImportX.flatConfigs.recommended,
      eslintPluginImportX.flatConfigs.typescript,
      eslintConfigPrettier,
    ],
    settings: {
      react: {
        version: "detect",
      },
      "import-x/resolver-next": [
        createTypeScriptImportResolver({
          alwaysTryTypes: true,
          project: "./tsconfig.json",
        }),
      ],
      "validate-i18next": {
        paths: [
          "./src/locales/en/translation.json",
          "./src/locales/fr/translation.json",
          "./src/locales/de/translation.json",
          "./src/locales/ja/translation.json",
        ],
        defaultLocale: "en",
      },
    },
    plugins: {
      "validate-i18next": validateI18next,
    },
    rules: {
      // Disable in favor of the @typescript-eslint version, per docs
      // https://typescript-eslint.io/rules/no-unused-vars/
      "no-unused-vars": "off",
      "@typescript-eslint/no-unused-vars": [
        "error",
        {
          varsIgnorePattern: "^_",
          argsIgnorePattern: "^_",
        },
      ],
      // This is enabled since some code uses `any` to type-check
      // correctly. The `any` gets correctly inferred.
      // TODO: Find an alternative to `any` that still typechecks correctly.
      "@typescript-eslint/no-explicit-any": "warn",
      "import-x/order": "error",
      "validate-i18next/validate-i18n-keys": "error",

      "no-restricted-imports": [
        "error",
        {
          paths: [
            {
              name: "i18next",
              importNames: ["t"],
              message: "Use 'src/utils/i18n.ts' instead of importing 't' from 'i18next' directly.",
            },
          ],
        },
      ],
    },
  },
  // Allow t import in the original wrapper file
  {
    files: ["src/utils/i18n.ts"],
    rules: {
      "no-restricted-imports": "off",
    },
  },
  // NOTE: @floqastinc/eslint-config-i18next is not eslint@9 compatible yet
  {
    plugins: {
      i18next: i18next,
    },
    rules: {
      "i18next/no-literal-string": [
        "error",
        {
          mode: "jsx-text-only",
          "jsx-attributes": {
            include: [/label$/i, /text$/i, /placeholder$/i],
          },
          callees: {
            exclude: [
              "requestPromise",
              "setPublicPath",
              "getAttribute",
              "_period.isAfter",
              "addEventListener",
              "addListener",
              "attach",
              "basename",
              "Blob",
              "call",
              "commit",
              "console.error",
              "console.log",
              "console.warn",
              "create",
              "createElement",
              "CustomEvent",
              "DELETE",
              "describe",
              "dispatch",
              "encodeURIComponent",
              "endsWith",
              "Error",
              "field",
              "filter",
              "format",
              "formatCurrency",
              "formatDate",
              "ga",
              "get",
              "GET",
              "getElementById",
              "getFileName",
              "getFixedT",
              "getFlagVaue",
              "getPropertyValue",
              "getRequestKeepAlive",
              "groupBy",
              "has",
              "history.push",
              "i18n(ext)?",
              "includes",
              "indexOf",
              "isRoleAuthorizedForAllPolicies",
              "it",
              "join",
              "keyBy",
              "lambdaRequest",
              "makeActionCreator",
              "mock",
              "moment",
              "navigate",
              "only",
              "openURL",
              "PATCH",
              "periodFromDate",
              "POST",
              "postMessage",
              "PUT",
              "querySelector",
              "readFile",
              "readFileSync",
              "removeEventListener",
              "removeListener",
              "removeTag",
              "replace",
              "request",
              "requestKeepAlive",
              "require",
              "resolve",
              "set",
              "setAttribute",
              "setHeader",
              "setItem",
              "setPublicPath",
              "setTag",
              "sortBy",
              "sortBySortOrder",
              "split",
              "startsWith",
              "subtract",
              "t",
              "toLocaleDateString",
              "toLocaleString",
              "TypeError",
              "useState",
              "watch",
            ],
          },
          "object-properties": {
            exclude: [
              "backgroundColor",
              "buttonClassName",
              "color",
              "fallbackLng",
              "height",
              "href",
              "key",
              "lambdaSystem",
              "left",
              "linkState",
              "lng",
              "location",
              "logLevel",
              "returnUrl",
              "sortColumn",
              "style",
              "system",
              "top",
              "type",
              "url",
              "viewType",
              "visibility",
              "width",
            ],
          },
          words: {
            exclude: [
              "transform-client",
              "data-qe-id",
              "flow-ui",
              "fq-ui",
              "GET",
              "lang",
              "POST",
              "PUT",
              "tr",
              "undefined",
              "url",
            ],
          },
          "should-validate-template": true,
        },
      ],
      "no-restricted-syntax": [
        "error",
        {
          message: "Please don't use toUpperCase. Consider refactoring if displayed in the UI",
          selector: 'MemberExpression > Identifier[name="toUpperCase"]',
        },
        {
          message: "Please don't use toLowerCase. Consider refactoring if displayed in the UI",
          selector: 'MemberExpression > Identifier[name="toLowerCase"]',
        },
        {
          message:
            "Please don't use sort with the default callback as it does not respect all locales. Instead, provide a callback that is locale aware. If needed and unrelated to translatable strings, simply ignore.",
          selector: "CallExpression[callee.property.name='sort'][arguments.length=0]",
        },
        {
          message:
            "Please don't use capitalize. The capitalize method on strings does not respect all locales. If needed and unrelated to translatable strings, simply ignore",
          selector: 'MemberExpression > Identifier[name="capitalize"]',
        },
      ],
    },
  },
);
