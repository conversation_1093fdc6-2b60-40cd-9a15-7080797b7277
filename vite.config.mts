/// <reference types="vitest" />
import react from "@vitejs/plugin-react";
import serve from "rollup-plugin-serve";
import { defineConfig } from "vite";
import EnvironmentPlugin from "vite-plugin-environment";
import vitePluginSingleSpa from "vite-plugin-single-spa";
import svgr from "vite-plugin-svgr";
import tsconfigPaths from "vite-tsconfig-paths";

export default defineConfig({
  define: {
    "process.env": {},
  },
  plugins: [
    svgr(),
    EnvironmentPlugin("all", { prefix: "REACT_APP_" }),
    EnvironmentPlugin({ X_LILJWTY_GATE: null }),
    react(),
    tsconfigPaths(),
    // NOTE: Since client-hub doesn't currently support Vite/TS, we can't
    // fully use vitePluginSingleSpa since the Typescript code will break
    // in injected mode.
    process.env.REACT_APP_RUNTIME_MODE === "injected"
      ? serve({
          contentBase: "./build",
          port: 8005,
          host: "localhost",
          headers: {
            "Access-Control-Allow-Origin": "*",
          },
          verbose: true,
        })
      : vitePluginSingleSpa({
          serverPort: 8005,
          spaEntryPoints: ["./src/single-spa-entry.jsx"],
        }),
  ],
  server: {
    proxy: {
      "/api": {
        target: "http://localhost:8080",
        rewrite: (path) => path.replace(/^\/api/, ""),
        secure: true,
        changeOrigin: true,
      },
    },
  },
  build: {
    lib: {
      entry: "./src/index.tsx",
      name: "@floqast/transform-client",
      fileName: (format) => {
        if (format === "umd") return "main.hub.js";
        return `main.hub.${format}.js`;
      },
      formats: ["es", "umd"],
    },
    manifest: true,
    minify: true, // does not minify whitespaces, see https://vitejs.dev/config/build-options.html#build-minify
    sourcemap: true,
    outDir: "./build",
    rollupOptions: {
      input: {
        index: "./src/index.tsx",
      },
      output: {
        dir: "./build",
        // If it works, it works. If it doesn't, it doesn't. That's the way it is.
        // JK, external variables that we are providing needs to be aliased in the vite's
        // rollup's external to work with MFEs webpack's externals
        paths: {
          "@floqastinc/transform-client": "@floqast/transform-client",
          react: "react-18",
          "react-dom": "react-dom-18",
          "react-is": "react-is-18",
        },
      },
      external: ["react", "react-dom", "react-is"],
    },
  },
  optimizeDeps: {
    include: ["@floqastinc/flow-ui_icons", "@floqastinc/fq-intl", "react-type-animation"],
  },
  test: {
    environment: "happy-dom",
    runner: "browser",
    setupFiles: "./vitest.setup.ts",
    include: ["src/**/*.test.ts", "src/**/*.test.tsx"],
    globals: true,
  },
});
