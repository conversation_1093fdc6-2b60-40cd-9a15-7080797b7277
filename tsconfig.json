{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": false, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./src/", "paths": {"@Admin/*": ["apps/Admin/*"], "@Auth/*": ["apps/Auth/*"], "@Internal/*": ["apps/Internal/*"], "@External/*": ["apps/External/*"], "@BuilderV2/*": ["apps/BuilderV2/*"], "@BuilderV3/*": ["apps/BuilderV3/*"], "@Transform/*": ["apps/Transform/*"], "@v0/*": ["api/__generated__/v0/hooks/*"], "@v3/*": ["api/__generated__/v3/hooks/*"], "@static/*": ["../static/*"], "@/*": ["./*"]}, "types": ["@vitest/browser/providers/playwright", "vitest/globals"]}, "include": ["src/**/*.*", "index.d.ts", "vitest.setup.ts"]}